{"name": "b2b", "version": "1.0.0", "scripts": {"dev": "vite --port 3005", "build": "vite build && cp -R dist/* ../saloplus_bo_pro/", "preview": "vite preview --port 4173"}, "dependencies": {"@vuepic/vue-datepicker": "^3.6.8", "accounting": "^0.4.1", "axios": "^1.6.8", "chart.js": "^3.9.1", "country-list-js": "^3.1.7", "jquery": "^3.6.1", "md5": "^2.3.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "numeral": "^2.0.6", "sass": "^1.54.4", "vue": "^3.2.37", "vue-cookies": "^1.8.1", "vue-loaders": "^4.1.4", "vue-loading-overlay": "^5.0.3", "vue-moment": "^4.1.0", "vue-pincode-input": "^0.4.0", "vue-router": "^4.1.3", "vue-select": "^4.0.0-beta.3", "vue-sweetalert2": "^5.0.5", "vue2-dropzone-vue3": "^1.1.0", "vuetable-2": "^2.0.0-beta.4", "vuex": "^4.0.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.8", "postcss": "8.4.38", "tailwindcss": "^3.1.8", "vite": "^6.0.3"}, "description": "This template should help get you started developing with Vue 3 in Vite.", "main": "postcss.config.js", "repository": {"type": "git", "url": "git+https://<EMAIL>/gichuwil/expresso-web.git"}, "author": "", "license": "ISC", "homepage": ""}