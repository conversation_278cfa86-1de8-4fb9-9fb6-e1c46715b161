import {createStore} from 'vuex'
import createPersistedState from "vuex-persistedstate"
import axios from "axios";
import router from "@/router";
import md5 from "md5";


const state = {
    isSideMenuOpen: true,
    merchant_account: null,
    loan_account: null,
    loan_number: null,
    request_number: null,
    loan_product: null,


    // old
    permission: null,
    role: null,
    isSuperRole: false,
    super_roles: [1, 8],
    permissions: [],
    base: import.meta.env.BASE_URL,
    api: import.meta.env.VITE_BASE_API,
    client_id: null,
    mc: null,
    client_mode: null,
    profile_info: null,
    alias_name: null,
    appKey: "4K7DN9O3OxZHWsapmnCkZTMvwAttZITx",
    tokenKey: null,
    authKey: "hjR1j33iKJ&-",
    authKeyWeb: "WEB_APP",
    hash_key: null,
    loggedUser: null,
    customer: null,

    /// old
    event: null,
    user_name: null,
    account: null,
    organizationAccount: null,
    msisdn: null,
    message: null,
    casino_game: null,
    data: null,
    roleData: null,
    addClient: false,
    showTransactionDetailsModal: false,
    changeAllExchangeRate: false,
    client_list: []
};
const getters = {
    //
    isSideMenuOpen: (state) => state.isSideMenuOpen,
    //
    isAuthenticated: state => !!state.tokenKey,
    StatePermissions: state => state.permissions,
    StateSuperRoles: state => state.super_roles,
    StateRole: state => state.role,
    StateApi: state => state.api,
    StateAppKey: state => state.appKey,
    StateTokenKey: state => state.tokenKey,
    StateAuthKey: state => state.authKey,
    StateHashKey: state => state.hash_key,
    StateAuthKeyWeb: state => state.authKeyWeb,
};
const actions = {

    toggleSideMenu({commit}) {
        commit("TOGGLE_SIDE_MENU");
    },

    //
    hasSuperRole({getters, commit}) {
        let ans = getters.StateSuperRoles.includes(getters.StateRole);
        commit("setIsSuperRole", ans)
        return ans
    },

    //
    async LogOut({commit}) {
        commit('setLogOut')
    },

    sortNestedArray(array) {
        array.sort();
        array.forEach((value, index) => {
            if (Array.isArray(value)) {
                array[index] = this.sortNestedArray(value);
            } else if (typeof value === 'object' && value !== null) {
                array[index] = this.sortNestedObject(value);
            } else {
                array[index] = value;
            }
        });
        return array;
    },
    sortNestedObject(obj) {
        const sortedObj = {};
        Object.keys(obj).sort().forEach(key => {
            const value = obj[key];
            if (Array.isArray(value)) {
                sortedObj[key] = this.sortNestedArray([...value]);
            } else if (typeof value === 'object' && value !== null) {
                sortedObj[key] = this.sortNestedObject(value);
            } else {
                sortedObj[key] = value;
            }
        });
        return sortedObj;
    },

// Basic implementation of the md5 hash function using the crypto module
    md5(input) {
        const crypto = require('crypto');
        return crypto.createHash('md5').update(input).digest('hex');
    },

    // hashCreate(request, tokenKey) {
    async HashCreate1({commit, getters}, request) {
        let hashkey = '';
        const sortedRequest = Object.keys(request).sort().reduce((obj, key) => {
            obj[key] = request[key];
            return obj;
        }, {});
        for (const [key, value] of Object.entries(sortedRequest)) {
            if (Array.isArray(value)) {
                const sortedValue = this.sortNestedArray([...value]);
                sortedValue.forEach((val, index) => {
                    hashkey += `&${index}=${md5(JSON.stringify(val))}`;
                });
                continue;
            } else if (typeof value === 'object' && value !== null) {
                const sortedValue = this.sortNestedObject(value)
                for (const [keyObj, valueObj] of Object.entries(sortedValue)) {
                    hashkey += `&${keyObj}=${md5(JSON.stringify(valueObj))}`;
                }
                continue
            }
            hashkey += `&${key}=${value}`;
        }

        hashkey = hashkey.substring(1);

        let key = md5(hashkey + getters.StateAppKey);

        await commit('setHashKey', key)
        console.log(`HASH is ${key}`);

        return hashkey;

    },


    async HashCreate({commit, getters}, request) {
        // console.log(`HashCreate payload --- ${JSON.stringify(request)}`);

        let hashKey = "";
        let sortedKeys = Object.keys(request).sort();

        for (let key of sortedKeys) {
            let value = request[key];

            // console.log(`Main Obj: ${key}`);
            if (Array.isArray(value) || typeof value === 'object') {
                if (Array.isArray(value)) {
                    // console.log(`Array.isArray:`, value);
                    for (let i = 0; i < value.length; i++) {
                        let val = value[i];
                        // console.log(`Am here with index: ${i} with Val: ${val} of JSON: ${JSON.stringify(val)}`);
                        hashKey += `&${i}=${md5(JSON.stringify(val))}`;
                        // console.log(`And hashKey: ${hashKey}`);
                    }
                } else {
                    // console.log(`Object:Hash`);
                    for (let keyVal in value) {
                        let val = value[keyVal];
                        // console.log(`Am here with keyVal: ${keyVal} with Val: ${val} of JSON: ${JSON.stringify(val)}`);
                        hashKey += `&${keyVal}=${md5(JSON.stringify(val))}`;
                        // console.log(`And hashKey: ${hashKey}`);
                    }
                }
                continue;
            }

            hashKey += `&${key}=${value}`;
        }

        // let hash = md5(hashKey.substring(1) + '' + tokenKey);
        // console.log(`HASH-HASH hashKey ${hashKey}`);

        let hash = hashKey.slice(1)
        // let md5 = require('md5');
        let key = md5(hash + getters.StateAppKey)
        await commit('setHashKey', key)
        // console.log(`HASH is ${key}`);

        return hash;
    },

    //------------- Auth ---------------------------------------------//
    async LogIn({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/user_login', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
            }
        })
            .then(async function (response) {
                // console.log("login success:: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data
                if (res.status === 200) {
                    if (response.data.data.data.token) {
                        await commit('setLoggedUser', response.data.data.data)
                        await commit('setAuth', response.data.data.data.token)
                        await commit('setRole', parseInt(atob(response.data.data.data.role_id)))
                    }

                    dispatch('hasSuperRole')
                    dispatch('getAccount', {limit: 1})

                }
            })
            .catch(async function (error) {
                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code
                }
            })

        return res
    },

    //
    async LogInCode({commit, getters}, payload) {
        let res = {}
        await axios.post(getters.StateApi + 'auth/v1/login', payload, {
            headers: {
                'X-Requested-With': "XMLHttpRequest",
                'X-Authorization-Key': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data
                if (res.status === 200) {
                    if (response.data.data.data.token) {
                        await commit('setAuth', response.data.data.data.token)
                        await commit('setPermission', response.data.data.data.permissions)
                        await commit('setRole', response.data.data.data.acl_id)
                        await commit('setClientId', response.data.data.data.c_id)
                        await commit('setMc', response.data.data.data.mc)
                        await commit('setClientMode', response.data.data.data.c_mode)
                        await commit('setAliasName', response.data.data.data.full_name)
                    }
                }
            })
            .catch(async function (error) {
                console.log('error: ' + JSON.stringify(error.response.data))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    //
    async passwordForgot({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/reset_password', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(async function (error) {
                console.log('error: ' + JSON.stringify(error.response.data))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    //
    async passwordReset({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/change_password', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(async function (error) {
                console.log('error: ' + JSON.stringify(error.response.data))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },


    //------------- Dash ---------------------------------------------//
    //
    async Stats({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        let url = new URLSearchParams(payload).toString();
        await axios.get(getters.StateApi + 'dashboard/v1/view/stats?' + url, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getLoanAccounts: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getUsers({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/users', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getUsers: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getSystemRoles({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/user_roles', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getSystemRoles: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === (401 || 203)) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async addUser({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/user_create', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("addUser success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log("addUser error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async updateUser({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/user_edit/' + payload.user_id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("updateUser success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log("updateUser error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    console.log("Ati setLogOut")
                    // commit('setLogOut')
                }
            })

        return res
    },

    async sendOtp({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/resend_otp', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("sendOtp success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log("sendOtp error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getConfigs({commit, getters, dispatch}, payload) {
        let res = {}
        let end_point = payload.end_point
        delete payload.end_point

        dispatch('HashCreate', payload)

        await axios.post(getters.StateApi + 'merchant/v1/view/' + end_point, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getConfigs: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async updateConfigs({commit, getters, dispatch}, payload) {
        let res = {}

        console.log("updateConfigs REQ1: ", JSON.stringify(payload))

        let end_point = payload.end_point
        delete payload.end_point
        console.log("updateConfigs REQ2: ", JSON.stringify(payload))


        dispatch('HashCreate', payload)

        await axios.post(getters.StateApi + 'merchant/v1/' + end_point, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("updateConfigs>>: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (res.status === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.message
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    // commit('setLogOut')
                }
            })

        return res
    },

    async updateChannels({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/set/channels', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("updateChannels: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async updateLoanAccount({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/edit_account', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("updateLoanAccount success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = response.data.data.message
                } else {
                    res.message = response.data.data.message
                }
            })
            .catch(function (error) {
                console.log("updateLoanAccount error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async validateAcc({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/validate/account', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("validateAcc success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = response.data.data.message
                } else {
                    res.message = response.data.data.data.data
                }
            })
            .catch(function (error) {
                console.log("validateAcc error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async resendTAN({commit, getters, dispatch}, payload) {
        let res = {}

        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/loan/resend_tan', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("resendTAN success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = response.data.data.message
                } else {
                    res.message = response.data.data.message
                }
            })
            .catch(function (error) {
                console.log("resendTAN error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async approveLoan({commit, getters, dispatch}, payload) {
        let res = {}
        let id = payload.id

        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/approve_loan/' + id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("approveLoan success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = response.data.data.message
                } else {
                    res.message = response.data.data.message
                }
            })
            .catch(function (error) {
                console.log("approveLoan error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async approveLimit({commit, getters, dispatch}, payload) {
        let res = {}
        let reference_id = payload.reference_id

        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/approve_limits/' + reference_id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("ApproveLimit success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = response.data.data.message
                } else {
                    res.message = response.data.data.message
                }
            })
            .catch(function (error) {
                console.log("approveLimit error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Loans stuff
    async searchLoanAccount({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/search/loan_accounts', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getLoanAccounts .then: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Loans stuff
    async getLoanAccounts({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/view/loan_accounts', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getLoanAccounts .then: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async addLoanAccount({commit, getters, dispatch}, payload) {
        let res = {}
        await dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/create_account/' + payload.id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("addLoanAccount success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = response.data.data.message
                } else {
                    res.message = response.data.data.data.data
                }
            })
            .catch(function (error) {
                console.log("addLoanAccount error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                // if (res.status === 401 || res.status === 403 || res.status === 406) {
                if (res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getCheckoffReport({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'checkoff/v1/report', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getLoanAccounts .then: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Loan - products
    async addLoanProduct({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/create/loan_product', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("addLoanProduct success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = response.data.data.message
                } else {
                    res.message = response.data.data.data.data
                }
            })
            .catch(function (error) {
                console.log("addLoanProduct error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },
    async getLoanProducts({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/view/loan_product', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getLoanProducts: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },
    async updateLoanProduct({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'products/v1/edit', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("updateLoanProduct success: " + JSON.stringify(response.data))

                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = response.data.data.message
                } else {
                    res.message = response.data.data.message
                }
            })
            .catch(function (error) {
                console.log("updateLoanAccount error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message
                console.log("updLoanAcc  error: " + JSON.stringify(res.message))

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getSystemProducts({commit, getters, dispatch}, payload) {
        let res = {}
        let url = new URLSearchParams(payload).toString();
        dispatch('HashCreate', payload)
        await axios.get(getters.StateApi + 'products/v1/list?' + url, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getSystemProducts: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = response.data.data.message
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    //
    async getSmsFilters({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/sms/filters', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getSmsFilters: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    //
    async sendBulkSMS({commit, getters, dispatch}, payload) {
        let res = {}
        await axios.post(getters.StateApi + 'merchant/v1/send/bulk_sms', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                console.log("sendBulkSMS: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 403 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    //
    async getClients({commit, getters}, payload) {
        let res = {}
        let url = new URLSearchParams(payload).toString();
        await axios.get(getters.StateApi + 'client/v1/view?' + url, {
            headers: {
                'X-Requested-With': "XMLHttpRequest",
                'X-App-Key': getters.StateAppKey,
                'X-Authorization-Key': getters.StateAuthKey,
                'X-Token-Key': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.data
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getGames({commit, getters}, payload) {
        let res = {}
        let url = new URLSearchParams(payload).toString();
        await axios.get(getters.StateApi + 'admin/v1/game/view?' + url, {
            headers: {
                'X-Requested-With': "XMLHttpRequest",
                'X-App-Key': getters.StateAppKey,
                'X-Authorization-Key': getters.StateAuthKey,
                'X-Token-Key': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.data
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getTrx({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/view/transactions', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getTrx: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getWithdrawals({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'bill_payment/v1/view/withdrawals', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("getWithdrawals: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getBillPayments({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'bill_payment/v1/view/utilities', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("getBillPayments: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getBillPaymentTransactions({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'bill_payment/v1/view/trxn', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("getBillPaymentTransactions: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log("getBillPaymentTransactions err:", JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getMerchants({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/view/companies', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getMerchants: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async addMerchant({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/create_account', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("addMerchant success: " + JSON.stringify(response.data))
                console.log("addMerchant status: " + res.status)
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log("addMerchant error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async updateMerchant({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/activate', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("updateMerchant success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log("updateMerchant error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async setMerchantLimit({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters
            .StateApi + 'merchant/v1/set_limits', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("setMerchantLimit success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log("setMerchantLimit error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async setMerchantFee({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/set/service_fee', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("setMerchantFee success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log("setMerchantFee error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async editMerchant({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/update_client', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("editMerchant success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log("editMerchant error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async deleteMerchant({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/delete_client', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("deleteMerchant success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log("deleteMerchant error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getLoanRequests({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/view/loan_request', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getLoanRequests: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log("KKK", JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getLimitRequests({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/view/limit_requests', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getLimitRequests: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log("getLimitRequests error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getLoanRepayments({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)

        // let id = payload.loan_number
        await axios.post(getters.StateApi + 'merchant/v1/view/loan_repayments', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getLoanRepayments: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log("getLimitRequests error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async confirmKYC({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/view/kyc/' + payload.loan_number, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("View KYC Uploads : " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async approveOrRejectKYC({commit, getters, dispatch}, payload) {
        let res = {}
        console.log("approveOrRejectKYC", payload)
        let id = payload.acc
        delete payload.acc;
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/kyc/approval/' + id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("Approve/Reject KYC Uploads : " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data.data
                }
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getAccount({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/view', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getAccount: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                //res.message = response.data.data.data.data
                await commit('setAccount', response.data.data.data)
            })
            .catch(function (error) {
                // console.log("getAccount: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getSystemPermissions({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)

        let queryString = new URLSearchParams(payload).toString();

        await axios.get(getters.StateApi + 'merchant/v1/permission/view?' + queryString, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.data
            })
            .catch(function (error) {
                // console.log(JSON.stringify("error:: ",error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getTransactions({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        let url = new URLSearchParams(payload).toString();
        await axios.get(getters.StateApi + 'client/v1/trxns?' + url, {
            headers: {
                'X-Requested-With': "XMLHttpRequest",
                'X-App-Key': getters.StateAppKey,
                'X-Authorization-Key': getters.StateAuthKey,
                'X-Token-Key': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.data
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async addSystemRole({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'merchant/v1/role/create', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async updateSystemRole({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)

        await axios.post(getters.StateApi + 'merchant/v1/role/update/' + payload.role_id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getSystemUsers({commit, getters, dispatch}, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        let url = new URLSearchParams(payload).toString();
        await axios.get(getters.StateApi + 'admin/v1/view?' + url, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.data
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async getUserLogs({commit, getters}, payload) {
        let res = {}
        let url = new URLSearchParams(payload).toString();
        await axios.get(getters.StateApi + 'auth/v1/logs?' + url, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },


    ///-------------------------------------------------------------------------///
    async fillUser({commit}, payload) {
        await commit('setUser', payload)
        return true
    }, async fillLoanNumber({commit}, payload) {
        await commit('setLoanNumber', payload)
        return true
    },

    async fillUserName({commit}, payload) {
        await commit('setUserName', payload)
        return true
    },

    async fillMessage({commit}, payload) {
        await commit('setMessage', payload)
        return true
    },

    async fillData({commit}, payload) {
        await commit('setData', payload)
        return true
    },

    async fillRole({commit}, payload) {
        await commit('setRoleData', payload)
        return true
    },

    async fillClientList({commit}, payload) {
        await commit('setClientList', payload)
        return true
    },

    async fillClientId({commit}, payload) {
        await commit('setClientId', payload)
        return true
    },

    async resendOTP({commit, getters}, profileId) {
        let res = {}
        await axios.get(getters.StateApi + 'admin/v1/resend/otp/' + profileId, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("login success" + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(async function (error) {
                console.log('error: ' + JSON.stringify(error.response.data))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async disableEnableBulk({commit, getters}, payload) {
        let res = {}
        await axios.get(getters.StateApi + 'admin/v1/bulk_user/' + payload.id + '/' + payload.status, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async blockUnblockUser({commit, getters}, payload) {
        let res = {}
        await axios.post(getters.StateApi + 'admin/v1/block_user/' + payload.id + '/' + payload.status, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async sendBulk({commit, getters}, payload) {
        let res = {}
        await axios.post(getters.StateApi + 'admin/v1/send/bulk', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access-Token': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                console.log("success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
                console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },


    async handleErrorResponse(fromFunc, error, commit) {
        const res = {
            status: error.response.status,
            message: []
        };

        switch (res.status) {
            case 401:
            case 403:
            case 406:
                // Clear message array for specific status codes
                break;
            default:
                // Set message from error response data
                res.message = error.response.data?.data?.message || [];
                break;
        }

        // Log error message
        console.log("From Func: ", fromFunc, " : ", JSON.stringify(res.message));

        // Logout user for specific status codes
        // if (res.status === 401 || res.status === 403 || res.status === 406) {
        if (res.status === 401) {
            commit('setLogOut');
        }
    },


    async fillLoanAcc({commit}, payload) {
        await commit('setLoanAccount', payload)
        return true
    },
    async fillLoanProduct({commit}, payload) {
        await commit('setLoanProduct', payload)
        return true
    },
    async fillPermissions({commit}, payload) {
        await commit('setPermissions', payload)
        return true
    },
    async fillMerchantAccount({commit}, payload) {
        await commit('setMerchantAccount', payload)
        return true
    },
    async fillOrgAcc({commit}, payload) {
        await commit('setOrgAcc', payload)
        return true
    },
    async fillMsisdn({commit}, msisdn) {
        await commit('setMsisdn', msisdn)
        return true
    },
    async fillCustomer({commit}, data) {
        await commit('setCustomer', data)
        // console.log("fillCustomer> : ",data, JSON.stringify(data))
        return true
    },
};

const mutations = {
    TOGGLE_SIDE_MENU(state) {
        state.isSideMenuOpen = !state.isSideMenuOpen;
    },

    setAuth(state, token) {
        state.tokenKey = token
    },
    setLoggedUser(state, data) {
        state.loggedUser = data
        // console.log("setLoggedUser: ", JSON.stringify(state.loggedUser))
    },
    setLoanNumber(state, data) {
        state.loan_number = data
        console.log("setLoanNumber: ", JSON.stringify(state.loan_number))
    },
    setLoanRequestNumber(state, data) {
        state.request_number = data
        console.log("setLoanRequestNumber: ", JSON.stringify(state.request_number))
    },
    setPermissions(state, data) {
        state.permissions = data
        // console.log("set permissions: ", JSON.stringify(state.permissions))
    },
    setHashKey(state, token) {
        state.hash_key = token
    },
    setLoanAccount(state, data) {
        state.loan_account = data
        // console.log("setLoanAccount", JSON.stringify(state.loan_account))
    },
    setLoanProduct(state, data) {
        // console.log("setLoanProduct", JSON.stringify(data))
        state.loan_product = data
    },
    setMerchantAccount(state, data) {
        // console.log("setMerchantAccount", JSON.stringify(data))
        state.merchant_account = data
    },
    setUser(state, data) {
        state.user = data
    },
    setSmsDesign(state, data) {
        state.sms_design = data
    },
    setMsisdn(state, msisdn) {
        state.msisdn = msisdn
    },
    setCustomer(state, data) {
        state.customer = data
    },
    setOrgAcc(state, data) {
        state.organizationAccount = data

        // console.log(`organizationAccount:`, JSON.stringify(state.organizationAccount))
    },


    ///
    setPermission(state, permission) {
        state.permission = permission
    },
    setRole(state, role) {
        state.role = role
        // console.log(`role??:`, role)
    },
    setIsSuperRole(state, role) {
        state.isSuperRole = role
        // console.log(`isSuperRole??:`, role)
    },
    setClientId(state, info) {
        state.client_id = info
    },
    setMc(state, info) {
        state.mc = info
    },
    setClientMode(state, info) {
        state.client_mode = info
    },
    setAliasName(state, alias_name) {
        state.alias_name = alias_name
    },
    setLogOut(state) {
        state.tokenKey = null
        router.push({name: 'login'});
    },
    setAccount(state, data) {
        state.account = data
    },
    setUserName(state, user) {
        state.user_name = user
        // console.log("Set UserName", state.user_name)
    },
    // setMsisdn(state, msisdn) {
    //     state.msisdn = msisdn
    // },
    // setCustomer(state, data) {
    //     state.customer = data
    // },
    setMessage(state, data) {
        state.message = data
    },
    setData(state, data) {
        state.data = data
    },
    setRoleData(state, data) {
        state.roleData = data
        // console.log("Set roleData", JSON.stringify(state.roleData))
    },
    setClientList(state, data) {
        state.client_list = data
    },
};

// Create a new store instance.
const store = createStore({
    state() {
        return state
    },
    mutations: mutations,
    actions: actions,
    getters: getters,
    plugins: [createPersistedState()]
})

export default store;
