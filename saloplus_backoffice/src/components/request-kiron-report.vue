<template>
  <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
  <div class="z-30 relative max-w-xl mt-24 bg-white mx-auto rounded-md">
    <div class="px-6 py-3 text-lg font-medium border-b">Select Date Range (UTC)</div>
    <div class="block px-6 py-3">

      <div class="grid grid-cols-1 gap-4 mb-4">
        <div class="block">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="block">
              <label class="text-xs mb-1 block ">Start Date</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="date" v-model="moreParams.start" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">End Date</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="date" v-model="moreParams.end" >
            </div>
          </div>
        </div>
      </div>
      <div class="gap-4 block text-sm text-right">
        <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md mx-2" @click="hideModal()">Cancel</button>
        <button v-if="!download.request && !download.loading" class="inline-block px-4 py-2 rounded-md bg-blue-500 text-white" @click.prevent="setDownload">Request Download</button>
        <button v-else-if="!download.request && download.loading" class="inline-block px-4 py-2 rounded-md bg-blue-500 text-white">Requesting...</button>
        <download
            class="inline-block bg-blue-500 rounded-md font-medium ml-2"
            :file-title="download.fileTitle"
            :headers="download.headers"
            :items="download.items"
            ref="downloadComponent"
            style="visibility: hidden"
        />
      </div>
    </div>
  </div>
</template>



<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import Download from "@/components/downloadCSV.vue";
import moment from "moment/moment";
export default {
  data() {
    return {
      loading: false,
      moreParams: {
        start: "",
        end: "",
      },
      download: { request: false, loading: false, headers: {}, items: [], fileTitle: '' }
    }
  },
  components: {
    Download,
    Loading
  },
  props: {
    item: Object
  },
  methods: {
    ...mapActions(['getKironReport']),
    hideModal() {
      this.$parent.show_kiron_report = false
    },
    async setDownload() {
      this.download.request = false
      this.download.loading = true
      this.moreParams.page = 1
      this.moreParams.export = 1
      this.moreParams.client_id = this.item.client_id
      this.moreParams.game_name = this.item.game_name
      let response = await this.getKironReport(this.moreParams)

      let data = response.message
      await this.initiateDownload()
      await this.setCompetitionStats(data.competitions)
      await this.setMarketStats(data.markets)
      await this.setGameVsBetCount(data.gameVsBetCount)
      await this.setBetCount([{singleBet: data.singleBet, multipleBet: data.multipleBet}])
      await this.setBetTotals([data.totalBet])
      await this.setWonBet([data.wonBet])
      await this.setLostBet([data.lostBet])
      this.download.loading = false
      this.download.request = true
      this.hideModal()
    },
    async setCompetitionStats(data){
      for (let i = 0; i < data.length; i++) {
        if (data[i].comp_name.includes("GermanPreRecFootballLeagueRound")){
          data[i].comp_name = 'German'
        } else if (data[i].comp_name.includes("FrenchPreRecFootballLeagueRound")){
          data[i].comp_name = 'French'
        } else if (data[i].comp_name.includes("SpanishFastLeagueFootballRound")){
          data[i].comp_name = 'Spanish'
        } else if (data[i].comp_name.includes("ItalyFastLeagueFootballRound")){
          data[i].comp_name = 'Italy'
        } else if (data[i].comp_name.includes("EnglishPreRecFootballLeagueRound")){
          data[i].comp_name = 'English'
        }
      }
      this.download.items = data
      this.download.headers = {
        comp_name: 'Competition',
        row_count: 'Bet Count'
      }
      this.download.fileTitle = `${this.item.client_name} ${this.item.game_name} Game [COMPETITION STATS] (${this.moreParams.start} to ${this.moreParams.end})`
      await this.initiateDownload()
    },
    async setMarketStats(data){
      this.download.items = data
      this.download.headers = {
        sub_type_id: 'Market Type',
        row_count: 'Bet Count'
      }
      this.download.fileTitle = `${this.item.client_name} ${this.item.game_name} Game [MARKET STATS] (${this.moreParams.start} to ${this.moreParams.end})`
      await this.initiateDownload()
    },
    async setGameVsBetCount(data){
      this.download.items = data
      this.download.headers = {
        total_games: 'No of Games',
        row_count: 'Bet Count'
      }
      this.download.fileTitle = `${this.item.client_name} ${this.item.game_name} Game [MULTIPLES BREAKDOWN] (${this.moreParams.start} to ${this.moreParams.end})`
      await this.initiateDownload()
    },
    async setBetCount(data){
      this.download.items = data
      this.download.headers = {
        singleBet: 'Multiple Bets',
        multipleBet: 'Single Bets'
      }
      this.download.fileTitle = `${this.item.client_name} ${this.item.game_name} Game [SINGLE VS MULTIPLE BET COUNT] (${this.moreParams.start} to ${this.moreParams.end})`
      await this.initiateDownload()
    },
    async setBetTotals(data){
      this.download.items = data
      this.download.headers = {
        total_odd: 'Total Odds',
        bet_amount: 'Total Stake',
        row_count: 'Total Bet Count'
      }
      this.download.fileTitle = `${this.item.client_name} ${this.item.game_name} Game [TOTAL BET COUNT] (${this.moreParams.start} to ${this.moreParams.end})`
      await this.initiateDownload()
    },
    async setWonBet(data){
      this.download.items = data
      this.download.headers = {
        won_stake: 'Won Stake',
        win_amount: 'Won Amount',
        won_odds: 'Won Odds',
        bet_count_won: 'Won Bet Count'
      }
      this.download.fileTitle = `${this.item.client_name} ${this.item.game_name} Game [WON BET COUNT] (${this.moreParams.start} to ${this.moreParams.end})`
      await this.initiateDownload()
    },
    async setLostBet(data){
      this.download.items = data
      this.download.headers = {
        lost_stake: 'Lost Stake',
        total_odd_lost: 'Lost Odds',
        bet_count_lost: 'Lost Bet Count'
      }
      this.download.fileTitle = `${this.item.client_name} ${this.item.game_name} Game [LOST BET COUNT] (${this.moreParams.start} to ${this.moreParams.end})`
      await this.initiateDownload()
    },

    async initiateDownload() {
      // Get a reference to the download component using the ref attribute
      const downloadComponent = this.$refs.downloadComponent;

      // Call the download() method of the download component
      downloadComponent.download();
    }
  }
}
</script>
