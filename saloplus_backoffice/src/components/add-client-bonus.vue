<template>
  <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
  <div class="z-30 relative max-w-xl mt-24 bg-white mx-auto rounded-md">
    <div class="px-6 py-3 text-lg font-medium border-b">Add Bonus</div>
    <div class="block px-6 py-3">

      <div class="grid grid-cols-1 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block">Bonus Type</label>
          <select class="w-full block px-2 text-xs py-2 border rounded-md outline-none" v-model="form.bonus_type">
            <option value="1">Multibet Bonus</option>
          </select>
        </div>
        <div class="block">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="block">
              <label class="text-xs mb-1 block ">Min Odd</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.bonus_data.minimum_odd" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Expiry Date(UTC)</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="datetime-local" v-model="form.expiry_date" >
            </div>
          </div>
        </div>
        <div class="text-xs mb-1 block">Selections</div>
        <div class="grid grid-cols-3 gap-4 mb-2" v-for="(selection,index) in form.bonus_data.selections">
          <div class="block">
            <label class="text-xs mb-1 block ">No of Games</label>
            <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selection.games">
          </div>
          <div class="block">
            <label class="text-xs mb-1 block ">Value X 100 (%)</label>
            <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selection.value">
          </div>
          <div class="block">
            <button v-if="form.bonus_data.selections.length > 1" class="inline-block px-4 py-2 mt-5 bg-red-600 text-white border rounded-md" @click.prevent="removeRow(index)">X</button>
            <button v-if="index === form.bonus_data.selections.length - 1" class="inline-block px-4 py-2 mt-5 bg-green-600 text-white border rounded-md" @click.prevent="addRow">+</button>
          </div>
        </div>
      </div>
      <div class="gap-4 block text-sm text-right">
        <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" @click="hideModal()">Cancel</button>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2" id="addMember" @click="createRow">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat> Add
        </button>
      </div>
    </div>
  </div>
</template>



<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
export default {
  data() {
    return {
      loading: false,
      form: {
        bonus_type: 1,
        bonus_data: {
          minimum_odd: 0,
          selections: [{ games: 0, value: 0}],
        },
        expiry_date: '',
        game_id: ''
      },
    }
  },
  components: {
    Loading
  },
  props: {
    item: Number
  },
  mounted() {
    this.form.game_id = this.item
  },
  methods: {
    ...mapActions(['createClientBonus']),
    hideModal() {
      this.$parent.show_add_bonus = false
    },

    addRow(){
      let item = { games: 0, value: 0}
      this.form.bonus_data.selections.push(item)
    },

    removeRow(index){
      this.form.bonus_data.selections.splice(index, 1)
    },

    async createRow() {
      let app = this

      if (app.form.bonus_data.minimum_odd < 1){
        app.$swal.fire('Error!', "Minimum odds cannot be less than 1", 'error')
        return
      }

      let error = false
      let error1 = false
      for (let i = 0; i < app.form.bonus_data.selections.length; i++) {
        if (parseInt(app.form.bonus_data.selections[i].games) < 1){
          error = true
          break
        }
        if (parseInt(app.form.bonus_data.selections[i].value) < 0){
          error1 = true
          break
        }
      }
      if (error){
        app.$swal.fire('Error!', "No of Games cannot be less than 1", 'error')
        return
      }
      if (error1){
        app.$swal.fire('Error!', "Value cannot be less than 0", 'error')
        return
      }

      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this create this bonus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, create!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.createClientBonus(payload)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.$swal.fire('Created!', result.value.message, 'success')
              app.hideModal()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },
  }
}
</script>
