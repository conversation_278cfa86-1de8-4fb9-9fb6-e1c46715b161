<template>
  <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
  <div class="z-30 relative max-w-xl mt-24 bg-white mx-auto rounded-md">
    <div class="px-6 py-3 text-lg font-medium border-b">Update Configs</div>
    <div class="block px-6 py-3">

      <div class="grid grid-cols-1 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Callback URL </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.callback_url" >
        </div>
        <div class="block">
          <div class="grid grid-cols-3 gap-4 mb-2">
            <div class="block">
              <label class="text-xs mb-1 block ">Home URL </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.home_url" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Top Up URL </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.topup_url" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Login URL </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.auth_url" >
            </div>
          </div>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block">Default Currency</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.default_currency" >
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Currency List (Separate them by a comma(,))</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.currency_list" >
        </div>
        <div class="block mb-4">
          <label class="text-xs mb-1 block ">Timezone</label>
          <select class="w-full block px-2 text-xs py-2 border rounded-md outline-none" v-model="form.timezone">
            <option v-for="item in timezones" :value="item">{{item}}</option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Tax Settings</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.tax_settings">
        </div>

      </div>
      <div class="gap-4 block text-sm text-right">
        <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" @click="hideModal()">Cancel</button>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2" id="addMember" @click="editRow">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat> Update
        </button>
      </div>
    </div>
  </div>
</template>



<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import moment from "moment-timezone";
export default {
  data() {
    return {
      loading: false,
      form: {
        default_currency: '',
        currency_list: '',
        callback_url: '',
        home_url: '',
        topup_url: '',
        client_id: '',
        tax_settings: '',
        auth_url: '',
        id: '',
        timezone: moment.tz.guess(true),
      },
      currencies: [{ text: 'KES', value: 'KES'},{ text: 'USD', value: 'USD'},{ text: 'NG', value: 'NG'}],
      timezones: moment.tz.names()
    }
  },
  components: {
    Loading
  },
  props: {
    item: Object
  },
  mounted() {
    this.form.default_currency = this.item.default_currency
    this.form.currency_list = this.item.currency_list
    this.form.callback_url = this.item.callback_url
    this.form.home_url = this.item.home_url
    this.form.topup_url = this.item.topup_url
    this.form.auth_url = this.item.auth_url
    this.form.tax_settings = this.item.tax_settings
    this.form.timezone = this.item.timezone
    this.form.client_id = this.item.id
    this.form.id = this.item.settings_id
  },
  methods: {
    ...mapActions(['updateClientSettings']),
    hideModal() {
      this.$parent.showUpdate = false
    },

    async editRow() {
      let app = this

      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this configs!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateClientSettings(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              app.hideModal()
              await app.$parent.getGames()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },
  }
}
</script>
