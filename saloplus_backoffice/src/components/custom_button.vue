<template>
  <div class="px-4">
    <!-- Conditionally render router-link or button based on whether link is passed -->
    <router-link
        v-if="link"
        :class="buttonClasses"
        :style="style"
        :to="link"
        @click.native="action"
    >
      {{ name }}
    </router-link>

    <button
        v-else
        :class="buttonClasses"
        :style="style"
        @click="action"
        @mouseover="() => $root.$emit('hover-color', hoverColor)"
    >
      {{ name }}
    </button>
  </div>
</template>

<script>
export default {
  props: {
    name: {
      type: String,
      required: true
    },
    width: {
      type: String,
      default: '100px'
    },
    color: {
      type: String,
      default: 'green'
    },
    link: {
      type: Object,
      default: null
    },
    action: {
      type: Function,
      default: () => {}
    },
    customClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    style() {
      return {
        width: this.width,
        backgroundColor: this.color,
        color: 'white',
      };
    },
    buttonClasses() {
      return `custom-button ${this.customClass}`;
    },
    hoverColor() {
      // Determine the hover color based on the base color
      const colorMap = {
        green: 'lightgreen', // This can be replaced with a shade from your color palette
        orange: '#FFA500', // You can adjust this as per your need
        blue: '#ADD8E6',
        // Add more colors as needed
      };
      return colorMap[this.color] || this.color; // Fallback to original color if not found
    }
  }
};
</script>

<style scoped>

.custom-button {
  height: 35px;
  display: inline-block;
  padding: 1px 20px;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  align-content: center;
  text-align: center;
  transition: background-color 0.3s ease, color 0.3s ease;
  background-color: rebeccapurple; /* Default color */
  color: white;
}

.custom-button:hover {
  background-color: lightgreen; /* Change this to the desired hover color */
  color: orange; /* Change the text color on hover */
}
</style>
