<template>
    <div class="loader-container"><div class="loader"></div></div>
</template>

<style scoped>
    .loader-container{
        padding: 1.5rem;
        width: 100%;
        min-height: 100px;
        height: 100%;
        text-align: center;
        position: absolute;
        top: 0; bottom: 0; left: 0; right: 0;
        background: rgba(255,255,255,.5);
        z-index: 1000000;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    .loader {
        border: 7px solid #ccc; /* Light grey */
        border-top: 7px solid #3498db; /* Blue */
        border-radius: 50%;
        vertical-align: middle;
        width: 50px;
        height: 50px;
        animation: spin 2s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>