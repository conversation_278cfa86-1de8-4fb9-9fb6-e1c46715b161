<template>
  <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
  <div class="z-30 relative max-w-xl mt-24 bg-white mx-auto rounded-md">
    <div class="px-6 py-3 text-lg font-medium border-b">Add Game</div>
    <div class="block px-6 py-3">

      <div class="grid grid-cols-1 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block">Game</label>
          <select class="w-full block px-2 text-xs py-2 border rounded-md outline-none" v-model="form.game_id">
            <option v-for="item in games" :value="item.game_id">{{item.game_name}}</option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Max Win </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.maximum_win" >
        </div>
        <div class="block">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="block">
              <label class="text-xs mb-1 block ">Min Stake </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.minimum_stake" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Max Stake </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.maximum_stake" >
            </div>
          </div>
        </div>
      </div>
      <div class="gap-4 block text-sm text-right">
        <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" @click="hideModal()">Cancel</button>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2" id="addMember" @click="addRow">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat> Add
        </button>
      </div>
    </div>
  </div>
</template>



<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
export default {
  data() {
    return {
      loading: false,
      form: {
        minimum_stake: '',
        maximum_stake: '',
        maximum_win: '',
        client_id: this.$store.state.client_id,
        game_id: ''
      },
      games: []
    }
  },
  components: {
    Loading
  },
  props: {
    item: Number
  },
  mounted() {
    this.form.client_id = this.item
    this.setGames()
  },
  methods: {
    ...mapActions(['createClientGames', 'getGames']),
    hideModal() {
      this.$parent.showAddGame = false
    },
    async setGames() {
      let app = this
      let payload = { page: 1, per_page: 100 }
      let response = await this.getGames(payload)
      if (response.status === 200){
        app.games = response.message
      }
    },

    async addRow() {
      let app = this

      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this add this game!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.createClientGames(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              app.hideModal()
              await app.$parent.getGames()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },
  }
}
</script>
