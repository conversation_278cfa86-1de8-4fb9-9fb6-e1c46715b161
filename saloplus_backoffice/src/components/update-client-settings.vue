<template>
  <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
  <div class="z-30 relative max-w-xl mt-24 bg-white mx-auto rounded-md">
    <div class="px-6 py-3 text-lg font-medium border-b">Update {{item.client_name}} Settings</div>
    <div class="block px-6 py-3">

      <div class="grid grid-cols-1 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block">Mode</label>
          <select class="w-full block px-2 text-xs py-2 border rounded-md outline-none" v-model="form.client_mode">
            <option v-for="item in modes" :value="item.value">{{item.text}}</option>
          </select>
        </div>
        <div class="block" v-if="form.client_mode === 0">
          <label class="text-xs mb-1 block ">Feeds URL </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.kiron_fetch_url" >
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Callback URL </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.callback_url" >
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Logo</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.logo" >
        </div>
        <div class="block">
          <div class="grid grid-cols-2 gap-4 mb-2">
            <div class="block">
              <label class="text-xs mb-1 block ">Home URL </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.home_url" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Top Up URL </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.topup_url" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Login URL </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.auth_url" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block">With Menu Bar</label>
              <select class="w-full block px-2 text-xs py-2 border rounded-md outline-none" v-model="form.with_top_bar">
                <option v-for="item in topbar" :value="item.value">{{item.text}}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Currency List (Separate them by a comma(,))</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.currency_list" >
        </div>
        <div class="block">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="block">
              <label class="text-xs mb-1 block">Default Currency</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.default_currency" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Revenue Rate(%) </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.revenue_rate" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Tax On Stake(%) </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.ExeciseTax" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Tax On Winnings(%) </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.WitholdingTax" >
            </div>
          </div>
        </div>
      </div>
      <div class="gap-4 block text-sm text-right">
        <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" @click="hideModal()">Cancel</button>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2" id="addMember" @click="editRow">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat> Update
        </button>
      </div>
    </div>
  </div>
</template>



<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
export default {
  data() {
    return {
      loading: false,
      form: {
        default_currency: '',
        currency_list: '',
        callback_url: '',
        kiron_fetch_url: '',
        home_url: '',
        topup_url: '',
        auth_url: '',
        revenue_rate: '',
        cron_timer: '',
        client_mode: '',
        with_top_bar: '',
        client_id: '',
        ExeciseTax: '',
        WitholdingTax: '',
        logo: '',
        id: ''
      },
      currencies: [
        { text: 'KES', value: 'KES'},
        { text: 'USD', value: 'USD'},
        { text: 'UGX', value: 'UGX'},
        { text: 'NG', value: 'NG'}
      ],
      modes: [{ text: 'Shared', value: 1},{ text: 'Dedicated', value: 0}],
      topbar: [{ text: 'With Menu Bar', value: 1},{ text: 'Without Menu Bar', value: 0}],
    }
  },
  components: {
    Loading
  },
  props: {
    item: Object
  },
  mounted() {
    let app = this
    this.form.default_currency = this.item.default_currency
    this.form.currency_list = this.item.currency_list
    this.form.callback_url = this.item.callback_url
    this.form.kiron_fetch_url = this.item.kiron_fetch_url
    this.form.auth_url = this.item.auth_url
    this.form.topup_url = this.item.topup_url
    this.form.home_url = this.item.home_url
    this.form.revenue_rate = this.item.revenue_rate
    this.form.cron_timer = this.item.cron_timer
    this.form.client_mode = parseInt(this.item.client_mode)
    this.form.with_top_bar = parseInt(this.item.with_top_bar)
    this.form.client_id = this.item.id
    this.form.logo = this.item.logo
    this.form.id = this.item.settings_id

    if (this.item.tax_settings){
      let tax = JSON.parse(this.item.tax_settings)
      for (let i = 0; i < tax.length; i++) {
        if (tax[i].name === 'ExeciseTax'){
          app.form.ExeciseTax = parseFloat(tax[i].value)
        } else if (tax[i].name === 'WitholdingTax'){
          app.form.WitholdingTax = parseFloat(tax[i].value)
        }
      }
    }
  },
  methods: {
    ...mapActions(['updateClientSettings']),
    hideModal() {
      this.$parent.showUpdate = false
    },

    async editRow() {
      let app = this

      let payload = this.form
      if (this.form.ExeciseTax || this.form.WitholdingTax) {
        payload.tax_settings = [
          {name: "ExeciseTax", value: app.form.ExeciseTax},
          {name: "WitholdingTax", value: app.form.WitholdingTax}
        ]
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this client settings!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateClientSettings(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              app.hideModal()
              await app.$parent.getGames()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },
  }
}
</script>
