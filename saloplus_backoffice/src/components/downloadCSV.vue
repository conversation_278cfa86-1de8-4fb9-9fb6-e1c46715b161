<script>
export default {
  name: 'download',
  props: {
    headers: Object,
    items: Array,
    fileTitle: String
  },
  methods: {
    convertToCSV(objArray) {
      let array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
      let str = '';

      for (let i = 0; i < array.length; i++) {
        let line = '';
        for (const index in array[i]) {
          if (line !== '') line += ','

          line += array[i][index];
        }

        str += line + '\r\n';
      }

      return str;
    },
    exportCSVFile(headers, items, fileTitle) {
      if (headers) {
        items.unshift(headers);
      }

      // Convert Object to JSON
      let jsonObject = JSON.stringify(items);

      let csv = this.convertToCSV(jsonObject);

      let exportedFilenmae = fileTitle + '.csv' || 'export.csv';

      let blob = new Blob([csv], {type: 'text/csv;charset=utf-8;'});
      if (navigator.msSaveBlob) { // IE 10+
        navigator.msSaveBlob(blob, exportedFilenmae);
      } else {
        let link = document.createElement("a");
        if (link.download !== undefined) { // feature detection
          // Browsers that support HTML5 download attribute
          let url = URL.createObjectURL(blob);
          link.setAttribute("href", url);
          link.setAttribute("download", exportedFilenmae);
          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      }
    },
    download(){
      console.log("itemsNotFormatted",this.items)
      let itemsNotFormatted = this.items
      if (itemsNotFormatted.length < 1){
        return
      }

      let itemsFormatted = [];

      // format the data
      itemsNotFormatted.forEach((item) => {
        let formattedItem = {};
        for (const key in this.headers) {
          if (item.hasOwnProperty(key)) {
            formattedItem[key] = item[key].toString().replace(/,/g, '');
          } else {
            formattedItem[key] = '';
          }
        }
        itemsFormatted.push(formattedItem);
      });

      this.exportCSVFile(this.headers, itemsFormatted, this.fileTitle); // call the exportCSVFile() function to process the JSON and trigger the download
    }
  }
}
</script>

<template>
  <div>
    <button class="inline-block px-4 py-2 rounded-md bg-primary font-bold" @click.prevent="download">Download</button>
  </div>
</template>

<style scoped>

</style>