<template>
  <button
    :type="type"
    :disabled="disabled"
    :class="buttonClasses"
    @click="handleClick"
    :title="tooltip"
  >
    <!-- Icon Slot -->
    <slot name="icon">
      <!-- Default Icons based on variant -->
      <i v-if="defaultIcon" :class="defaultIcon"></i>
    </slot>
    
    <!-- Text (optional) -->
    <span v-if="$slots.default || text" :class="textClasses">
      <slot>{{ text }}</slot>
    </span>
  </button>
</template>

<script>
export default {
  props: {
    variant: {
      type: String,
      default: 'primary',
      validator: (value) => [
        'primary', 'secondary', 'success', 'danger', 'warning', 'info', 
        'edit', 'delete', 'view', 'approve', 'reject', 'add', 'download', 
        'settings', 'users', 'transactions'
      ].includes(value)
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['xs', 'sm', 'md', 'lg'].includes(value)
    },
    shape: {
      type: String,
      default: 'round',
      validator: (value) => ['round', 'square', 'rounded'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'button',
      validator: (value) => ['button', 'submit', 'reset'].includes(value)
    },
    text: {
      type: String,
      default: ''
    },
    tooltip: {
      type: String,
      default: ''
    },
    iconOnly: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    // Default icons for different variants using Font Awesome classes
    defaultIcon() {
      const iconMap = {
        edit: 'fa fa-edit',
        delete: 'fa fa-trash',
        view: 'fa fa-eye',
        approve: 'fa fa-check',
        reject: 'fa fa-times',
        add: 'fa fa-plus',
        download: 'fa fa-download',
        settings: 'fa fa-cog',
        users: 'fa fa-users',
        transactions: 'fa fa-money-bill',
        primary: null,
        secondary: null,
        success: 'fa fa-check',
        danger: 'fa fa-times',
        warning: null,
        info: null
      }
      return iconMap[this.variant]
    },

    // Size classes
    sizeClasses() {
      const sizes = {
        xs: 'h-6 w-6 text-xs',
        sm: 'h-8 w-8 text-sm',
        md: 'h-10 w-10 text-base',
        lg: 'h-12 w-12 text-lg'
      }
      return sizes[this.size]
    },

    // Shape classes
    shapeClasses() {
      const shapes = {
        round: 'rounded-full',
        square: 'rounded-none',
        rounded: 'rounded-lg'
      }
      return shapes[this.shape]
    },

    // Variant color classes
    variantClasses() {
      const variants = {
        primary: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700',
        secondary: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-700',
        success: 'bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700',
        danger: 'bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700',
        warning: 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600 hover:border-yellow-700',
        info: 'bg-cyan-600 hover:bg-cyan-700 text-white border-cyan-600 hover:border-cyan-700',
        edit: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700',
        delete: 'bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700',
        view: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-700',
        approve: 'bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700',
        reject: 'bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700',
        add: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700',
        download: 'bg-indigo-600 hover:bg-indigo-700 text-white border-indigo-600 hover:border-indigo-700',
        settings: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-700',
        users: 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600 hover:border-purple-700',
        transactions: 'bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700'
      }
      return variants[this.variant]
    },

    buttonClasses() {
      return [
        'inline-flex items-center justify-center border font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
        this.sizeClasses,
        this.shapeClasses,
        this.variantClasses,
        {
          'opacity-50 cursor-not-allowed': this.disabled || this.loading,
          'hover:shadow-lg transform hover:scale-105': !this.disabled && !this.loading,
          'gap-2': !this.iconOnly && (this.text || this.defaultIcon)
        }
      ]
    },

    textClasses() {
      return [
        'font-medium',
        {
          'sr-only': this.iconOnly
        }
      ]
    }
  },

  methods: {
    handleClick(event) {
      if (!this.disabled && !this.loading) {
        this.$emit('click', event)
      }
    }
  }
}
</script>

<style scoped>
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
