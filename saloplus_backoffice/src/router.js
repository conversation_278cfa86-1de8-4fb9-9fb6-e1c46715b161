import {createRouter, createWebHistory} from 'vue-router'
import store from "@/store";

import App from './app/index.vue'
import Login from './views/auth/login.vue'
import ForgotPassword from './views/auth/forgot.vue'
import Verify from './views/auth/verify.vue'

import Dashboard from './views/dashboard/dashboard.vue'

import LoanProducts from "@/views/loan/products/index.vue";
import LoanProductsAdd from "@/views/loan/products/add.vue";
import LoanProductsEdit from "@/views/loan/products/edit.vue";

import LoanAccounts from "@/views/loan/accounts/index.vue";
import LoanAccountsAdd from "@/views/loan/accounts/add.vue";
import LoanAccountsEdit from "@/views/loan/accounts/edit.vue";
import LoanRepayment from "@/views/loan/repayments/index.vue";

import LoanRequests from "@/views/loan/requests.vue";
import LoanLimits from "@/views/loan/limits.vue";
import LoanCheckOff from "@/views/loan/check-off.vue";

import Organisations from "@/views/organization/index.vue";
import OrganisationsAdd from "@/views/organization/add.vue";
import OrganisationsConfig from "@/views/organization/config.vue";
import OrganisationsBulk from "@/views/organization/send_bulk.vue";

import Transactions from '@/views/transactions/transactions.vue'

import Customers from '@/views/customer_search/customers.vue'

import Withdrawals from '@/views/withdrawals/index.vue'

import SystemUsers from '@/views/system/users/index.vue'
import SystemUsersAdd from '@/views/system/users/add.vue'
import SystemUsersEdit from '@/views/system/users/edit.vue'

import BillPayments from '@/views/bill_payments/index.vue'
import BillPaymentsAdd from '@/views/bill_payments/add.vue'
import BillPaymentsEdit from '@/views/bill_payments/edit.vue'

import Roles from '@/views/system/roles/index.vue'
import RolesAdd from '@/views/system/roles/add.vue'
import RolesEdit from '@/views/system/roles/edit.vue'
import Permissions from '@/views/system/roles/permissions.vue'

import Logs from '@/junk/clients/logs.vue'


const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {path: '/', name: 'login', component: Login, meta: {guest: true}},
        {path: '/forgot', name: 'forgot-password', component: ForgotPassword, meta: {guest: true}},
        {path: '/verify', name: 'verify', component: Verify, meta: {guest: true}},
        {
            path: '/app', component: App, children: [
                {path: '', name: 'dashboard', component: Dashboard, meta: {requiresAuth: true}},

                /// Loans
                {
                    path: 'requests/:client_id(\\d+)?',
                    name: 'requests',
                    component: LoanRequests,
                    meta: {requiresAuth: true}
                },
                {path: 'limits/:client_id(\\d+)?', name: 'limits', component: LoanLimits, meta: {requiresAuth: true}},
                {
                    path: 'check-off/:client_id(\\d+)?',
                    name: 'check-off',
                    component: LoanCheckOff,
                    meta: {requiresAuth: true}
                },
                // Accounts
                {
                    path: 'loan-accounts/:client_id(\\d+)?',
                    name: 'loan-accounts',
                    component: LoanAccounts,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'loan-accounts/add/:client_id(\\d+)?',
                    name: 'loan-accounts-add',
                    component: LoanAccountsAdd,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'loan-accounts/edit',
                    name: 'loan-accounts-edit',
                    component: LoanAccountsEdit,
                    meta: {requiresAuth: true}
                },
                // Repayments
                {
                    path: 'loan-repayments/:client_id(\\d+)?',
                    name: 'loan-repayments',
                    component: LoanRepayment,
                    meta: {requiresAuth: true}
                },
                // Products
                {
                    path: 'loan-products/:client_id(\\d+)?',
                    name: 'loan-products',
                    component: LoanProducts,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'loan-products/add/:client_id(\\d+)?',
                    name: 'loan-products-add',
                    component: LoanProductsAdd,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'loan-products/edit',
                    name: 'loan-products-edit',
                    component: LoanProductsEdit,
                    meta: {requiresAuth: true}
                },

                /// Organisations
                {path: 'organisations', name: 'organisations', component: Organisations, meta: {requiresAuth: true}},
                {
                    path: 'organisations/add',
                    name: 'organisations-add',
                    component: OrganisationsAdd,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'organisations/config',
                    name: 'organisations-config',
                    component: OrganisationsConfig,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'organisations/bulk',
                    name: 'organisations-bulk',
                    component: OrganisationsBulk,
                    meta: {requiresAuth: true}
                },

                ///Transactions
                {
                    // path: 'transactions/:id(\\d+)?/:loan_number?',
                    path: 'transactions/:client_id(\\d+)?',
                    name: 'transactions',
                    component: Transactions,
                    meta: {requiresAuth: true}
                },

                ///Customers
                {path: 'customers/:phone(\\d+)?', name: 'customers', component: Customers, meta: {requiresAuth: true}},

                ///Withdrawal
                {path: 'withdrawals', name: 'withdrawals', component: Withdrawals, meta: {requiresAuth: true}},


                /// Bill Payments
                {
                    path: 'bill-payments/:id?',
                    name: 'bill-payments',
                    component: BillPayments,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'bill-payments/add',
                    name: 'bill-payments-add',
                    component: BillPaymentsAdd,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'bill-payments/edit',
                    name: 'bill-payments-edit',
                    component: BillPaymentsEdit,
                    meta: {requiresAuth: true}
                },


                ///System
                {path: 'system/users/:id?', name: 'system-users', component: SystemUsers, meta: {requiresAuth: true}},
                {
                    path: 'system/users/:client_id(\\d+)?',
                    name: 'system-users',
                    component: SystemUsers,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'system-users/add/:client_id(\\d+)?',
                    name: 'system-users-add',
                    component: SystemUsersAdd,
                    meta: {requiresAuth: true}
                },
                {
                    path: 'system-users/edit',
                    name: 'system-users-edit',
                    component: SystemUsersEdit,
                    meta: {requiresAuth: true}
                },

                {path: 'system/roles', name: 'system-roles', component: Roles, meta: {requiresAuth: true}},
                {path: 'system-roles/add', name: 'system-roles-add', component: RolesAdd, meta: {requiresAuth: true}},
                {
                    path: 'system-roles/edit',
                    name: 'system-roles-edit',
                    component: RolesEdit,
                    meta: {requiresAuth: true}
                },

                {
                    path: 'system-permissions',
                    name: 'system-permissions',
                    component: Permissions,
                    meta: {requiresAuth: true}
                },

                {path: 'logs', name: 'logs', component: Logs, meta: {requiresAuth: true}},
            ]
        }
    ]
})

router.beforeEach((to, from, next) => {
    if (to.matched.some((record) => record.meta.requiresAuth)) {
        if (store.getters.isAuthenticated) {
            next();
            return;
        }
        next("/");
    } else {
        next();
    }
});

router.beforeEach((to, from, next) => {
    if (to.matched.some((record) => record.meta.guest)) {
        if (store.getters.isAuthenticated) {
            next("/app/");
            return;
        }
        next();
    } else {
        next();
    }
});
export default router
