<template>
<canvas id="myChart" width="800" height="400"></canvas>
</template>

<script>
import Chart from "chart.js/auto"
import Swal from 'sweetalert2/dist/sweetalert2.js'
import store from "../../store";
import $ from 'jquery'
export default {
    data() {
        return {
            ctx: "",
            section: "transactions",
            isLoading: false,
            clients: [],
            fullPage: true,
            data: {
                labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
                datasets: [{
                    label: '# of Votes',
                    data: [12, 19, 3, 5, 2, 3],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(153, 102, 255, 0.2)',
                        'rgba(255, 159, 64, 0.2)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        }
    },
    mounted() {
        this.ctx = document.getElementById('myChart').getContext('2d');
        // this.getGraph()
    },
    methods: {
        getGraph() {
            let vm = this;
            $.get({
                url: store.state.rootUrl + 'dashboard/v1/client/graph/summary',
                type: "get",
                async: true,
                crossDomain: true,
                data: {
                    days: vm.$parent.day,
                },
                headers: {
                    "X-Token-Key": vm.$cookies.get("accessToken"),
                    "X-Requested-With": "XMLHttpRequest",
                    "X-App-Key": store.state.systemToken,
                    "X-Authorization-Key": store.state.channel,
                    "Content-Type": "application/json"
                },
                success: function (response, status, jQxhr) {
                    if (response.data.code == 200) {
                        var result = response.data.data
                        var labelData =[]
                        var dataY = []
                        
                        for (var i = 0; i < result.length; i++) {
                          labelData.push(result[i].dateT)
                          dataY.push(result[i].totalCount)
                        }
                        console.log("Y data "+JSON.stringify(dataY))
                        new Chart(vm.ctx, {
                            type: 'bar',
                            data: {
                                labels: labelData,
                                datasets: [{
                                    label: '# of Transaction',
                                    data: dataY,
                                    backgroundColor: [
                                        'rgba(255, 99, 132, 0.2)',
                                        'rgba(54, 162, 235, 0.2)',
                                        'rgba(255, 206, 86, 0.2)',
                                        'rgba(75, 192, 192, 0.2)',
                                        'rgba(153, 102, 255, 0.2)',
                                        'rgba(255, 159, 64, 0.2)'
                                    ],
                                    borderColor: [
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)'
                                    ],
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });

                    } 
                },
                error: function (jQxhr, status, error) {
                    if (jQxhr.responseJSON.data.code == 401) {
                        var errorMessage = jQxhr.responseJSON.data.message;
                        Swal.fire({
                            icon: 'error',
                            title: errorMessage,
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 5000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });
                        vm.$router.push({
                            name: 'login'
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: jQxhr.responseJSON.statusDescription,
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 5000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });
                    }
                }
            });

        }
    }

}
</script>
