<template>
  <div class="relative z-0">

    <div class="fixed left-0 top-0 bottom-0 w-64 bg-white rounded-r-xl shadow-lg overflow-auto z-10"
         :style="{ left: this.$store.state.isSideMenuOpen ? '0' : '-16em' }"
         style="background-color: #084B72; color: white;">

      <div class="text-xl font-bold px-8 pt-6">SaloPlus<sup>+</sup></div>

      <div class="px-8 text-sm">

        <router-link v-if="checkHasPermissions('49')||this.$store.state.isSuperRole" :to="{name: 'dashboard'}"
                     class="w-full flex my-4 gap-4 items-center opacity-70"
                     :class="{'opacity-100 font-bold text-accent': $route.name === 'dashboard'}">
          <svg width="21" height="21" fill="#fff" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M4 13h6c.55 0 1-.45 1-1V4c0-.56-.45-1-1-1H4c-.552 0-1 .44-1 1v8c0 .55.44 1 1 1Zm-1 7c0 .55.44 1 1 1h6c.55 0 1-.45 1-1v-4c0-.56-.45-1-1-1H4c-.552 0-1 .44-1 1v4Zm10 0c0 .55.44 1 1 1h6c.55 0 1-.45 1-1v-7c0-.56-.45-1-1-1h-6c-.56 0-1 .44-1 1v7Zm1-10h6c.55 0 1-.45 1-1V4c0-.56-.45-1-1-1h-6c-.56 0-1 .44-1 1v5c0 .55.44 1 1 1Z"/>
          </svg>
          <span>Dashboard</span>
        </router-link>

        <router-link :to="{name: 'organisations'}"
                     class="w-full block my-4 gap-4 items-center opacity-70"
                     :class="{'text-accent opacity-100 font-bold':
                     ['organisations','organisations-config','organisations-bulk',
                     'requests','limits','check-off','loan-accounts','loan-accounts-add','loan-accounts-edit',
               'loan-products','loan-products-add','loan-products-edit'
               ].includes(this.$route.name) }">
          <div class="flex items-center gap-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008z"/>
            </svg>

            <span class="flex-grow">Organisations</span>
            <svg width="16" height="16" fill="#fff" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                    d="M1.64 4.646l-.01 0c.19-.2.51-.2.7-.01 0 0 0 0 0 0l5.64 5.64 5.64-5.65 -.01 0c.19-.2.51-.2.7-.01 .19.19.19.51 0 .7l-6 6 0-.01c-.2.19-.52.19-.71 0 -.01-.01-.01-.01-.01-.01l-6-6 -.01-.01c-.2-.2-.2-.52-.01-.71 0-.01 0-.01 0-.01Z"/>
            </svg>
          </div>
          <div class="pt-2 ml-2.5 border-l border-white border-opacity-30 pl-7 text-xs font-normal text-white"
               v-show="['organisations','organisations-config','organisations-bulk',
               'requests','limits','check-off','loan-accounts','loan-accounts-add','loan-accounts-edit',
               'loan-products','loan-products-add','loan-products-edit'
               ].includes(this.$route.name)">

            <router-link :to="{name: 'organisations'}" class="block w-full my-2 opacity-70"
                         :class="{'font-medium text-accent opacity-100': ['organisations'].includes(this.$route.name)}">
              Organisations
            </router-link>

            <router-link :to="{name: 'organisations-bulk'}" class="block w-full my-2 opacity-70"
                         :class="{'font-medium text-accent opacity-100': ['organisations-bulk'].includes(this.$route.name)}">
              Organisation's Bulk SMS
            </router-link>

            <router-link v-if="this.$store.state.isSuperRole" :to="{name: 'organisations-config'}"
                         class="block w-full my-2 opacity-70"
                         :class="{'font-medium text-accent opacity-100': ['organisations-config'].includes(this.$route.name)}">
              Organisation Config
            </router-link>
          </div>
        </router-link>

        <router-link v-if="checkHasPermissions('66')||this.$store.state.isSuperRole" :to="{name: 'transactions'}"
                     class="w-full flex my-4 gap-4 items-center opacity-70"
                     :class="{'opacity-100 font-bold text-accent': $route.name === 'transactions'}">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
               stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"/>
          </svg>
          <span>Transactions</span>
        </router-link>


<!--        <router-link :to="{name: 'loan-repayments'}"
                     class="w-full flex my-4 gap-4 items-center opacity-70"
                     :class="{'opacity-100 font-bold text-accent': $route.name === 'loan-repayments'}">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
               stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"/>
          </svg>
          <span>Repayments</span>
        </router-link>-->

        <router-link v-if="checkHasPermissions('95')||this.$store.state.isSuperRole" :to="{name: 'withdrawals'}"
                     class="w-full flex my-4 gap-4 items-center opacity-70"
                     :class="{'opacity-100 font-bold text-accent': $route.name === 'withdrawals'}">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
               stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 19V6m-7 7l7-7 7 7"/>
          </svg>
          <span>Withdrawals</span>
        </router-link>

        <router-link v-if="this.$store.state.isSuperRole" :to="{name: 'check-off'}"
                     class="w-full flex my-4 gap-4 items-center opacity-70"
                     :class="{'opacity-100 font-bold text-accent': $route.name === 'check-off'}">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
            <path fill-rule="evenodd"
                  d="M20.78 5.22a.75.75 0 010 1.06l-11 11a.75.75 0 01-1.06 0l-5-5a.75.75 0 111.06-1.06L9 15.19l10.72-10.72a.75.75 0 011.06 0z"
                  clip-rule="evenodd"/>
          </svg>

          <span>CheckOff</span>
        </router-link>

        <router-link v-if="checkHasPermissions('94')||this.$store.state.isSuperRole" :to="{name: 'bill-payments'}"
                     class="w-full block my-4 gap-4 items-center opacity-70"
                     :class="{'text-accent opacity-100 font-bold': ['bill-payments','bill-payments-add','bill-payments-edit',].includes(this.$route.name) }">
          <div class="flex items-center gap-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M4 7V3a1 1 0 011-1h14a1 1 0 011 1v4M4 7h16M4 7h16v13a1 1 0 01-1 1H5a1 1 0 01-1-1V7zM9 11h6m-6 4h6"/>
            </svg>
            <span class="flex-grow">Bill Payments</span>
          </div>
        </router-link>

        <router-link :to="{name: 'customers'}" class="w-full flex my-4 gap-4 items-center opacity-70"
                     :class="{'opacity-100 font-bold text-accent': $route.name === 'customers'}">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
               stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M21 21l-4.35-4.35m-3.257-.293a7.5 7.5 0 111.084-1.09m0 0L21 21m-9-16.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11z"/>
          </svg>
          <span>Customer Search</span>
        </router-link>

        <router-link v-if=" checkHasPermissions('50')||this.$store.state.isSuperRole" :to="{name: 'system-users'}"
                     class="w-full block my-4 gap-4 items-center opacity-70"
                     :class="{'text-accent opacity-100 font-bold': ['system-users','system-users-add','system-users-edit','system-roles','system-roles-add','system-permissions',].includes(this.$route.name) }">
          <div class="flex items-center gap-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M4.5 12a7.5 7.5 0 0015 0m-15 0a7.5 7.5 0 1115 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077l1.41-.513m14.095-5.13l1.41-.513M5.106 17.785l1.15-.964m11.49-9.642l1.149-.964M7.501 19.795l.75-1.3m7.5-12.99l.75-1.3m-6.063 16.658l.26-1.477m2.605-14.772l.26-1.477m0 17.726l-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205L12 12m6.894 5.785l-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864l-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"/>
            </svg>

            <span class="flex-grow">System</span>
            <svg width="16" height="16" fill="#fff" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                    d="M1.64 4.646l-.01 0c.19-.2.51-.2.7-.01 0 0 0 0 0 0l5.64 5.64 5.64-5.65 -.01 0c.19-.2.51-.2.7-.01 .19.19.19.51 0 .7l-6 6 0-.01c-.2.19-.52.19-.71 0 -.01-.01-.01-.01-.01-.01l-6-6 -.01-.01c-.2-.2-.2-.52-.01-.71 0-.01 0-.01 0-.01Z"/>
            </svg>
          </div>
          <div class="pt-2 ml-2.5 border-l border-white border-opacity-30 pl-7 text-xs font-normal text-white"
               v-show="['system-users','system-users-add','system-users-edit','system-roles','system-permissions',].includes(this.$route.name)">
            <router-link :to="{name: 'system-users'}" class="block w-full my-2 opacity-70"
                         :class="{'font-medium text-accent opacity-100': ['system-users'].includes(this.$route.name)}">
              Users
            </router-link>
            <router-link :to="{name: 'system-roles'}" class="block w-full my-2 opacity-70"
                         :class="{'font-medium text-accent opacity-100': ['system-roles'].includes(this.$route.name)}">
              Roles
            </router-link>
            <router-link :to="{name: 'system-permissions'}" class="block w-full my-2 opacity-70"
                         :class="{'font-medium text-accent opacity-100': ['system-permissions'].includes(this.$route.name)}">
              Permissions
            </router-link>
          </div>
        </router-link>

        <router-link v-if="checkHasPermissions('49')||this.$store.state.isSuperRole" :to="{name: 'logs'}"
                     class="w-full flex my-4 gap-4 items-center opacity-70"
                     :class="{'opacity-100 font-bold text-accent': this.$route.name === 'logs'}">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
               stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"/>
          </svg>
          <span>Logs</span>
        </router-link>

        <div class="pt-4"></div>

        <button style="background-color: coral;"
                class="inline-block mt-20 px-3 py-2 rounded-md text-white bg-coral-500 hover:bg-coral-600 focus:ring-2 focus:ring-coral-600 focus:ring-opacity-50"
                @click="confirmLogOut">

          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
               class="w-6 h-6 inline-block align-middle -mt-1 mr-2">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75"/>
          </svg>

          <span class="align-middle">Logout</span>
        </button>

      </div>
    </div>

    <div class="main-content fixed left-64 top-0 bottom-0 -right-0 overflow-auto"
         :style="{ left: this.$store.state.isSideMenuOpen ? '16em' : '0' }">

      <!--    <div class="fixed left-64 top-0 bottom-0 -right-0 overflow-auto">-->
      <div :style="{ left: this.$store.state.isSideMenuOpen ? '16em' : '0' }">
        <RouterView/>
      </div>
    </div>

  </div>
</template>

<script>
import {mapActions} from "vuex";
import $ from "jquery";

export default {
  data() {
    return {
      authorized: false,
    }
  },

  methods: {
    ...mapActions(["LogOut",]),


    //
    //
    checkHasPermissions(permission) {
      return this.$store.state.permissions.includes(permission)
    },

    async confirmLogOut() {
      let app = this

      app.$swal.fire({
        title: 'Logout',
        text: "Are you sure you want to logout?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.logout()
        },
      })
          .then(async (result) => {

          })
    },


    async logout() {
      await this.LogOut();
    },

    async getToken() {
      let app = this

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this generates a new token!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, generate!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.generateToken({})
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.$swal.fire('Generated!', result.value.message, 'success')
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    getPermission(name) {
      //let app = this
      if (this.$store.state.role === 1 && name !== 'Read Kiron Statistics') {
        return true
      }
      // kiron
      if (name === 'Read Kiron Statistics' && this.$store.state.role === 8) {
        return true
      }
      let permissions = this.$store.state.permission
      for (let item of permissions) {
        if (item.name === name) {
          //console.log('returning true: ' + name)
          return true
        }
      }
      //console.log('returning false: ' + name)
      return false

    }
  }
}
</script>
