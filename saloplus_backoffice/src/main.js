import { createApp } from 'vue'
import './assets/app.css'
import App from './App.vue'
import router from './router'
import store from './store'
import 'vue-loaders/dist/vue-loaders.css';
import 'sweetalert2/dist/sweetalert2.min.css';
import VueLoaders from 'vue-loaders';
import VueSweetalert2 from 'vue-sweetalert2';
import VueCookies from 'vue-cookies'
import Datepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
const app = createApp(App)
import VueSelect from "vue-select";
import "vue-select/dist/vue-select.css";

app.use(router)
app.use(store)
app.use(VueLoaders)
app.use(VueCookies)
app.use(VueSweetalert2)
app.component("v-select", VueSelect);
app.component('Datepicker', Datepicker);


app.mount('#app')
