<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Send Bulk</div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <div class="grid grid-cols-2 gap-4">
        <div class="block">
          <label class="font-semibold text-black mb-1 block">Filter By Organisation <strong
              v-show="clientName">({{ clientName }})</strong></label>
          <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                 @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
          <ul class=" left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10 "
              v-if="searchDropdown">
            <li v-for="item in filteredOrganizations"
                class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                @click="setOrganisationId(item)">{{ item.text }}
            </li>
          </ul>
        </div>

        <div class="block">
          <label class="font-semibold text-black mb-1 block ">Send to One/Many</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.send_to">
            <option value="" disabled>Select</option> <!-- Placeholder option -->
            <option v-for="item in sendTo" :value="item.value">
              {{ item.text }}
            </option>
          </select>
        </div>
      </div>

      <div v-if="form.send_to===2" class="relative mt-5">
        <label class="font-semibold text-black mb-1 block">Select Filters</label>
        <div class="flex flex-wrap w-full bg-white border rounded-md">
          <div v-for="(item, index) in selectedFilters" :key="item.value"
               class="flex items-center px-3 py-1 m-1 bg-gray-200 rounded-full">
            <span>{{ item.text }}</span>
            <button @click="removeFilters(index)" class="ml-2 focus:outline-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-600" viewBox="0 0 20 20"
                   fill="currentColor">
                <path fill-rule="evenodd"
                      d="M5.293 5.293a1 1 0 011.414 0L10 8.586l3.293-3.293a1 1 0 111.414 1.414L11.414 10l3.293 3.293a1 1 0 11-1.414 1.414L10 11.414l-3.293 3.293a1 1 0 01-1.414-1.414L8.586 10 5.293 6.707a1 1 0 010-1.414z"
                      clip-rule="evenodd"/>
              </svg>
            </button>
          </div>
          <input class="flex-grow px-3 py-1 outline-none" @click="toggleFiltersSearchDropdown" v-model="searchFilter"
                 @input="filteredFilters"
                 :placeholder="filterSearchDropdownPlaceholder">
        </div>
        <ul v-if="filterSearchDropdown"
            class="absolute left-0 mt-1 w-full bg-white border border-gray-300 rounded-md z-10">
          <li v-for="item in filteredFilters" :key="item.value"
              class="px-3 py-2 cursor-pointer hover:bg-gray-100" @click="toggleFilters(item)">
            {{ item.text }}
          </li>
        </ul>
      </div>

      <div v-else class="block mt-5">
        <label v-if="clientName===''" class="font-semibold text-black mb-1 block">Search Customer <strong
            v-show="customerName"> </strong></label>
        <label v-else class="font-semibold text-black mb-1 block">Search Customer From {{ clientName }} <strong
            v-show="customerName"> </strong></label>
        <!---->
        <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchCustomer"
               @click="toggleCustomerSearchDropdown" :placeholder="customerSearchDropdownPlaceholder">

        <ul class=" left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10 "
            v-if="customerSearchDropdown&&customers.length>0">
          <li v-for="item in filteredCustomers"
              class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
              @click="setCustomerId(item)">{{ item.first_name }} - {{ item.msisdn }}
          </li>
        </ul>

        <ul class=" left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10 "
            v-if="customerSearchDropdown&&customers.length===0">
          <li v-if="clientName===''" class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"> No
            Customers for {{ clientName }}
          </li>
          <li v-else class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"> No Customers</li>
        </ul>

      </div>

      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

      <div class="block mt-5">
        <label class="font-semibold text-black mb-1 block">Message to send</label>
        <div class="block border-2">
          <div class="ui top attached segment">
            <div class="ui fluid">
              <textarea class="p-2 text" v-model="bulk_params.message" name="w3review" rows="5" style="width: 100%; "></textarea>
            </div>
          </div>

          <div class=" bg-blue-100">
            <div class="grid grid-cols-2 p-4">
              <div class="block"><strong><span v-text="wordCount"></span></strong> character(s)</div>
              <div class="block text-right"><strong><span v-text="pageCount"></span></strong> page(s)</div>
            </div>
          </div>
        </div>
      </div>

      <button class="w-full inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20 mt-10"
              @click="sendMessage"
              id="addMember">
        <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
        Send
      </button>

    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import moment from "moment-timezone";
import country from 'country-list-js';
import VueDatePicker from "@vuepic/vue-datepicker";

export default {
  data() {
    return {

      text: "",
      wordCount: 0,
      pageCount: 0,

      // search
      selectedFilters: [],
      filteredFilters: [],
      filters: [],
      searchFilter: "",
      filterSearchDropdown: false,
      filterSearchDropdownPlaceholder: "Search and Select Filter",
      filterName: "",

      ///

      client_id: "",
      organisations: [],
      //
      customers: [],
      customerName: "",
      customerId: "",
      searchCustomer: "",
      customerSearchDropdownPlaceholder: "Search by Customer Name",
      customerSearchDropdown: false,

      ///
      isLoading: false,
      fullPage: true,

      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",

      //
      account_number: '',
      moreParams: {
        account_number: '',
      },

      moreParamsCustomer: {
        start: "",
        end: "",
        client_id: '',
        limit: 1000
      },
      // filters: [],

      configs: {
        currency_code: "",
        max_total_loans: "",
        can_issue_loans: false,
        service_fee: "",
        iprs_token: "",
        crb_token: "",
        app_name: null,
        logo_url: null,
        color_scheme: null,
        open_date: "",
        close_date: "",
        b2c_merchant_code: "",
        c2b_merchant_code: "",
        b2c_credentials: null,
        bulk_short_code: "",
        smtp_host: "",
        smtp_port: "",
        smtp_username: "",
        smtp_password: null,
        help_line: null
      },


      /// old
      // date: null,
      loading: false,
      form: {
        send_to: 1,
        dial_code: "254",
        company_phone: null,
        company_address: null,
        company_name: null,
        company_email: null,
        contact_person: null
      },

      sendTo: [
        {text: 'To Individual', value: 1},
        {text: 'To Many', value: 2},
      ],

      bulk_params: {
        // timestamp: '',
        account_number: '',
        filter: '',
        message: '',
      },


    }
  },
  components: {
    Loading
  },
  watch: {
    searchFilter(newVal, oldVal) {
      if (newVal !== oldVal && newVal !== "") {
        this.filteredFilters();
      }
    },

    text: function (val, oldVal) {
      if (val !== oldVal) {
        var count = val.length;
        // Display it as output
        this.wordCount = count;

        var page = count / 160;
        this.pageCount = Math.ceil(page);
      }
    },
  },
  async mounted() {
    await this.setFilters()

    await this.setOrganisations()
  },
  computed: {
    filteredFilters() {
      return this.filters.filter(org => org.text.toLowerCase().includes(this.searchFilter.toLowerCase()));
    },

    filteredOrganizations() {
      return this.organisations.filter(org => org.text.toLowerCase().includes(this.searchClient.toLowerCase()));
    },

    filteredCustomers() {
      try {
        return this.customers.filter(customer => customer.first_name.toLowerCase().includes(this.searchCustomer.toLowerCase()));
      } catch (e) {

      }
    }
  },
  methods: {
    ...mapActions(["getSmsFilters", "getMerchants", "getLoanAccounts"]),
    toggleFilters(item) {
      this.toggleFiltersSearchDropdown()
      const index = this.selectedFilters.findIndex(org => org.value === item.value);
      if (index === -1) {
        this.selectedFilters.push(item);
      } else {
        this.selectedFilters.splice(index, 1);
      }
    },

    removeFilters(index) {
      this.selectedFilters.splice(index, 1);
      // this.filterOrganizations();
    },

    toggleFiltersSearchDropdown() {
      // Toggle the value of filterSearchDropdown
      this.filterSearchDropdown = !this.filterSearchDropdown;
    },

    // Fetch and set Organisations to UI
    async setFilters() {
      let app = this
      let response = await this.getSmsFilters(app.moreParams)
      if (response.status === 200) {
        response.message.forEach(function (item) {
          let list = {text: item.toUpperCase(), value: item}
          app.filters.push(list)
        })
      }
    },

    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
      this.customerSearchDropdown = false;
      this.searchCustomer = ""
      this.customerName = ""
      this.customerSearchDropdownPlaceholder = ""

    },

    setOrganisationId(item) {
      this.clientName = item.text
      this.searchDropdown = false
      this.searchFilter = ""
      this.searchDropdownPlaceholder = this.clientName

      this.moreParamsCustomer.client_id = item.value
      this.customers = []
      this.setLoanAccounts()
    },

    toggleCustomerSearchDropdown() {
      // Toggle the value of searchDropdown
      this.customerSearchDropdown = !this.customerSearchDropdown;
    },

    setCustomerId(item) {
      console.log("setCustomerId:", item.loan_number)
      this.customerId = item.loan_number
      this.customerName = item.first_name
      this.customerSearchDropdown = false
      this.searchCustomer = ""
      this.customerSearchDropdownPlaceholder = this.customerName + " - " + item.msisdn
    },

    async setOrganisations() {
      let app = this
      app.isLoading = true;
      let response = await this.getMerchants({limit: 100, status: 1})
      if (response.status === 200) {
        response.message.data.forEach(function (item) {
          let _organisations = {text: item.client_name, value: item.client_id}
          app.organisations.push(_organisations)

        })
      } else {
      }
      app.isLoading = false
    },

    //
    async setLoanAccounts() {
      this.isLoading = true
      let response = await this.getLoanAccounts(this.moreParamsCustomer)
      this.customers = response.message.data

      this.searchCustomer = ""
      this.isLoading = false
    },

    //
    async sendMessage() {
      let app=this
      this.isLoading = true

      console.log("bulk_params : " + JSON.stringify(this.bulk_params));

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this sends SMS to Client(s)!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, set!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {

          console.log("bulk_params : " + JSON.stringify(this.bulk_params));
          return await this.sendBulkSMS(this.bulk_params)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Set!', result.value.message, 'success')

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })




      // let response = await this.sendBulkSMS(this.bulk_params)
      //
      // // console.log("deposits OK: " + JSON.stringify(response.message.result))
      // if(response.status===200) {
      //
      // }
      this.isLoading = false
    },


  },
}
</script>
