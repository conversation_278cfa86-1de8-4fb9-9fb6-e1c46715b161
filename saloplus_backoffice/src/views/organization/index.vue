<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">

    <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

      <div class="flex font-medium p-6 pb-1 border-b gap-4 mb-5">
        <div class="flex-shrink font-medium">
          <i v-if="!this.$store.state.isSideMenuOpen" class="fa fa-bars text-black text-lg" @click="toggleSideM"></i>
          <i v-else class="fa fa-close text-black text-lg" @click="toggleSideM"></i>
        </div>

        <div class="flex-grow font-medium pb-2"> Organizations</div>
      </div>

    <div v-if="checkHasPermissions('82') || this.$store.state.isSuperRole "
         class="w-full flex mt-4 text-xs">
      <div class="flex-grow gap-2 flex"></div>
      <!--      <div class="px-4">
              <router-link class="inline-block px-4 py-2 rounded-md bg-primary
              font-bold hover:text-orange-600 hover:bg-green-400"
                           :to="{ name: 'organisations-add' }">
                Add Organisation
              </router-link>
            </div>-->

      <CustomButton
          name="Add Organisation"
          width="150px"
          :link="{ name: 'organisations-add' }"
      />

    </div>

    <div class="block px-6 py-4 mt-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <div class="mb-4">
        <label for="statusFilter" class="text-sm font-medium">Filter by Status:</label>
        <select
            id="statusFilter"
            v-model="moreParams.status"
            @change="setMerchants"
            class="border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-green-500 focus:outline-none ml-2 py-1 px-2 text-sm text-gray-800 transition duration-150 ease-in-out hover:border-green-500"
        >
          <option value="">All</option>
          <option value="1">Active</option>
          <option value="2">Unverified</option>
          <option value="3">Deactivated</option>
        </select>
      </div>

      <table class="w-full mb-4 table">


        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">Acc No</th>
          <th class="py-2">Company</th>
          <th class="py-2">Contacts</th>
          <th class="py-2">Max Loan Assets</th>
          <th class="py-2">Open Dates</th>
          <th class="py-2">PayBills</th>
          <th class="py-2 text-center">Date</th>
          <th class="py-2 text-center">Status</th>
          <th class="text-center">Actions</th>
          <th></th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in organisations" :key="item.trans_id">
          <td class="py-2 font-medium">
            <strong> {{ item.client_account }}</strong>
          </td>

          <td class="py-2">
            <strong>{{ item.client_name }}</strong>
          </td>

          <td class="py-2">
            <strong>+{{ item.client_phone }}</strong>
            <br>
            <span>{{ item.client_email }}</span>
          </td>


          <td class="py-2">
            <strong>{{ item.currency_code }}. {{
                formatCurrency(parseFloat(item.total_loan_assets).toFixed(2))
              }}</strong>
            <br>
            <span v-if="item.can_issue_loans_desc==='CAN_ISSUE'" style="font-size: smaller; color: green">
              {{ item.can_issue_loans_desc }} loans</span>
            <span v-else style="font-size: smaller; color: orange">
              {{ item.can_issue_loans_desc }} loans</span>
          </td>

          <td class="py-2 ">
            <strong>{{ item.open_date }}{{ getDateSuffix(item.open_date) }} - {{
                item.close_date
              }}{{ getDateSuffix(item.close_date) }}</strong>
          </td>

          <td class="py-2 ">
            <strong>B2C: {{ item.b2c_paybill }}</strong>
            <br>
            <span>C2B: {{ item.c2b_paybill }}</span>
          </td>

          <td class="py-2 font-medium">
            <span>{{ moment(item.created).format('lll') }}</span>
          </td>

          <td class="py-2 text-center">
            <button v-if="parseInt(item.client_status) === 1" class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: green">
              Activate
            </button>
            <button v-else-if="parseInt(item.client_status) === 3"
                    class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: red">
              Deactivated
            </button>
            <button v-else
                    class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: purple">
              Inactive
            </button>
          </td>

          <td class="py-2 text-center relative w-32">
            <div class="flex items-center justify-center space-x-2">
              <!-- Edit Button -->
              <action-button
                variant="edit"
                size="sm"
                shape="round"
                :icon-only="true"
                tooltip="Edit Client"
                @click="editClient(item)"
              />

              <!-- View Transactions Button -->
              <action-button
                variant="transactions"
                size="sm"
                shape="round"
                :icon-only="true"
                tooltip="View Transactions"
                @click="viewTransactions(item)"
              />

              <!-- Status Toggle Button -->
              <action-button
                :variant="parseInt(item.client_status) === 1 ? 'danger' : 'success'"
                size="sm"
                shape="round"
                :icon-only="true"
                :tooltip="parseInt(item.client_status) === 1 ? 'Deactivate Client' : 'Activate Client'"
                @click="parseInt(item.client_status) === 1 ? deactivate(item.client_account) : toggleActivate(item)"
              />

              <!-- More Actions Dropdown -->
              <div class="relative inline-block">
                <action-button
                  variant="secondary"
                  size="sm"
                  shape="round"
                  :icon-only="true"
                  tooltip="More Actions"
                  @click="toggleDropdown(index)"
                >
                  <template #icon>
                    <i class="fa fa-ellipsis-v"></i>
                  </template>
                </action-button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 260px; text-align: left;">
                <ul class="py-2 ">

                  <li v-if="this.$store.state.role===1&&parseInt(item.client_status) !== 1"
                      @click.prevent="toggleActivate(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200"> Activate <b>{{ item.client_name }}</b></a>
                  </li>

                  <li v-else-if="this.$store.state.role===1&&parseInt(item.client_status) === 1"
                      @click.prevent="deactivate(item.client_account)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-red-200">Deactivate <b>{{ item.client_name }}</b></a>
                  </li>

                  <li @click="viewAccounts(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">View Loan Accounts </a>
                  </li>

                  <li @click="viewLoanRequests(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">View Loan Requests </a>
                  </li>

                  <li @click="viewLoanLimitRequests(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">View Loan Limits </a>
                  </li>

                  <li @click="viewTransactions(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">View Transactions</a>
                  </li>

                  <li @click="viewLoanProducts(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">View Loan Products </a>
                  </li>

                  <!--                  <li v-if="this.$store.state.isSuperRole&&parseInt(item.client_status) === 1"
                                        @click="addSystemUser(item)">
                                      <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Add System User to
                                        <b>{{ item.client_name }}</b> </a>
                                    </li>-->

                  <li v-if="this.$store.state.isSuperRole"
                      @click="viewUsers(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200"> View <b>{{ item.client_name }}</b>
                      System Users</a>
                  </li>

                  <li v-if="this.$store.state.isSuperRole && parseInt(item.client_status) === 1"
                      @click="sendBlk(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Send Bulk SMS</a>
                  </li>

                </ul>
              </div>

            </div>
          </td>
        </tr>
        </tbody>
      </table>

      <!--Pagination-->
      <div v-show="total>limit" class="flex w-full text-xs items-center pb-2">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to {{ limit * offset }} of
          {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1" @click="gotToPage(offset-1)">&larr;</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>

    </div>

    <!--Activate Organization Modal-->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': open, 'opacity-0 pointer-events-none': !open }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3">
            <p class="text-xl font-bold">Activate Organization - {{ this.client_name }}</p>
            <div class="modal-close cursor-pointer z-50" @click="open = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                   viewBox="0 0 18 18">
                <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!-- Body -->
          <div class="mb-4">
            <hr class="divider">

            <div class="grid grid-cols-2 gap-4 mb-4 mt-3">
              <div class="block">
                <label class="text-xs mb-1 block ">Service Fee (in %)</label>
                <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="service_fee"
                       placeholder="1%">
              </div>
              <div class="block">
                <label class="text-xs mb-1 block ">Max Loan Limit Amount</label>
                <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                       v-model="max_client_loan" placeholder="Max Loan Limit Amount">
              </div>
            </div>

            <hr class="divider">

            <div class="block mb-2 mt-2">
              <input type="checkbox" id="can_issue_loans" v-model="can_issue_loans">
              <label class="ml-4" for="can_issue_loans">Can Issue Loans</label>
            </div>

            <hr class="divider">
          </div>

          <!-- Footer -->
          <div class="flex justify-end pt-2">

            <button @click="toggleApprove"
                    class="inline-block px-4 py-2 bg-neutral-100 border rounded-md mr-4">
              Cancel
            </button>
            <button @click="activateOrg"
                    class="inline-block px-4 py-2 bg-green-500 text-white  border rounded-md ml-2 pl-10 pr-10">
              Activate
            </button>
          </div>
        </div>
      </div>
    </div>

    <!--Limit Configuration Modal-->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': open_limit, 'opacity-0 pointer-events-none': !open_limit }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3">
            <p class="text-xl font-bold">Limit Configuration</p>
            <div class="modal-close cursor-pointer z-50" @click="open_limit = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                   viewBox="0 0 18 18">
                <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!-- Body -->
          <div class="mb-4">
            <hr class="divider">

            <div class="grid grid-cols-1 gap-4 mb-4 mt-3">
              <div class="block">
                <label class="text-xs mb-1 block ">Max Client Loan</label>
                <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                       v-model="max_client_loan" placeholder="Max Loan Limit Amount">
              </div>
            </div>

            <div class="block mb-2 mt-2">
              <input type="checkbox" id="crbCheck" v-model="can_issue_loans">
              <label class="ml-4" for="crbCheck">Can Issue Loans</label>
            </div>

            <hr class="divider">
          </div>

          <!-- Footer -->
          <div class="flex justify-end pt-2">

            <button @click="open_limit=false"
                    class="inline-block px-4 py-2 bg-neutral-100 border rounded-md mr-4">
              Cancel
            </button>
            <button @click="setLimit"
                    class="inline-block px-4 py-2 bg-green-500 text-white  border rounded-md ml-2 pl-10 pr-10">
              Set
            </button>
          </div>
        </div>
      </div>
    </div>

    <!--Service Fee Configuration Modal-->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': service, 'opacity-0 pointer-events-none': !service }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3">
            <p class="text-xl font-bold">Service Fee Configuration</p>
            <div class="modal-close cursor-pointer z-50" @click="service = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                   viewBox="0 0 18 18">
                <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!-- Body -->
          <div class="mb-4">
            <hr class="divider">

            <div class="grid grid-cols-1 gap-4 mb-4 mt-3">
              <div class="block">
                <label class="text-xs mb-1 block ">Service Fee</label>
                <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                       v-model="service_fee" placeholder="Service Fee">
              </div>
            </div>

            <hr class="divider">
          </div>

          <!-- Footer -->
          <div class="flex justify-end pt-2">

            <button @click="service=false"
                    class="inline-block px-4 py-2 bg-neutral-100 border rounded-md mr-4">
              Cancel
            </button>
            <button @click="setService"
                    class="inline-block px-4 py-2 bg-green-500 text-white  border rounded-md ml-2 pl-10 pr-10">
              Set Service Fee
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>

import CustomButton from '../../components/custom_button.vue'
import ActionButton from '../../components/action_button.vue'

import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'

export default {
  data() {
    return {
      foundPermission: "",
      form: {
        full_name: "",
      },

      showDropdown: [],
      open: false,
      open_limit: false,
      service: false,
      loading: true,
      visible: false,
      message: null,
      sortOrder: [
        {
          field: "created_at",
          direction: "desc"
        }
      ],

      moreParams: {
        start: "",
        end: "",
        limit: 10,
        offset: 0,
        page: 1,
        status: "1",
      },
      days: 3,
      time3: "",
      perPage: 10,
      organisations: [],
      moment: moment,
      status_description: '',
      id: '',
      status: '',
      approved_amount: 0,
      label: 'approved',
      account_number: '',
      client_name: '',
      // B2C
      b2c_paybill: '',
      b2c_initiator_name: '',
      b2c_initiator_password: '',
      b2c_paybill_secret_key: '',
      b2c_paybill_consumer_key: '',
      //C2B
      c2b_paybill: '',
      c2b_initiator_name: '',
      c2b_initiator_password: '',
      c2b_paybill_secret_key: '',
      c2b_paybill_consumer_key: '',
      c2b_paybill_pass_key: '',
      //
      max_client_loan: 0,
      service_fee: 0,
      can_issue_loans: false,
      add_paybill: false,

      ///
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,

      isOpen: false,
      isLoading: false,
      //
      items: [],
      clients: [],
      clientId: null,
    }
  },
  components: {
    Loading,
    VueDatePicker,
    CustomButton,
    ActionButton,
  },

  mounted() {
    // console.log("sad", this.$store.state.permissions)
    this.setMerchants()
  },

  methods: {
    ...mapActions(["toggleSideMenu", "getMerchants", "updateMerchant", "editMerchant", "deleteMerchant", "fillLoanAcc", "fillOrgAcc",
      "setMerchantLimit", "setMerchantFee", "fillMerchantAccount",]),
    handleClick() {
      alert('Button Clicked!');
    },

    //
    checkHasPermissions(permission) {
      // console.log("checkHasPermissions: ", p)
      return this.$store.state.permissions.includes(permission)
    },
    //
    toggleSideM() {
      this.toggleSideMenu()
      // this.isSideMenuOpen = !this.isSideMenuOpen;
    },

    //
    formatCurrency(number) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },

    //
    getDateSuffix(day) {
      let day_suffix = 'th';
      if (day < 10 || day > 20) {
        switch (day % 10) {
          case 1:
            day_suffix = 'st';
            break;
          case 2:
            day_suffix = 'nd';
            break;
          case 3:
            day_suffix = 'rd';
            break;
          default:
            break;
        }
      }

      return day_suffix
    },

    //
    gotToPage(page) {
      let vm = this
      vm.moreParams.offset = page
      vm.moreParams.page = page
      vm.offset = page
      vm.setMerchants()
    },

    //
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    //
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    //
    async toggleActivate(item) {
      this.closeDropdown()
      this.account_number = item.client_account
      this.client_name = item.client_name
      this.max_client_loan = item.total_loan_assets
      this.service_fee = item.service_fee * 100
      this.can_issue_loans = item.can_issue_loans === "1";
      //
      this.open = !this.open
    },

    async activateOrg() {
      let app = this

      let payload = {
        account_number: this.account_number,
        status: 1,
        service_fee: this.service_fee,
        max_client_loan: this.max_client_loan,
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this activates this merchant!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, activate!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          return await this.updateMerchant(payload)
        },
      })
          .then(async (result) => {
            // console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Activated!', result.value.message, 'success')
              await app.setMerchants()
              app.open = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    async toggleService(item) {
      this.closeDropdown()
      this.account_number = item.account_number
      this.max_client_loan = item.total_loan_assets
      this.can_issue_loans = item.can_issue_loans === "1";
      this.service_fee = item.service_fee;

      this.service = !this.service
    },

    async approve() {
      let app = this

      let payload = '';

      if (this.service_fee === true) {
        payload = {
          account_number: this.account_number,
          status: 1,
        }
      } else {
        payload = {
          account_number: this.account_number,
          status: 1,
          service_fee: this.service_fee,
          max_client_loan: this.max_client_loan,
          b2c_paybill: this.b2c_paybill,
          b2c_credentials: {
            b2c_initiator_name: this.b2c_initiator_name,
            b2c_initiator_password: this.b2c_initiator_password,
            b2c_paybill_secret_key: this.b2c_paybill_secret_key,
            b2c_paybill_consumer_key: this.b2c_paybill_consumer_key,
          },
          //C2B
          c2b_paybill: this.c2b_paybill,
          c2b_credentials: {
            c2b_initiator_name: this.c2b_initiator_name,
            c2b_initiator_password: this.c2b_initiator_password,
            c2b_paybill_secret_key: this.c2b_paybill_secret_key,
            c2b_paybill_consumer_key: this.c2b_paybill_consumer_key,
            c2b_paybill_pass_key: this.c2b_paybill_pass_key,
          }
          // c2b_paybill: payloadNew,
        }

        // b2c_paybill_username: this.b2c_paybill_username,
        // b2c_paybill_credentials: this.b2c_paybill_credentials,
        // b2c_paybill_secret_key: this.b2c_paybill_secret_key,
        // b2c_paybill_consumer_key: this.b2c_paybill_consumer_key,
        // b2c_paybill: this.b2c_paybill,
        // c2b_paybill: this.c2b_paybill,

      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this activates this merchant!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, activate!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          await app.toggleApprove()
          return await this.updateMerchant(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Activated!', result.value.message, 'success')
              await app.setMerchants()
              app.open = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    async toggleLimit(item) {
      this.closeDropdown()
      this.account_number = item.client_account
      this.max_client_loan = item.total_loan_assets
      this.service_fee = item.service_fee * 100

      this.can_issue_loans = item.can_issue_loans === "1";

      this.limit = !this.limit
    },

    //
    async setLimit() {
      let app = this

      let can_issue_loans = 0
      if (this.can_issue_loans) {
        can_issue_loans = 1
      }
      const payload = {
        account_number: this.account_number,
        can_issue_loans: can_issue_loans,
        max_client_loan: this.max_client_loan,
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this sets this merchant limit!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, set!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          return await this.setMerchantLimit(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Set!', result.value.message, 'success')
              await app.setMerchants()
              app.limit = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    async setService() {
      let app = this

      const payload = {
        account_number: this.account_number,
        service_fee: this.service_fee,
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this sets this merchant service fee!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, set!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          return await this.setMerchantFee(payload)
        },
      })
          .then(async (result) => {
            // console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Set!', result.value.message, 'success')
              await app.setMerchants()
              app.service = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async deactivate(account_number) {
      let app = this
      app.closeDropdown()

      const payload = {
        account_number: account_number,
        status: 3,
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this deactivates this merchant!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, deactivate!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          return await this.updateMerchant(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Deactivated!', result.value.message, 'success')
              await app.setMerchants()
              app.open = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async setLoanProduct(org) {
      await this.fillMerchantAccount(org)
      await this.$router.push({name: 'loan-products-add'})
    },

    async viewTransactions(org) {
      await this.fillMerchantAccount(org)
      await this.$router.push({name: 'transactions', params: {client_id: org.client_id}})
    },

    async viewAccounts(org) {
      await this.fillMerchantAccount(org)
      await this.$router.push({name: 'loan-accounts', params: {client_id: org.client_id}})
    },

    async viewLoanLimitRequests(org) {
      await this.fillMerchantAccount(org)
      await this.$router.push({name: 'limits', params: {client_id: org.client_id}})
    },

    async viewLoanRequests(org) {
      await this.fillMerchantAccount(org)
      await this.$router.push({name: 'requests', params: {client_id: org.client_id}})
    },

    async viewLoanProducts(org) {
      await this.fillMerchantAccount(org)
      await this.$router.push({name: 'loan-products', params: {client_id: org.client_id}})
    },

    async viewUsers(org) {
      await this.fillMerchantAccount(org)
      // console.log("LLLLL1:",JSON.stringify(org))
      await this.$router.push({name: 'system-users', params: {client_id: org.client_id}})
    },

    async addSystemUser(org) {
      await this.fillMerchantAccount(org)
      await this.$router.push({name: 'system-users-add'})
    },

    async sendBlk(org) {
      await this.fillMerchantAccount(org)
      let payload = {type: 'bulk', account_number: org.client_account}
      await this.fillSmsDesign(payload)
      await this.$router.push({name: 'organisations-bulk'})
    },

    async editClient(org) {
      await this.fillMerchantAccount(org)
      await this.$router.push({name: 'organisations-config', params: {client_id: org.client_id}})
    },

    async setMerchants() {
      let app = this
      app.isLoading = true

      let response = await this.getMerchants(this.moreParams)

      if (response.status === 200) {
        app.organisations = response.message.data
        app.total = response.message.total_count

        // console.log("setMerchants app.total: ", app.total)

        app.showDropdown = []
        for (let i = 0; i < app.organisations.length; i++) {
          app.showDropdown.push(false)
        }

      } else {
        app.showDropdown = []
        app.organisations = []
      }

      this.isLoading = false
    },


  }

}
</script>
