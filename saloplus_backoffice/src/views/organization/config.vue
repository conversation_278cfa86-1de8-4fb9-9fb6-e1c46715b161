<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Organisation Config</div>

    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 mb-6 border ">
      <div class="grid grid-cols-2 gap-4 mb-5">

        <div class="relative">
          <label class="block">Select Organisation <strong
              v-show="searchDropdownPlaceholder">({{ searchDropdownPlaceholder }})</strong></label>

          <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                 @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">

          <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
              v-if="searchDropdown">
            <li v-for="item in filteredOrganizations" class="py-2 px-3 hover:bg-gray-100 cursor-pointer"
                @click="setOrg(item)">{{ item.text }}
            </li>
          </ul>
        </div>

        <div class="block">
          <label v-if="selectedConfig===null" class="block font-medium"> <strong>Select Configuration</strong></label>
          <label v-else class="block font-medium">Selected Configuration <strong> ({{
              selectedConfig.text
            }})</strong></label>

          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selectedConfig">
            <option value="" disabled>Select One</option>
            <option v-for="menu in configMenu" :value="menu">
              {{ menu.text }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- PayBill Config -->
    <div v-if="configTypeId==='1'" class="rounded-lg bg-white shadow-lg mx-3 px-6 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">#</th>
          <th class="py-2">Paybill Type</th>
          <th class="py-2">Initiator Name</th>
          <th class="py-2">Paybill Number</th>
          <th class="py-2">Service Route</th>
          <th class="py-2">Date</th>
          <th class="py-2 text-center">Paybill Status</th>
          <th class="py-2 text-center">Wallet Status</th>
          <th class="py-2 text-center">View</th>
          <th class="text-center">Action</th>
          <th></th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in configList" :key="item.id">
          <td class="py-2 w-1 pr-8">{{ (index + 1) + ((offset - 1) * 10) }}</td>

          <td class="py-2"><strong>{{ item.type_name }}</strong></td>

          <td class="py-2 w-1 pr-8"><strong>{{ item.user_name }}</strong></td>

          <td class="py-2"><span><strong>{{ item.paybill_number }}</strong></span></td>

          <td class="py-2"><span><strong>{{ item.service_route_desc }}</strong></span></td>

          <td class="px-2"><span>{{ moment(item.created_at).format('llll') }}</span></td>

          <td class="text-center py-2">
            <div v-if="parseInt(item.paybill_status) === 1" class="action-button"
                 style="background-color: green; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              Active
            </div>
            <div v-else-if="parseInt(item.paybill_status) === 3" class="action-button"
                 style="background-color: red; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              In-Active
            </div>

          </td>

          <td class="text-center py-2">
            <div v-if="parseInt(item.wallet_status) === 1" class="action-button"
                 style="background-color: green; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              Active
            </div>
            <div v-else-if="parseInt(item.wallet_status) === 0" class="action-button"
                 style="background-color: red; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              In-Active
            </div>
          </td>

          <td class="text-center px-2">
            <div @click="toggleDetails(item)" class="action-button"
                 style="background-color: #e3e3e3; padding: 8px; border-radius: 4px;  cursor: pointer;">
              <strong> View </strong>
            </div>
          </td>

          <td class="py-2 text-center relative w-24">
            <div class="relative inline-block">
              <button
                  v-if="parseInt(item.paybill_status) !== 0"
                  class="px-3 py-1 flex items-center space-x-1"
                  @click="toggleDropdown(index)">

                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <button v-else class="px-3 py-1 flex items-center space-x-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#808080" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 230px; text-align: left;">
                <ul class="py-2">

                  <li @click="toggleSetClientWalletSettings(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Set Client Wallet Settings</a>
                  </li>

                  <li v-if="parseInt(item.type_id)===2 && parseInt(item.service_route)===2"
                      @click="switchGates(item,1)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Switch to Daraja</a>
                  </li>

                  <li v-if="parseInt(item.type_id)===2 && parseInt(item.service_route)===2"
                      @click="switchGates(item,1)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Switch to Daraja</a>
                  </li>

                  <li v-if="parseInt(item.type_id)===2 && parseInt(item.paybill_status)===1"
                      @click="switchGates(item,2)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Switch to Broker</a>
                  </li>

                  <li v-if="parseInt(item.type_id)===1 && parseInt(item.paybill_status) === 1"
                      @click="disableEnable(item,0)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Disable Deposits</a>
                  </li>

                  <li v-if="parseInt(item.type_id)===1 && parseInt(item.paybill_status) !== 3"
                      @click="disableEnable(item,3)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Suspend Deposits</a>
                  </li>

                  <li v-if="item.type_id==='1'&& parseInt(item.paybill_status) !== 1"
                      @click="disableEnable(item,1)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Enable Deposits</a>
                  </li>

                  <li v-if="item.type_id==='2'&& parseInt(item.paybill_status) !== 1"
                      @click="disableEnable(item,1)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Enable Payout</a>
                  </li>

                  <li v-if="item.type_id==='2'&& parseInt(item.paybill_status) === 1"
                      @click="disableEnable(item,0)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Disable Payout</a>
                  </li>

                  <li v-if="item.type_id==='2'&& parseInt(item.paybill_status) !== 3"
                      @click="disableEnable(item,3)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Suspend Payout</a>
                  </li>

                  <li @click="editPaybill(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Edit PayBill</a>
                  </li>

                </ul>
              </div>

            </div>
          </td>

        </tr>
        </tbody>
      </table>

      <!--Pagination-->
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to {{ limit * offset }} of
          {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-if="offset > 1" @click="gotToPage(offset-1)">&larr;</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
            <!--            <button class="p-2 px-3" v-show="offset > 1" @click="gotToPage(offset+1)">&rarr;</button>-->
          </div>
        </div>
      </div>

      <!--  PayBill Config Modal  -->
      <div v-if="selectedPBConf.paybill_config_id!==''"
           class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
           :class="{ 'opacity-100 pointer-events-auto': payBillModalOpen, 'opacity-0 pointer-events-none': !payBillModalOpen }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!--Title -->
            <div class="flex justify-between items-center pb-3">
              <p class="text-2xl font-bold">{{ selectedPBConf.type_name }} M-Pesa PayBill</p>
              <div class="modal-close cursor-pointer z-50" @click="payBillModalOpen = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                     viewBox="0 0 18 18">
                  <path
                      d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                </svg>
              </div>
            </div>
            <!--Body -->
            <div class="w-full ">
              <div class="px-3 py-3">
                <div class="grid grid-cols-3 mt-4 ">
                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">PayBill Number</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedPBConf.paybill_number" placeholder="PayBill Number" disabled>
                  </div>

                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">Initiator Name</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedPBConf.user_name" placeholder="PayBill Initiator Name">
                  </div>

                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">Credentials</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedPBConf.b2c_credentials" placeholder="PayBill Credentials">
                  </div>
                </div>

                <div class="grid grid-cols-3 mt-4 ">
                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">PayBill Route</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedPBConf.service_route_desc" placeholder="PayBill Route" disabled>
                  </div>

                  <div class="block mx-1">
                    <label class="block text-xs mb-1">Paybill Status</label>
                    <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                            v-model="selectedPBConf.paybill_status">
                      <option v-for="item in statuses" :value="item.value">
                        {{ item.text }}
                      </option>
                    </select>
                  </div>

                  <div class="block mx-1">
                    <label class="block text-xs mb-1">Paybill Wallet Status</label>
                    <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                            v-model="selectedPBConf.wallet_status">
                      <option v-for="item in statuses" :value="item.value">
                        {{ item.text }}
                      </option>
                    </select>
                  </div>

                </div>

                <div class="grid grid-cols-1 mt-4 ">
                  <div class="block mx-1 ">
                    <label class="text-xs mb-1 block ">Passkey</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedPBConf.pass_key" placeholder="PayBill Passkey">
                  </div>

                  <div class="block mx-1 mt-4">
                    <label class="text-xs mb-1 block ">Secret Key</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedPBConf.secret_key" placeholder="PayBill Passkey">
                  </div>

                  <div class="block mx-1 mt-4">
                    <label class="text-xs mb-1 block ">Consumer Key</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedPBConf.consumer_key" placeholder="PayBill Passkey">
                  </div>
                </div>

                <div class="gap-4 block text-sm text-center mt-10">
                  <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20"
                          @click="editConfig(1)"
                          id="addMember">
                    <vue-loaders-ball-beat color="red" scale="1" v-show="isLoading"></vue-loaders-ball-beat>
                    Save {{ selectedPBConf.type_name }} Configs
                  </button>
                </div>

              </div>

            </div>

          </div>
        </div>
      </div>
    </div>
    <!--  End PayBill  -->

    <!-- Communications Config -->
    <div v-if="configTypeId==='2'" class="rounded-lg bg-white shadow-lg mx-3 px-6 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2"> Sender ID</th>
          <th class="py-2"> SMTP Username</th>
          <th class="py-2"> SMTP Host</th>
          <th class="py-2"> SMTP Port</th>
          <th class="py-2 text-center">Status</th>
          <th class="py-2 text-center">Date</th>
          <th class="text-center">Action</th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in configList" :key="item.id">
          <td class="py-2 "><strong>{{ item.sender_id }}</strong></td>
          <td class="py-2 "><strong>{{ item.smtp_host }}</strong></td>
          <td class="py-2 "><strong>{{ item.smtp_username }}</strong></td>
          <td class="py-2 "><strong>{{ item.smtp_port }}</strong></td>
          <td class="py-2 text-center ">
            <div v-if="parseInt(item.status) === 1" class="action-button"
                 style="background-color: green; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              Active
            </div>
            <div v-else-if="parseInt(item.status) === 3" class="action-button"
                 style="background-color: red; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              In-Active
            </div>

          </td>
          <td class="px-2 text-center ">
            <span>{{ moment(item.created).format('llll') }}</span>
          </td>
          <td class="py-2 text-center relative w-24">
            <div class="relative inline-block">
              <button
                  v-if="parseInt(item.status) === 1"
                  class="px-3 py-1 flex items-center space-x-1"
                  @click="toggleDropdown(index)">

                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <button v-else class="px-3 py-1 flex items-center space-x-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#808080" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 230px; text-align: left;">
                <ul class="py-2">

                  <li @click="toggleCommunicationsConfig(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">View</a>
                  </li>


                </ul>
              </div>

            </div>
          </td>

        </tr>
        </tbody>
      </table>

      <!--  Communications Config Modal  -->
      <div v-if="commsModalOpen"
           class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
           :class="{ 'opacity-100 pointer-events-auto': commsModalOpen, 'opacity-0 pointer-events-none': !commsModalOpen }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!--Title -->
            <div class="flex justify-between items-center pb-3">
              <p class="text-2xl font-bold">{{ selectedCommConf.sender_id }} </p>
              <div class="modal-close cursor-pointer z-50" @click="commsModalOpen = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                     viewBox="0 0 18 18">
                  <path
                      d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                </svg>
              </div>
            </div>
            <!--Body -->
            <div class="w-full ">
              <div class="px-3 py-3">
                <div class="grid grid-cols-2 mt-4 ">
                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">Sender ID</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedCommConf.sender_id" placeholder="Jenga">
                  </div>


                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">Help Line</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedCommConf.help_line" placeholder="07xx xxx xxx">
                  </div>

                </div>

                <div class="grid grid-cols-2 mt-4 ">
                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">SMTP Host</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedCommConf.smtp_host" placeholder="smtp.xxxx.com">
                  </div>

                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">SMTP Username</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedCommConf.smtp_username" placeholder="<EMAIL>">
                  </div>
                </div>

                <div class="grid grid-cols-2 mt-4 ">
                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">SMTP Port</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedCommConf.smtp_port" placeholder="977">
                  </div>

                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">SMTP Password</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedCommConf.smtp_password" placeholder="07xx xxx xxx">
                  </div>

                </div>

                <div class="gap-4 block text-sm text-center mt-10">
                  <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20"
                          @click="editConfig(2)"
                          id="addMember">
                    <vue-loaders-ball-beat color="red" scale="1" v-show="isLoading"></vue-loaders-ball-beat>
                    Save {{ selectedPBConf.type_name }} Configs
                  </button>
                </div>

              </div>

            </div>

          </div>
        </div>
      </div>
    </div>
    <!-- End Communications Config -->

    <!-- App Display Configs -->
    <div v-if="configTypeId==='3'" class="rounded-lg bg-white shadow-lg mx-3 px-6 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">Account No</th>
          <th class="py-2">App Name</th>
          <th class="py-2">Color Scheme</th>
          <th class="py-2">Approval Time</th>
          <th class="py-2">TnC link</th>
          <th class="py-2 text-center">Status</th>
          <th class="py-2 text-center">Action</th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in configList" :key="item.id">

          <td class="py-2"><strong>{{ item.account_number }}</strong></td>
          <td class="py-2">
            <span v-if="!item.app_name">{{ 'Not yet set' }}</span>
            <strong v-else>{{ item.app_name }}</strong>
          </td>

          <td class="py-2">
            <span v-if="!item.color_scheme">{{ 'Not yet set' }}</span>
            <span v-else>{{ item.color_scheme }}</span>
          </td>

          <td class="px-2"><span>{{ item.approval_in_hours }} hour(s)</span></td>
          <td class="px-2"><span>{{ item.tnc_url }}</span></td>
          <td class="py-2 text-center">
            <div v-if="parseInt(item.status) === 1" class="action-button"
                 style="background-color: green; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              Active
            </div>
            <div v-else-if="parseInt(item.status) === 0" class="action-button"
                 style="background-color: red; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              In-Active
            </div>
          </td>

          <td class="py-2 text-center relative w-24">
            <div class="relative inline-block">
              <button
                  v-if="parseInt(item.status) !== 0"
                  class="px-3 py-1 flex items-center space-x-1"
                  @click="toggleDropdown(index)">

                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <button v-else class="px-3 py-1 flex items-center space-x-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#808080" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 230px; text-align: left;">
                <ul class="py-2">

                  <li @click="toggleAppDisplayConfigs(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">View App Config</a>
                  </li>

                </ul>
              </div>

            </div>
          </td>

        </tr>
        </tbody>
      </table>

      <!--  App Display Config Modal  -->
      <div v-if="appDisplayConfigModalOpen"
           class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
           :class="{ 'opacity-100 pointer-events-auto': appDisplayConfigModalOpen, 'opacity-0 pointer-events-none': !appDisplayConfigModalOpen }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!--Title -->
            <div class="flex justify-between items-center pb-3">
              <p class="text-2xl font-bold">{{ selectedAppDisplayConf.type_name }} App Display Config </p>
              <div class="modal-close cursor-pointer z-50" @click="appDisplayConfigModalOpen = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                     viewBox="0 0 18 18">
                  <path
                      d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                </svg>
              </div>
            </div>
            <!--Body -->
            <div class="w-full ">
              <div class="px-3 py-3">

                <div class="grid grid-cols-2 mt-4 ">
                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">App Name</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedAppDisplayConf.client_name" disabled>
                  </div>

                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">Account Number</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedAppDisplayConf.account_number" disabled>
                  </div>

                </div>

                <div class="grid grid-cols-2 mt-4 ">
                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">App Name</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedAppDisplayConf.app_name" placeholder="Salo Plus+">
                  </div>

                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">Logo URL</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedAppDisplayConf.logo_url" placeholder="https://www.logos.com/app-logo">
                  </div>

                </div>

                <div class="grid grid-cols-2 mt-4 ">
                  <!--                  <div class="block  mx-1 ">
                                      <label class="text-xs mb-1 block ">Logo URL</label>
                                      <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                                             v-model="selectedAppDisplayConf.logo_url" placeholder="['#CD7642','#FF3D42']">
                                    </div>-->

                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">Terms & Conditions URL</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedAppDisplayConf.tnc_url" placeholder="https://www.logos.com/app-logo">
                  </div>

                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">Approval in hours</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number"
                           v-model="selectedAppDisplayConf.approval_in_hours" placeholder="12">
                  </div>

                </div>

                <div class="gap-4 block text-sm text-center mt-10">
                  <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20"
                          @click="editConfig(3)"
                          id="addMember">
                    <vue-loaders-ball-beat color="red" scale="1" v-show="isLoading"></vue-loaders-ball-beat>
                    Save Configs
                  </button>
                </div>

              </div>

            </div>

          </div>
        </div>
      </div>

    </div>
    <!-- End App Display Configs -->

    <!-- Credit Referencing Configs -->
    <div v-if="configTypeId==='4'" class="rounded-lg bg-white shadow-lg mx-3 px-6 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">Account No</th>
          <th class="py-2">Client Name</th>
          <th class="py-2">Credit Score</th>
          <th class="py-2">Borrow Dates</th>
          <th class="py-2 text-center">Can ISSUE loan</th>
          <th class="py-2 text-center">Action</th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in configList" :key="item.id">

          <td class="py-2"><strong>{{ item.account_number }}</strong></td>

          <td class="py-2">
            <span v-if="!item.client_name">{{ 'Not yet set' }}</span>
            <strong v-else>{{ item.client_name }}</strong>
          </td>

          <td class="py-2">
            <strong>{{ item.currency }}. {{
                formatCurrency(parseFloat(item.max_total_loans).toFixed(2))
              }}</strong>
            <br>
          </td>

          <td class="py-2 ">
            <strong>{{ item.open_date }}{{ getDateSuffix(item.open_date) }} - {{
                item.close_date
              }}{{ getDateSuffix(item.close_date) }}</strong>
          </td>

          <td class="py-2 text-center">
            <div v-if="parseInt(item.can_issue_loans) === 1" class="action-button"
                 style="background-color: green; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              YES
            </div>
            <div v-else-if="parseInt(item.can_issue_loans) === 0" class="action-button"
                 style="background-color: red; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              NO
            </div>
          </td>

          <td class="py-2 text-center relative w-24">
            <div class="relative inline-block">
              <button
                  v-if="parseInt(item.status) !== 0"
                  class="px-3 py-1 flex items-center space-x-1"
                  @click="toggleDropdown(index)">

                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <button v-else class="px-3 py-1 flex items-center space-x-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#808080" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 230px; text-align: left;">
                <ul class="py-2">

                  <li @click="toggleCreditReferencingConfigs(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">View Credit Ref Config</a>
                  </li>

                  <li @click="viewIPRSToken(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">View IPRS & CRB Tokens</a>
                  </li>

                </ul>
              </div>

            </div>
          </td>

        </tr>
        </tbody>
      </table>

      <!-- Credit Referencing Config Modal  -->
      <div v-if="creditRefModalOpen"
           class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
           :class="{ 'opacity-100 pointer-events-auto': creditRefModalOpen, 'opacity-0 pointer-events-none': !creditRefModalOpen }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!--Title -->
            <div class="flex justify-between items-center pb-3">
              <p class="text-2xl font-bold">{{ selectedCreditRefConf.type_name }} Credit Referencing </p>
              <div class="modal-close cursor-pointer z-50" @click="creditRefModalOpen = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                     viewBox="0 0 18 18">
                  <path
                      d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                </svg>
              </div>
            </div>
            <!--Body -->
            <div class="w-full ">
              <div class="px-3 py-3">

                <div class="grid grid-cols-2 mt-4 ">
                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">Organisation Name</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedCreditRefConf.client_name" disabled>
                  </div>

                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">Account Number</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedCreditRefConf.account_number" disabled>
                  </div>

                </div>

                <div class="grid grid-cols-2 mt-4 ">
                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">App Name</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedCreditRefConf.app_name" placeholder="Salo Plus+">
                  </div>

                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">Max Total Loans ({{ selectedCreditRefConf.currency }})</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedCreditRefConf.max_total_loans" placeholder="https://www.logos.com/app-logo">
                  </div>
                </div>

                <div class="grid grid-cols-2 mt-4 ">
                  <div class="block  mx-1 ">
                    <label class="text-xs mb-1 block ">Open Date</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                           v-model="selectedCreditRefConf.open_date" placeholder="Salo Plus+">
                  </div>

                  <div class="block mx-1">
                    <label class="text-xs mb-1 block ">Close Date</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                           v-model="selectedCreditRefConf.close_date" placeholder="https://www.logos.com/app-logo">
                  </div>
                </div>

                <div class="gap-4 block text-sm text-center mt-10">
                  <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20"
                          @click="editConfig(4)"
                          id="addMember">
                    <vue-loaders-ball-beat color="red" scale="1" v-show="isLoading"></vue-loaders-ball-beat>
                    Save Credit Ref Configs
                  </button>
                </div>

              </div>

            </div>

          </div>
        </div>
      </div>

      <!-- IPRS and CRB Config Modal  -->
      <div v-if="viewIPRSTModalOpen"
           class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
           :class="{ 'opacity-100 pointer-events-auto': viewIPRSTModalOpen, 'opacity-0 pointer-events-none': !viewIPRSTModalOpen }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!--Title -->
            <div class="flex justify-between items-center pb-3">
              <p class="text-2xl font-bold"> IPRS & CRB Tokens </p>
              <div class="modal-close cursor-pointer z-50" @click="viewIPRSTModalOpen = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                     viewBox="0 0 18 18">
                  <path
                      d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                </svg>
              </div>
            </div>
            <!--Body -->
            <div class="w-full ">
              <div class="px-3 py-3">

                <div class="block  mx-1 ">
                  <label class="text-xs mb-1 block ">IPRS Token</label>
                  <span>{{ selectedCreditRefConf.iprs_token }}</span>
                </div>

                <div class="block mx-1 mt-3">
                  <label class="text-xs mb-1 block ">CRB Token</label>
                  <span>{{ selectedCreditRefConf.crb_token }}</span>
                </div>

              </div>

            </div>

          </div>
        </div>
      </div>

    </div>
    <!-- End Credit Referencing Configs -->


  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import moment from "moment-timezone";
import country from 'country-list-js';
import VueDatePicker from "@vuepic/vue-datepicker";

export default {
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      account_number: '',
      moreParams: {
        account_number: '',
      },
      organisations: [],

      configTypeId: null,
      config: {
        offset: "",
        limit: "",
        sort: "",
        export: "",
        client_id: "",
        type_id: "",
      },
      configList: [],

      configs: {
        currency_code: "",
        max_total_loans: "",
        can_issue_loans: false,
        service_fee: "",
        iprs_token: "",
        crb_token: "",
        app_name: null,
        logo_url: null,
        color_scheme: null,
        open_date: "",
        close_date: "",
        b2c_merchant_code: "",
        c2b_merchant_code: "",
        b2c_credentials: null,
        bulk_short_code: "",
        smtp_host: "",
        smtp_port: "",
        smtp_username: "",
        smtp_password: null,
        help_line: null
      },

      configMenu: [
        {text: 'Select One', value: 0},
        {text: 'PayBill Configs', value: 1},
        {text: 'Communication Configs', value: 2},
        {text: 'App Display Configs', value: 3},
        {text: 'Credit Referencing Configs', value: 4},
        {text: 'Loan Configs', value: 5},
      ],
      selectedConfig: null,

      set_service_fee: false,
      set_communication_channels: false,
      set_loan_limits: false,
      set_open_dates: false,

      //
      payBillModalOpen: false,
      selectedPBConf: {},
      pBConfPayload: {
        paybill_config_id: "",
        paybill_type_id: "",
        paybill_number: "",
        paybill_route: "",
        paybill_wallet_status: "",
        paybill_credentials: {
          initiator_name: "",
          initiator_password: "",
          pass_key: "",
          secret_key: "",
          consumer_key: ""
        }
      },

      //
      appDisplayConfigModalOpen: false,
      selectedAppDisplayConf: {
        trx_count: "",
        setting_id: "",
        client_name: "",
        account_number: "",
        app_name: "",
        logo_url: "",
        color_scheme: "",
        approval_in_hours: "",
        tnc_url: "",
        status: ""

      },

      //
      commsModalOpen: false,
      selectedCommConf: {},
      commPayload: {
        setting_id: "",
        smtp_password: "",
        smtp_username: "",
        smtp_host: "",
        smtp_port: "",
        sender_id: "",
        helpline: ""
      },

      //
      viewIPRSTModalOpen: false,
      viewCRBTModalOpen: false,
      //
      creditRefModalOpen: false,
      selectedCreditRefConf: {},
      credit_ref_payload: {
        account_number: "",
        currency: "",
        open_date: "",
        close_date: "",
        iprs_token: "",
        crb_token: ""
      },

      statuses: [
        {text: 'Active', value: 1},
        {text: 'Pending Approval', value: 2},
        {text: 'Suspended', value: 3},
      ],
    }

  },
  components: {
    Loading
  },
  watch: {

    selectedConfig(newVal, oldVal) {
      if (newVal !== oldVal && newVal !== "") {
        if (this.selectedConfig.value !== 0) {
          this.setOrgConfig(newVal);
        }
      }
    }
  },
  async mounted() {
    await this.setOrganisations()
    this.selectedConfig = this.configMenu[0]
  },
  computed: {
    filteredOrganizations() {
      return this.organisations.filter(org => org.text.toLowerCase().includes(this.searchClient.toLowerCase()));
    }
  },
  methods: {
    ...mapActions(["getMerchants", "getConfigs", "updateConfigs", "updateChannels"]),

    //
    toggleSetClientWalletSettings(config) {
      this.closeDropdown()
      this.selectedPBConf = config

      // console.log("selectedPBConf: ", JSON.stringify(this.selectedPBConf))
      this.payBillModalOpen = true
    },
    //
    toggleCommunicationsConfig(config) {
      this.closeDropdown()
      this.selectedCommConf = config

      // console.log("selectedCommConf: ", JSON.stringify(this.selectedCommConf))
      this.commsModalOpen = true
    },
    //
    toggleAppDisplayConfigs(config) {
      this.closeDropdown()
      this.selectedAppDisplayConf = config

      // console.log("selectedAppDisConf: ", JSON.stringify(this.selectedAppDisConf))
      this.appDisplayConfigModalOpen = true
    },
    //
    toggleCreditReferencingConfigs(config) {
      this.closeDropdown()
      this.selectedCreditRefConf = config

      // console.log("selectedCreditRefConf: ", JSON.stringify(this.selectedCreditRefConf))
      this.creditRefModalOpen = true
    },
    //
    viewIPRSToken(config) {
      this.selectedCreditRefConf = config
      // console.log("viewIPRSToken: ", JSON.stringify(this.selectedCreditRefConf))
      this.viewIPRSTModalOpen = true
      this.closeDropdown()
    },

    //
    setOrg(item) {
      this.searchDropdownPlaceholder = item.text
      this.searchDropdown = false
      //
      this.config.client_id = item.id
      this.selectedConfig = this.configMenu[0]
      console.log("setOrg form::''", JSON.stringify(this.config))

    },

    //
    setOrgConfig(item) {
      this.configTypeId = item.value.toString()
      this.config.end_point = this.getConfigType(item.value)

      console.log("setOrgConfig form", JSON.stringify(this.config))

      this.setConfigs()
    },

    //
    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },

    //
    async setConfigs() {
      let app = this
      app.isLoading = true

      let response = await this.getConfigs(app.config)

      console.log("getConfigs response: ", JSON.stringify(response))

      if (response.status === 200) {
        app.configList = response.message.data
        app.total = response.message.total_count

        app.showDropdown = []
        for (let i = 0; i < app.configList.length; i++) {
          app.showDropdown.push(false)
        }
      }

      app.isLoading = false

    },

    //
    async editConfig(urlType) {
      console.log("editConfig: ", urlType)
      // return;
      let app = this
      let payload = {}
      // payload.end_point = this.getConfigEndPoint(urlType)
      if (urlType === 1) {
        this.pBConfPayload.paybill_config_id = this.selectedPBConf.config_id ?? ""
        this.pBConfPayload.paybill_type_id = this.selectedPBConf.type_id ?? ""
        this.pBConfPayload.paybill_number = this.selectedPBConf.paybill_number ?? ""
        this.pBConfPayload.paybill_route = this.selectedPBConf.service_route ?? ""
        this.pBConfPayload.paybill_status = this.selectedPBConf.paybill_status ?? ""
        this.pBConfPayload.paybill_wallet_status = this.selectedPBConf.wallet_status ?? ""

        this.pBConfPayload.paybill_credentials.initiator_name = this.selectedPBConf.user_name ?? ""
        this.pBConfPayload.paybill_credentials.initiator_password = this.selectedPBConf.b2c_credentials ?? ""
        this.pBConfPayload.paybill_credentials.pass_key = this.selectedPBConf.pass_key ?? ""
        this.pBConfPayload.paybill_credentials.secret_key = this.selectedPBConf.secret_key ?? ""
        this.pBConfPayload.paybill_credentials.consumer_key = this.selectedPBConf.consumer_key ?? ""
        this.pBConfPayload.end_point = this.getConfigEndPoint(urlType)

        payload = this.pBConfPayload
        //
      } else if (urlType === 2) {
        this.commPayload.sender_id = this.selectedCommConf.sender_id ?? ""
        this.commPayload.setting_id = this.config.client_id ?? ""
        this.commPayload.smtp_port = this.selectedCommConf.smtp_port ?? ""
        this.commPayload.smtp_host = this.selectedCommConf.smtp_host ?? ""
        this.commPayload.smtp_username = this.selectedCommConf.smtp_username ?? ""
        this.commPayload.smtp_password = this.selectedCommConf.smtp_password ?? ""
        this.commPayload.helpline = this.selectedCommConf.help_line ?? ""
        this.commPayload.end_point = this.getConfigEndPoint(urlType)

        payload = this.commPayload
        //
      } else if (urlType === 4) {
        //
        this.credit_ref_payload.currency_id = this.selectedCreditRefConf.currency_id ?? ""
        this.credit_ref_payload.iprs_token = this.selectedCreditRefConf.iprs_token ?? ""
        this.credit_ref_payload.crb_token = this.selectedCreditRefConf.crb_token ?? ""
        this.credit_ref_payload.open_date = this.selectedCreditRefConf.open_date ?? ""
        this.credit_ref_payload.close_date = this.selectedCreditRefConf.close_date ?? ""
        this.credit_ref_payload.account_number = this.selectedCreditRefConf.account_number ?? ""

        this.credit_ref_payload.end_point = this.getConfigEndPoint(urlType)
        payload = this.credit_ref_payload
      }

      console.log("CONFIG PAYLOAD:  ", JSON.stringify(payload))

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this updates this paybill account!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          return await this.updateConfigs(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              app.resetModals()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    // Reset Modals
    resetModals() {
      this.payBillModalOpen = false
      this.commsModalOpen = false
      this.appDisplayConfigModalOpen = false
      this.creditRefModalOpen = false
      this.viewIPRSTModalOpen = false
    },

    // Fetch and set Organisations to UI
    async setOrganisations() {
      let app = this
      app.isLoading = true;
      let response = await this.getMerchants({limit: 100})
      if (response.status === 200) {
        response.message.data.forEach(function (item) {
          let _organisations = {text: item.client_name, value: item.client_account, id: item.client_id}
          app.organisations.push(_organisations)

        })

        app.setOrg(app.organisations[0])

      } else {
      }
      app.isLoading = false
    },

    //
    onOrganisationSelected(selectedItem) {
      // Perform actions based on the selected Organisations

      console.log("Selected Organisations B4:", selectedItem);
      if (parseInt(selectedItem) === -1) {
        this.moreParams.account_number = ""
      } else {
        this.moreParams.account_number = selectedItem
      }
      console.log("Selected Organisations:", this.moreParams.account_number);
      // You can perform any other actions you need here
      // this.setConfigs()
    },

    getConfigType(c) {
      let conf = ""
      if (c === 1) {
        conf = "payment_config"
      } else if (c === 2) {
        conf = "communications"
      } else if (c === 3) {
        conf = "system_config"
      } else if (c === 4) {
        conf = "credit_scoring"
      } else if (c === 5) {
        // conf = "credit_scoring"
      }
      return conf
    },

    getConfigEndPoint(c) {
      let endpoint = ""
      if (c === 1) {
        endpoint = "update/wallet"
      } else if (c === 2) {
        endpoint = "update/channels"
      } else if (c === 3) {
        endpoint = "update/app_configs"
      } else if (c === 4) {
        endpoint = "update/settings"
      } else if (c === 5) {
        // endpoint = "update/credit_scoring"
        // endpoint = "update/loan_config"
      }
      return endpoint
    },


    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    //
    formatCurrency(number) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    //
    getDateSuffix(day) {
      let day_suffix = 'th';
      if (day < 10 || day > 20) {
        switch (day % 10) {
          case 1:
            day_suffix = 'st';
            break;
          case 2:
            day_suffix = 'nd';
            break;
          case 3:
            day_suffix = 'rd';
            break;
          default:
            break;
        }
      }

      return day_suffix
    },

  },
}
</script>
