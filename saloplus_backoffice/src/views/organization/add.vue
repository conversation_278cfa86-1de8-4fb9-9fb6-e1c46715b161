<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Add Organisation</div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <!--Company Name-->
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium ">Company Name</label>
        <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.company_name"
               placeholder="MKenya LTD">
      </div>

      <!--Company Phone, Company Email -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Company Email</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="email"
                 v-model="form.company_email" placeholder="<EMAIL>">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Company Phone Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                 v-model="form.company_phone" placeholder="07xx xxx xxx">
        </div>
      </div>

      <!--Company Address -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium ">Company Address</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.company_address" placeholder="**********, Nairobi">
        </div>

        <!--Contact Person -->
        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium ">Contact Person (Phone No)</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.contact_person" placeholder="07xx xxx xxx">
        </div>
      </div>

      <!--Buttons-->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'organisations'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20"
                @click="addOrganisation"
                id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Submit
        </button>
      </div>


    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import moment from "moment-timezone";
import country from 'country-list-js';
import VueDatePicker from "@vuepic/vue-datepicker";

export default {
  data() {
    return {
      // date: null,
      loading: false,
      form: {
        dial_code: "254",
        company_name: null,
        company_phone: null,
        company_address: null,
        company_email: null,
        contact_person: null
      },

    }
  },
  components: {
    Loading
  },
  methods: {
    ...mapActions(["addMerchant"]),

    //
    checkFormValidity(payload) {
      for (const key in payload) {
        if (payload[key] === null || payload[key] === '') {
          return false;
        }
      }
      return true;
    },

    //
    async addOrganisation() {
      let app = this
      if (!this.checkFormValidity(app.form)) {
        // Handle the case where there are invalid elements
        // console.log("There are null or empty elements in the form.");
        app.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: `Fill in all data.`
        });
        return
      }

      const payload = app.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new Organisation!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.addMerchant(payload)
        },
      })
          .then(async (result) => {
            // console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              await this.$router.push({name: 'merchant'})
              app.resetForm()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },
  },
}
</script>
