<template>

  <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">

    <div class="flex font-medium p-6 pb-1 border-b gap-4 mb-5">
      <div class="flex-shrink font-medium">
        <i v-if="!this.$store.state.isSideMenuOpen" class="fa fa-bars text-black text-lg" @click="toggleSideM"></i>
        <i v-else class="fa fa-close text-black text-lg" @click="toggleSideM"></i>
      </div>

      <div class="flex-grow font-medium pb-2"> Customer Search</div>
    </div>

    <div class="block px-3">
      <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="phone"
             placeholder="Enter Phone Number to Search" @keyup.enter="searchPhone">
    </div>

    <!-- Customer Details -->
    <div class="block px-3 py-3 ">
      <div class="flex flex-col md:flex-row md:space-x-4" v-if="customer !== null">

        <div class="w-full md:w-1/2">
          <div class="bg-gray-200 rounded-lg shadow-md mb-5 " style="height: 270px">
            <div class="text-center pt-0" v-if="true">
              <img class="w-20 h-20 rounded-full mx-auto mb-4" src="../../assets/logo.png" alt="Avatar">
              <h3 class="text-3xl font-semibold mb-1">{{ customer.name }}</h3>
              <h4 class="text-gray-500 text-xl mb-4">+{{ customer.msisdn }} | {{ customer.loan_number }} |
                {{ customer.employee_number }} | </h4>
              <h4 class="text-gray-500 text-xl mb-4">{{ customer.client_name }} </h4>
            </div>

            <div class="text-center pt-0" v-else>
              <img class="w-20 h-20 rounded-full mx-auto mb-4" src="../../assets/logo.png" alt="Avatar">
              <h3 class="text-3xl font-semibold mb-1 text-gray-300"> John Doe </h3>
              <h5 class="text-xl mb-4 text-gray-300">Kenyan</h5>
            </div>

            <div class="border-t border-black mt-5">
              <!-- Buttons -->
              <div class="flex justify-between px-5 py-2 mt-5">
                <button v-if="parseInt(customer.marketing_status) === 1"
                        class="bg-orange-500 hover:bg-orange-400 text-white font-semibold py-2 px-4 rounded-full w-2/5"
                        @click.prevent="disableBulk">
                  Disable Bulk
                </button>

                <button v-else
                        class="bg-green-500 hover:bg-green-400 text-white font-semibold py-2 px-4 rounded-full w-2/5"
                        @click.prevent="enableBulk">
                  Enable Bulk
                </button>

                <button class="bg-red-500 hover:bg-red-400 text-white font-semibold py-2 px-4 rounded-full w-2/5"
                        @click="toggleBlockUnblock">
                  Block
                </button>
              </div>
            </div>

          </div>
        </div>

        <div class="w-full md:w-1/2 ">
          <div class="bg-gray-200 shadow-xl rounded-md pb-3 " style="height: 270px">

            <div class="px-4 py-2 md:px-6">
              <h2 class="text-lg font-medium text-gray-800 mb-4">Details</h2>

              <div class="person-transactions mb-2 flex justify-between ">
                <span class="font-medium">Max Approved Loan</span>
                <h6 class="person-transactions">
                  Kes <span class="pl-1 amount-transacted">{{ accounting.format(customer.max_approved, 2) }}</span>
                </h6>
              </div>

              <div class="person-transactions mb-2 flex justify-between ">
                <h5 class="font-medium">Credit Score</h5>
                <h6 class="person-transactions">
                  <span class="pl-1 amount-transacted">{{ customer.credit_score }}</span>
                </h6>
              </div>

              <div class="person-transactions mb-2 flex justify-between ">
                <h5 class="font-medium">Employment</h5>
                <h6 class="person-transactions">
                  <span class="pl-1 amount-transacted">{{ moment(customer.employment_date).format('lll') }}</span>
                </h6>
              </div>

              <div class="person-transactions flex justify-between ">
                <h5 class="font-medium">Last Login Date</h5>
                <h6 class="person-transactions"> <span class="pl-1 amount-transacted">
                   {{ customer.last_login_date ? moment(customer.last_login_date).format('lll') : '-' }} </span>
                  <span> {{ }} </span>
                </h6>
              </div>
            </div>


            <h5 class="font-medium w-full text-center">Balances</h5>

            <div class="card-footer border-top px-6  border-t border-black pt-1">
              <div class="person-transactions mb-1 flex justify-between ">
                <span class="font-medium">Actual Balance</span>
                <h6 class="person-transactions">
                  Kes <span class="pl-1 amount-transacted">{{ accounting.format(customer.actual_balance, 2) }}</span>
                </h6>
              </div>

              <div class="person-transactions mb-1 flex justify-between ">
                <span class="font-medium">Loan Balance</span>
                <h6 class="person-transactions">
                  Kes <span class="pl-1 amount-transacted">{{ accounting.format(customer.loan_balance, 2) }}</span>
                </h6>
              </div>
            </div>

          </div>
        </div>

      </div>
    </div>

    <div v-if="showTables" class="block  py-4 rounded-lg bg-white shadow-lg mx-3 mb-6 border ">
      <!-- Tab buttons -->
      <div class="flex mb-4 pb-4 border-b rounded-lg bg-white shadow-sm">
        <button @click="fetchData('loan_requests')"
                :class="{ 'bg-blue-500 text-white': activeTab === 'loan_requests' }"
                class="flex-1 p-0 mx-2 border border-b-2 border-blue-500 rounded-full"
                style="height: 40px; align-content: center">
          Loan Requests
        </button>

        <button @click="fetchData('loan_repayments')"
                :class="{ 'bg-green-500 text-white': activeTab === 'loan_repayments' }"
                class="flex-1 p-0 mx-2 border border-b-2 border-green-500  rounded-full"
                style="height: 40px; align-content: center">
          Repayments
        </button>

        <button @click="fetchData('loan_limits') "
                :class="{ 'bg-orange-500 text-white': activeTab === 'loan_limits' }"
                class="flex-1 p-0 mx-2 border border-b-2 border-orange-500 rounded-full"
                style="height: 40px; align-content: center">
          Loan Limits
        </button>

        <button @click="fetchData('transactions') "
                :class="{ 'bg-yellow-500 text-white': activeTab === 'transactions' }"
                class="flex-1 p-0 mx-2 border border-b-2 border-yellow-500  rounded-full"
                style="height: 40px; align-content: center">
          Transactions
        </button>

        <button @click="fetchData('checkoff') "
                :class="{ 'bg-purple-500 text-white': activeTab === 'checkoff' }"
                class="flex-1 p-0 mx-2 border border-b-2 border-purple-500  rounded-full"
                style="height: 40px; align-content: center">
          Checkoff
        </button>

        <button @click="fetchData('kyc') "
                :class="{ 'bg-cyan-500 text-white': activeTab === 'kyc' }"
                class="flex-1 p-0 mx-2 border border-b-2 border-cyan-500  rounded-full"
                style="height: 40px; align-content: center">
          View KYC
        </button>

      </div>

      <!-- Tables -->
      <!-- Loan Requests -->
      <div v-if="activeTab === 'loan_requests'" class="rounded-lg bg-white shadow-sm mx-3 pb-2 border">

        <div class="grid grid-cols-3 mb-4">
          <div class="px-6 block">
            <label class="text-xs font-medium mb-1 mt-3 block">Filter By Request No.</label>
            <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                   v-model="loan_req_params.loan_request_number"
                   placeholder="Enter Reference Number" @keyup.enter="setLoanRequests">
          </div>
        </div>

        <div v-if="loan_requests.length>0 " class="px-6">
          <table class="w-full mb-4 table">
            <thead class="border-b-2 text-xs text-left">
            <tr class="table-row">
              <th class="py-2">Loan Request No</th>
              <th class="text-center py-2">Product Name</th>
              <th class="text-center py-2">Requested Amount</th>
              <th class="text-center py-2">Approved Amount</th>
              <th class="text-center py-2"> Status</th>
              <th class="text-center py-2">Date</th>
              <th class="text-center">Actions</th>
              <th></th>
            </tr>
            </thead>
            <tbody class="text-xs text-gray-600 divide-y">
            <tr v-for="(loan_request, index) in loan_requests" :key="loan_request.client_ac">
              <td class="py-2 font-medium">
                <strong>{{ loan_request.req_number }}</strong>
              </td>
              <td class="text-center py-2"> {{ loan_request.product_name || 'N/A' }}</td>
              <td class="text-center py-2">{{
                  formatCurrency(parseFloat(loan_request.requested_amount).toFixed(2))
                }}
              </td>
              <td class="text-center py-2">{{
                  formatCurrency(parseFloat(loan_request.approved_amount).toFixed(2))
                }}
              </td>
              <td class="text-center py-2">
                <button v-if="parseInt(loan_request.approval_status) === 1"
                        class="inline-block px-3 py-1 rounded-md text-white bg-green-500">
                  Fully Paid
                </button>

                <button v-else-if="parseInt(loan_request.approval_status) === 2"
                        class="inline-block px-3 py-1 rounded-md text-white bg-orange-500">
                  Partially Paid
                </button>

                <button v-else-if="parseInt(loan_request.approval_status) === 3"
                        class="inline-block px-3 py-1 rounded-md text-white bg-red-500">
                  Rejected
                </button>

                <button v-else-if="parseInt(loan_request.approval_status) === 4"
                        class="inline-block px-3 py-1 rounded-md text-white bg-purple-500">
                  Unverified
                </button>

                <button v-else-if="parseInt(loan_request.approval_status) === 5"
                        class="inline-block px-3 py-1 rounded-md text-white bg-teal-500">
                  Pending
                </button>

                <button v-else-if="parseInt(loan_request.approval_status) === 6"
                        class="inline-block px-3 py-1 rounded-md text-white bg-brown-500">
                  Unpaid
                </button>

                <button v-else-if="parseInt(loan_request.approval_status) === 7"
                        class="inline-block px-3 py-1 rounded-md text-white bg-yellow-500">
                  Failure
                </button>

                <button v-else-if="parseInt(loan_request.approval_status) === 8"
                        class="inline-block px-3 py-1 rounded-md text-white bg-green-500">
                  Approved
                </button>
              </td>
              <td class="text-center py-2">
                {{ moment(loan_request.created).format('lll') }}
              </td>
              <td class="py-2 text-center relative w-24">
                <div class="relative inline-block">
                  <button
                      v-if="parseInt(loan_request.approval_status) === 2 || parseInt(loan_request.approval_status) === 5 || parseInt(loan_request.approval_status) === 4"
                      class="px-3 py-1 flex items-center space-x-1"
                      @click="toggleLRDropdown(index)">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                         stroke="#000000" class="w-6 h-6">
                      <path stroke-linecap="round" stroke-linejoin="round"
                            d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </button>
                  <button
                      v-else
                      class="px-3 py-1 flex items-center space-x-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                         stroke="#808080" class="w-6 h-6">
                      <path stroke-linecap="round" stroke-linejoin="round"
                            d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </button>

                  <div v-if="showLRDropdown[index]"
                       class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10">
                    <ul class="py-2">
                      <li v-if="parseInt(loan_request.approval_status) === 5"
                          @click="toggleApproveLR(loan_request.req_number, loan_request.requested_amount, 1)">
                        <a class="block px-16 py-2 cursor-pointer hover:bg-green-200">Approve</a>
                      </li>
                      <li v-if="parseInt(loan_request.approval_status) === 5"
                          @click="toggleApproveLR(loan_request.req_number, loan_request.requested_amount, 0)">
                        <a class="block px-16 py-2 cursor-pointer hover:bg-red-200">Reject</a>
                      </li>
                      <li v-if="parseInt(loan_request.approval_status) === 4"
                          @click="requestTAN(loan_request.req_number)">
                        <a class="block px-16 py-2 cursor-pointer hover:bg-purple-200">Resend Code</a>
                      </li>
                      <li v-if="parseInt(loan_request.approval_status) === 2"
                          @click="viewRepayments(loan_request)">
                        <a class="block px-16 py-2 cursor-pointer hover:bg-purple-200">View Repayments</a>
                      </li>
                    </ul>
                  </div>

                </div>
              </td>
            </tr>
            </tbody>
          </table>

          <div class="flex w-full text-xs items-center" v-show="lr_total>lr_limit">
            <div class="flex-shrink" v-show="lr_offset === 0">{{ lr_offset + 1 }} to {{ lr_limit }} of {{
                lr_total
              }}
            </div>
            <div class="flex-shrink" v-show="lr_offset > 0">{{ ((lr_offset * lr_limit) - lr_limit) + 1 }} to
              {{ lr_limit * lr_offset < lr_total ? lr_limit * lr_offset : lr_total }} of {{ lr_total }}
            </div>
            <div class="flex-grow text-right" v-show="lr_total > lr_limit">
              <div class="inline-block bg-white border rounded-md divide-x">
                <button class="p-2 px-3" v-show="Math.ceil(lr_total/lr_limit) > 1 && lr_offset > 1"
                        @click="gotToPageLR(lr_offset-1)">
                  &larr;
                </button>
                <button class="p-2 px-4" :class="{'bg-gray-100':lr_offset===1}" @click="gotToPageLR(1)">1</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':lr_offset===2}"
                        v-show="Math.ceil(lr_total/lr_limit) > 1"
                        @click="gotToPageLR(2)">2
                </button>
                <button class="p-2 px-4" :class="{'bg-gray-100':lr_offset===3}"
                        v-show="Math.ceil(lr_total/lr_limit) > 2"
                        @click="gotToPageLR(3)">3
                </button>
                <button class="p-2 px-4" v-show="Math.ceil(lr_total/lr_limit) > 3">...</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':lr_offset===lr_offset}"
                        v-show="Math.ceil(lr_total/lr_limit) > 3 && lr_offset >= 3" @click="gotToPageLR(lr_offset)">
                  {{ lr_offset }}
                </button>

                <button class="p-2 px-4" :class="{'bg-gray-100':lr_offset===Math.ceil(lr_total/lr_limit)}"
                        v-show="Math.ceil(lr_total/lr_limit) > 4" @click="gotToPageLR(Math.ceil(lr_total/lr_limit))">
                  {{ Math.ceil(lr_total / lr_limit) }}
                </button>
                <button class="p-2 px-3" v-show="(lr_offset*lr_limit) < total" @click="gotToPageLR(lr_offset+1)">
                  &rarr;
                </button>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="my-8 flex justify-center">
          No Results
        </div>

        <!-- Modal -->
        <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
             :class="{ 'opacity-100 pointer-events-auto': isAcceptRejectModelOpenLR, 'opacity-0 pointer-events-none': !isAcceptRejectModelOpenLR }">
          <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
          <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
            <div class="modal-content py-4 text-left px-6">
              <!-- Title -->
              <div class="flex justify-between items-center pb-3">
                <p class="text-2xl font-bold">Loan Review</p>
                <div class="modal-close cursor-pointer z-50" @click="isAcceptRejectModelOpenLR = false">
                  <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                       viewBox="0 0 18 18">
                    <path
                        d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                  </svg>
                </div>
              </div>
              <!-- Body -->
              <div class="mb-4">
                <label class="block text-sm font-medium">Please type
                  <strong class="text-blue-500">{{ label }}</strong> to {{ label }} this loan.</label>
                <input
                    :placeholder="label"
                    type="text"
                    v-model="status_description"
                    class="mt-1 p-2 border rounded-md w-full"
                />
              </div>
              <!-- Footer -->
              <div class="flex justify-end pt-2">
                <button @click="toggleApproveLR(1, 1, 1)"
                        class="px-4 py-2 bg-transparent rounded-lg text-blue-500 hover:bg-gray-100 hover:text-blue-400">
                  Cancel
                </button>
                <button @click="approveLR"
                        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400">
                  {{ capitalizeFirstLetter(label) }}
                </button>
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- Repayments -->
      <div v-if="activeTab === 'loan_repayments'" class="rounded-lg bg-white shadow-sm mx-3 pb-2 border">

        <!-- Table -->
        <!--      <GenericTable :loan_number="this.$store.state.loan_number" :request_number="request_number" v-show="this.$store.state.loan_number!==null"/>-->

        <div class="grid grid-cols-3 mb-4">
          <div class="px-6 block">
            <label class="text-xs font-medium mb-1 mt-3 block">Filter Repayment By Request No.</label>
            <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                   v-model="loan_repayments_params.request_number"
                   placeholder="Enter Reference Number" @keyup.enter="setLoanRepayments">
          </div>
        </div>

        <div v-if="loan_repayments.length>0" class="px-6">
          <table class="w-full mb-4 table">
            <thead class="border-b-2 text-xs text-left">
            <tr class="table-row">
              <th class="py-2">Transaction Code</th>
              <th class="py-2">Customer</th>
              <th class="py-2">Amount Borrowed</th>
              <th class="py-2">Amount Paid</th>
              <th class="py-2">Outstanding Balance</th>
              <th class="py-2">Repayment Source</th>
              <th class="py-2">Date</th>
            </tr>
            </thead>
            <tbody class="text-xs text-gray-600 divide-y">
            <tr v-for="item in loan_repayments" :key="item.id">
              <td class="py-2 font-medium">
                <strong>{{ item.trxn_code }}</strong>
              </td>

              <td class="py-2">
                <span>{{ item.payer ? item.payer.split('-')[1] : '' }}</span>
                <br>
                <span>+{{ item.payer ? item.payer.split('-')[0] : '' }}</span>
              </td>

              <td class="py-2">{{ item.currency_code ?? "KES" }}.
                {{ formatCurrency((parseFloat(item.repayment_amount)+parseFloat(item.reducing_balance)).toFixed(2)) }}
              </td>

              <td class="py-2">{{ item.currency_code ?? "KES" }}.
                {{ formatCurrency(parseFloat(item.repayment_amount).toFixed(2)) }}
              </td>

              <td class="py-2">{{ item.currency_code ?? "KES" }}.
                {{ formatCurrency(parseFloat(item.reducing_balance).toFixed(2)) }}
              </td>

              <td class="py-2">
                <strong>{{ item.repayment_source }}</strong>
              </td>

              <td class="py-2">
                <span> {{ moment(item.created).format('lll') }}</span>
              </td>

            </tr>
            </tbody>
          </table>

          <!--Pagination-->
          <div class="flex w-full text-xs items-center" v-show="lrps_total>lrps_limit">
            <div class="flex-shrink" v-show="lrps_offset === 0">{{ lrps_offset + 1 }} to {{ lrps_limit }} of {{
                lrps_total
              }}
            </div>
            <div class="flex-shrink" v-show="lrps_offset > 0">{{ ((lrps_offset * lrps_limit) - lrps_limit) + 1 }} to
              {{ lrps_limit * lrps_offset }} of
              {{ lrps_total }}
            </div>
            <div class="flex-grow text-right" v-show="lrps_total > lrps_limit">
              <div class="inline-block bg-white border rounded-md divide-x">
                <button class="p-2 px-3" v-show="Math.ceil(lrps_total/lrps_limit) > 1"
                        @click="gotToPageTrnx(lrps_offset-1)">
                  &larr;
                </button>
                <button class="p-2 px-4" :class="{'bg-gray-100':lrps_offset===1}" @click="gotToPageTrnx(1)">1</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':lrps_offset===2}"
                        v-show="Math.ceil(lrps_total/lrps_limit) > 1"
                        @click="gotToPageTrnx(2)">2
                </button>
                <button class="p-2 px-4" v-show="Math.ceil(lrps_total/lrps_limit) > 3">...</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':lrps_offset===lrps_offset}"
                        v-show="Math.ceil(lrps_total/lrps_limit) > 3 && lrps_offset >= 3"
                        @click="gotToPageTrnx(lrps_offset)">
                  {{ lrps_offset }}
                </button>

                <button class="p-2 px-4" :class="{'bg-gray-100':lrps_offset===Math.ceil(lrps_total/lrps_limit)}"
                        v-show="Math.ceil(lrps_total/lrps_limit) > 4"
                        @click="gotToPageTrnx(Math.ceil(lrps_total/lrps_limit))">
                  {{ Math.ceil(lrps_total / lrps_limit) }}
                </button>
                <button class="p-2 px-3" v-show="(lrps_offset*lrps_limit) < lrps_total"
                        @click="gotToPageTrnx(t_offset+1)">
                  &rarr;
                </button>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="my-8 flex justify-center">
          No Results
        </div>
      </div>

      <!-- Loan Limits -->
      <div v-if="activeTab === 'loan_limits'" class="rounded-lg bg-white shadow-sm mx-3 pb-2 border">
        <div class="grid grid-cols-3 mb-4">
          <div class="px-6 block">
            <label class="text-xs font-medium mb-1 mt-3 block">Filter By Request No.</label>
            <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                   v-model="loan_lim_params.request_number"
                   placeholder="Enter Reference Number" @keyup.enter="setLimitRequests">
          </div>
        </div>

        <div v-if="loan_limits.length>0 " class="px-6">
          <table class="w-full mb-4 table">
            <thead class="border-b-2 text-xs text-left">
            <tr class="table-row">
              <th class="py-2">Ref No</th>
              <th class="text-center py-2">Current Limit</th>
              <th class="text-center py-2">Requested Limit</th>
              <th class="text-center py-2">Status</th>
              <th class="text-center py-2">Date</th>
              <th class="text-center">Actions</th>
              <th></th>
            </tr>
            </thead>
            <tbody class="text-xs text-gray-600 divide-y">
            <tr v-for="(loan_limit, index) in loan_limits" :key="loan_limit.client_ac">
              <td class="py-2 font-medium">
                <strong>{{ loan_limit.reference_id }}</strong>
              </td>
              <td class="text-center py-2"> {{
                  formatCurrency(parseFloat(loan_limit.current_limit).toFixed(2))
                }}
              </td>
              <td class="text-center py-2">{{
                  formatCurrency(parseFloat(loan_limit.requested_limit).toFixed(2))
                }}
              </td>
              <td class="text-center py-2">
                <button v-if="parseInt(loan_limit.status) === 1"
                        class="inline-block px-3 py-1 rounded-md text-white bg-green-500">
                  Approved
                </button>

                <button v-else-if="parseInt(loan_limit.status) === 2"
                        class="inline-block px-3 py-1 rounded-md text-white bg-orange-500">
                  Pending
                </button>

                <button v-else-if="parseInt(loan_limit.status) === 3"
                        class="inline-block px-3 py-1 rounded-md text-white bg-red-500">
                  Rejected
                </button>
              </td>
              <td class="text-center py-2">
                <span style="font-size: 11px; color: grey">{{ moment(loan_limit.created).format('lll') }}</span>
              </td>
              <td class="py-2 text-center relative w-24">
                <div class="relative inline-block">
                  <button
                      v-if="parseInt(loan_limit.status) === 2||parseInt(loan_limit.status) === 3"
                      class="px-3 py-1 flex items-center space-x-1"
                      @click="toggleLLDropdown(index)">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                         stroke="#000000" class="w-6 h-6">
                      <path stroke-linecap="round" stroke-linejoin="round"
                            d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </button>

                  <button v-else class="px-3 py-1 flex items-center space-x-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                         stroke="#808080" class="w-6 h-6">
                      <path stroke-linecap="round" stroke-linejoin="round"
                            d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </button>

                  <!--ShowDropdown-->
                  <div v-if="showLLDropdown[index]"
                       class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10">
                    <ul class="py-2">
                      <li v-if="parseInt(loan_limit.status) === 2||parseInt(loan_limit.status) === 3"
                          @click="toggleApproveLL(loan_limit.reference_id,  1)">
                        <a class="block px-16 py-2 cursor-pointer hover:bg-green-200">Approve</a>
                      </li>
                      <li v-if="parseInt(loan_limit.status) === 2"
                          @click="toggleApproveLL(loan_limit.reference_id, 3)">
                        <a class="block px-16 py-2 cursor-pointer hover:bg-red-200">Reject</a>
                      </li>
                    </ul>
                  </div>

                </div>
              </td>
            </tr>
            </tbody>
          </table>

          <!--Pagination-->
          <div class="flex w-full text-xs items-center" v-show="ll_total>ll_limit">
            <div class="flex-shrink" v-show="ll_offset === 0">{{ ll_offset + 1 }} to {{ ll_limit }} of {{
                ll_total
              }}
            </div>
            <div class="flex-shrink" v-show="ll_offset > 0">{{ ((ll_offset * ll_limit) - ll_limit) + 1 }} to
              {{ ll_limit * ll_offset }} of
              {{ ll_total }}
            </div>
            <div class="flex-grow text-right" v-show="ll_total > ll_limit">
              <div class="inline-block bg-white border rounded-md divide-x">
                <button class="p-2 px-3" v-show="Math.ceil(ll_total/ll_limit) > 1" @click="gotToPageLL(ll_offset-1)">
                  &larr;
                </button>
                <button class="p-2 px-4" :class="{'bg-gray-100':ll_offset===1}" @click="gotToPageLL(1)">1</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':ll_offset===2}"
                        v-show="Math.ceil(ll_total/ll_limit) > 1"
                        @click="gotToPageLL(2)">2
                </button>
                <button class="p-2 px-4" v-show="Math.ceil(ll_total/ll_limit) > 3">...</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':ll_offset===ll_offset}"
                        v-show="Math.ceil(ll_total/ll_limit) > 3 && ll_offset >= 3" @click="gotToPageLL(ll_offset)">
                  {{ ll_offset }}
                </button>

                <button class="p-2 px-4" :class="{'bg-gray-100':ll_offset===Math.ceil(ll_total/ll_limit)}"
                        v-show="Math.ceil(ll_total/ll_limit) > 4" @click="gotToPageLL(Math.ceil(ll_total/ll_limit))">
                  {{ Math.ceil(ll_total / ll_limit) }}
                </button>
                <button class="p-2 px-3" v-show="(ll_offset*ll_limit) < ll_total" @click="gotToPageLL(ll_offset+1)">
                  &rarr;
                </button>
              </div>
            </div>
          </div>

          <!-- Modal -->
          <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
               :class="{ 'opacity-100 pointer-events-auto': isAcceptRejectModelOpenLL, 'opacity-0 pointer-events-none': !isAcceptRejectModelOpenLL }">
            <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
            <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
              <div class="modal-content py-4 text-left px-6">
                <!-- Title -->
                <div class="flex justify-between items-center pb-3">
                  <p class="text-2xl font-bold">Loan Review</p>
                  <div class="modal-close cursor-pointer z-50" @click="isAcceptRejectModelOpenLL = false">
                    <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                         viewBox="0 0 18 18">
                      <path
                          d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                    </svg>
                  </div>
                </div>
                <!-- Body -->
                <div class="mb-4">
                  <label class="block text-sm font-medium">Please type
                    <strong class="text-blue-500">{{ label }}</strong> to {{ label }} this loan.</label>
                  <input
                      :placeholder="label"
                      type="text"
                      v-model="narration"
                      class="mt-1 p-2 border rounded-md w-full"
                  />
                </div>

                <!-- Your table and other content here -->
                <!-- Footer -->
                <div class="flex justify-end pt-2">

                  <button @click="toggleApproveLL(1, 1, 1)"
                          class="px-4 py-2 bg-transparent rounded-lg text-blue-500 hover:bg-gray-100 hover:text-blue-400">
                    Cancel
                  </button>
                  <button @click="approveLL"
                          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 ">
                    {{ capitalizeFirstLetter(label) }}
                  </button>
                </div>
              </div>
            </div>
          </div>

        </div>
        <div v-else class="my-8 flex justify-center">
          No Results
        </div>
      </div>

      <!-- Transactions -->
      <div v-if="activeTab === 'transactions'" class="rounded-lg bg-white shadow-sm mx-3 pb-2 border">

        <div class="grid grid-cols-3 mb-4">
          <div class="px-6 block">
            <label class="text-xs font-medium mb-1 mt-3 block">Filter By {{ selectedFilterTrx.text }}</label>
            <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selectedFilterTrx">
              <option value="" disabled>Select Filter Type</option> &lt;!&ndash; Placeholder option &ndash;&gt;
              <option v-for="item in trxn_filters" :value="item">
                {{ item.text }}
              </option>
            </select>
          </div>

          <div v-if="selectedFilterTrx.value===1" class="block">
            <label class="text-xs font-medium mb-1 mt-3 block"> {{ selectedFilterTrx.text }}</label>
            <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                   v-model="trxn_params.trxn_id"
                   placeholder="Enter Transaction ID" @keyup.enter="setTrx">
          </div>

          <div v-else-if="selectedFilterTrx.value===2" class="block">
            <label class="text-xs font-medium mb-1 mt-3 block">Filter By Reference Type</label>
            <select
                class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selectedFilterTrxType">
              <option value="" disabled>Select Transaction Type</option> &lt;!&ndash; Placeholder option &ndash;&gt;
              <option v-for="item in filter_trnx_type" :value="item">
                {{ item.text }}
              </option>
            </select>
          </div>

          <div v-else-if="selectedFilterTrx.value===3" class="block">
            <label class="text-xs font-medium mb-1 mt-3 block">Filter By Reference Type</label>
            <select
                class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selectedFilterRefType">
              <option value="" disabled>Select Reference Type</option> &lt;!&ndash; Placeholder option &ndash;&gt;
              <option v-for="item in filter_ref_type" :value="item">
                {{ item.text }}
              </option>
            </select>
          </div>

        </div>

        <div v-if="trxn.length>0" class="px-6">
          <table class="w-full mb-4 table">
            <thead class="border-b-2 text-xs text-left">
            <tr class="table-row">
              <th class="py-2">Transaction ID</th>
              <th class="text-center py-2">Customer</th>
              <th class="text-center py-2">Transaction Type</th>
              <th class="text-center py-2">Amount</th>
              <th class="text-center py-2">Description</th>
              <th class="py-2">Date</th>
            </tr>
            </thead>
            <tbody class="text-xs text-gray-600 divide-y">
            <tr v-for="item in trxn" :key="item.transaction_id">
              <td class="py-2 font-medium">
                <strong>{{ item.transaction_id }}</strong>
              </td>

              <td class="text-center py-2">
                <strong>{{ item.msisdn }} - {{ item.first_name }} {{ item.last_name }}</strong>
              </td>

              <td class="text-center py-2">
                <strong>{{ item.reference_name }}</strong>
                <br>
                <span style="font-size: smaller; color: grey"> {{ item.source }}</span>
              </td>

              <td class="text-center py-2">
                <strong>{{ item.currency_code }}. {{ item.amount }}</strong>
              </td>

              <td class="py-2">
                <span>{{ item.identifier_name }} | {{ item.reference_type }} | {{ item.channel_name }}</span>
                <br>
                <span style="font-size: smaller; color: grey"> {{ item.description }}</span>
              </td>

              <td class="py-2">
                <span> {{ moment(item.created).format('lll') }}</span>
              </td>

            </tr>
            </tbody>
          </table>

          <!--Pagination-->
          <div class="flex w-full text-xs items-center" v-show="t_total>t_limit">
            <div class="flex-shrink" v-show="t_offset === 0">{{ t_offset + 1 }} to {{ t_limit }} of {{
                t_total
              }}
            </div>
            <div class="flex-shrink" v-show="t_offset > 0">{{ ((t_offset * t_limit) - t_limit) + 1 }} to
              {{ t_limit * t_offset }} of
              {{ t_total }}
            </div>
            <div class="flex-grow text-right" v-show="t_total > t_limit">
              <div class="inline-block bg-white border rounded-md divide-x">
                <button class="p-2 px-3" v-show="Math.ceil(t_total/t_limit) > 1" @click="gotToPageTrnx(t_offset-1)">
                  &larr;
                </button>
                <button class="p-2 px-4" :class="{'bg-gray-100':t_offset===1}" @click="gotToPageTrnx(1)">1</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':t_offset===2}"
                        v-show="Math.ceil(t_total/t_limit) > 1"
                        @click="gotToPageTrnx(2)">2
                </button>
                <button class="p-2 px-4" v-show="Math.ceil(t_total/t_limit) > 3">...</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':t_offset===t_offset}"
                        v-show="Math.ceil(t_total/t_limit) > 3 && t_offset >= 3" @click="gotToPageTrnx(t_offset)">
                  {{ t_offset }}
                </button>

                <button class="p-2 px-4" :class="{'bg-gray-100':t_offset===Math.ceil(t_total/t_limit)}"
                        v-show="Math.ceil(t_total/t_limit) > 4" @click="gotToPageTrnx(Math.ceil(t_total/t_limit))">
                  {{ Math.ceil(t_total / t_limit) }}
                </button>
                <button class="p-2 px-3" v-show="(t_offset*t_limit) < t_total" @click="gotToPageTrnx(t_offset+1)">
                  &rarr;
                </button>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="my-8 flex justify-center">
          No Results
        </div>

      </div>

      <!-- Checkoff -->
      <div v-show="trxn.length>0 && activeTab === 'checkoff'" class="rounded-lg bg-white shadow-sm mx-3 pb-2 border">
        <div class="px-6">
          <table class="w-full mb-4 table">
            <thead class="border-b-2 text-xs text-left">
            <tr class="table-row">
              <th class="py-2">Transaction ID</th>
              <th class="py-2">Source</th>
              <th class="py-2">Amount</th>
              <th class="py-2">Customer</th>
              <th class="py-2">Reference</th>
            </tr>
            </thead>
            <tbody class="text-xs text-gray-600 divide-y">
            <tr v-for="item in trxn" :key="item.transaction_id">
              <td class="py-2 font-medium">
                <strong style="color: dodgerblue">{{ item.transaction_id }}</strong>
                <br>
                <span style="font-size: smaller; color: grey">Created {{ moment(item.created).format('lll') }}</span>
              </td>

              <td class="py-2">
                <strong style="color: #136207">{{ item.reference_name }}</strong>
                <br>
                <span style="font-size: smaller; color: grey">Type {{ item.reference_type }}</span>
              </td>

              <td class="py-2">
                <strong style="color: forestgreen">{{ item.source }}</strong>
                <br>
                <span style="font-size: smaller; color: grey">Desc {{ item.description }}</span>
              </td>

              <td class="py-2">
                <strong style="color: dodgerblue">{{ item.first_name }} {{ item.last_name }}</strong>
                <br>
                <span style="font-size: smaller; color: grey">Max approved {{ item.msisdn }}</span>
              </td>

              <td class="py-2">
                <strong style="color: #136207">{{ parseFloat(item.amount).toFixed(2) }}</strong>
                <br>
                <span style="font-size: smaller; color: grey">Channel {{ item.channel_name }}</span>
              </td>

            </tr>
            </tbody>
          </table>

          <!--Pagination-->
          <div class="flex w-full text-xs items-center" v-show="t_total>t_limit">
            <div class="flex-shrink" v-show="t_offset === 0">{{ t_offset + 1 }} to {{ t_limit }} of {{
                t_total
              }}
            </div>
            <div class="flex-shrink" v-show="t_offset > 0">{{ ((t_offset * t_limit) - t_limit) + 1 }} to
              {{ t_limit * t_offset }} of
              {{ t_total }}
            </div>
            <div class="flex-grow text-right" v-show="t_total > t_limit">
              <div class="inline-block bg-white border rounded-md divide-x">
                <button class="p-2 px-3" v-show="Math.ceil(t_total/t_limit) > 1" @click="gotToPageTrnx(t_offset-1)">
                  &larr;
                </button>
                <button class="p-2 px-4" :class="{'bg-gray-100':t_offset===1}" @click="gotToPageTrnx(1)">1</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':t_offset===2}"
                        v-show="Math.ceil(t_total/t_limit) > 1"
                        @click="gotToPageTrnx(2)">2
                </button>
                <button class="p-2 px-4" v-show="Math.ceil(t_total/t_limit) > 3">...</button>
                <button class="p-2 px-4" :class="{'bg-gray-100':t_offset===t_offset}"
                        v-show="Math.ceil(t_total/t_limit) > 3 && t_offset >= 3" @click="gotToPageTrnx(t_offset)">
                  {{ t_offset }}
                </button>

                <button class="p-2 px-4" :class="{'bg-gray-100':t_offset===Math.ceil(t_total/t_limit)}"
                        v-show="Math.ceil(t_total/t_limit) > 4" @click="gotToPageTrnx(Math.ceil(t_total/t_limit))">
                  {{ Math.ceil(t_total / t_limit) }}
                </button>
                <button class="p-2 px-3" v-show="(t_offset*t_limit) < t_total" @click="gotToPageTrnx(t_offset+1)">
                  &rarr;
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Customer KYC -->
      <div v-if="activeTab === 'kyc'" class="rounded-lg bg-white shadow-sm mx-3 pb-2 border">
        <div v-if="customers.length>0" class="px-6">
          <table class="w-full mb-12 table">
            <thead class="border-b-2 text-xs text-left">
            <tr class="table-row">
              <th class="py-2">Loan Acc No</th>
              <th class="py-2">Identifier Type</th>
              <th class="py-2  text-center">KYC Status</th>
              <th class="text-center">Action</th>
              <th></th>
            </tr>
            </thead>
            <tbody class="text-xs text-grey-600 divide-y">
            <tr v-for="(item,index) in customers" :key="item.user_id">

              <td class="py-2"><strong>{{ item.loan_number }}</strong></td>

              <td class="py-2">
                <strong>{{ item.identifier_type }}</strong>
                <br>
                <strong>{{ item.national_id }}</strong>
              </td>

              <td class="py-3 text-center">
                <button v-if="parseInt(item.kyc_confirm) === 1" class="inline-block px-3 py-1 rounded-md text-white"
                        style="background-color: #51e353">
                  Completed
                </button>
                <button v-else-if="parseInt(item.kyc_confirm) === 3"
                        class="inline-block px-3 py-1 rounded-md text-white"
                        style="background-color: #cc0404">
                  InCompleted
                </button>
                <button v-else
                        class="inline-block px-3 py-1 rounded-md text-white"
                        style="background-color: purple">
                  Unverified
                </button>
              </td>

              <td class="py-2 text-center relative w-24">
                <div class="relative inline-block">
                  <button
                      v-if="parseInt(item.status) === 1000 || parseInt(item.status) === 1005"
                      class="px-3 py-1 flex items-center space-x-1"
                      @click="toggleKycDropdown(index)">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                         stroke="#000000" class="w-6 h-6">
                      <path stroke-linecap="round" stroke-linejoin="round"
                            d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </button>

                  <button v-else class="px-3 py-1 flex items-center space-x-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                         stroke="#808080" class="w-6 h-6">
                      <path stroke-linecap="round" stroke-linejoin="round"
                            d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </button>

                  <div v-if="showDropdownKyc[index]"
                       class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                       style="width: 230px; text-align: left;">
                    <ul class="py-2">
                      <li @click="toggleOpenKyc">
                        <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">View KYC</a>
                      </li>
                      <li @click="acceptKYC(item.loan_number)">
                        <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Approve</a>
                      </li>
                      <li @click="rejectKYC(item.loan_number)">
                        <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Reject</a>
                      </li>
                      <li @click="toggleKyc(item.loan_number)">
                        <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Upload</a>
                      </li>
                    </ul>
                  </div>

                </div>
              </td>

            </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="my-8 flex justify-center">
          No Results
        </div>

        <!-- KYC Modal -->
        <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
             :class="{ 'opacity-100 pointer-events-auto': open_kyc, 'opacity-0 pointer-events-none': !open_kyc }">
          <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
          <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
            <div class="modal-content py-4 text-left px-6">
              <!-- Title -->
              <div class="flex justify-between items-center pb-3">
                <p class="text-2xl font-bold">KYC Documents</p>
                <div class="modal-close cursor-pointer z-50" @click="open_kyc = false">
                  <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                       viewBox="0 0 18 18">
                    <path
                        d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                  </svg>
                </div>
              </div>
              <!-- Body -->
              <div class="mb-4">
                <div class="row">
                  <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="block" style="padding: 1rem;">
                      <div class="column">
                        <h4>ID Front</h4>
                        <div class="ui card">
                          <div class="image">
                            <img :src="idFrontImage || placeholderImage" alt="ID Front" class="kyc-image"/>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="block" style="padding: 1rem;">
                      <div class="column">
                        <h4>ID Back</h4>
                        <div class="ui card">
                          <div class="image">
                            <img :src="idBackImage || placeholderImage" alt="ID Back" class="kyc-image"/>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="block" style="padding: 1rem;">
                      <div class="column">
                        <h4>Selfie</h4>
                        <div class="ui card">
                          <div class="image">
                            <img :src="selfieImage || placeholderImage" alt="Selfie" class="kyc-image"/>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>


                </div>
              </div>
              <!-- Footer -->
              <div class="flex justify-end pt-2">
                <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" v-if="imagesFound===true"
                        @click="rejectKYC">Reject
                </button>
                <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-5 pr-5"
                        v-if="imagesFound===true" @click="acceptKYC">Accept
                </button>
                <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2"
                        v-if="imagesFound===false && imagesLoaded===false" @click="toggleCloseKyc">Close
                </button>
              </div>
            </div>
          </div>
        </div>

      </div>

    </div>

    <!-- Modal -->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': block, 'opacity-0 pointer-events-none': !block }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Block User</p>
            <div class="modal-close cursor-pointer z-50" @click="block = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                   viewBox="0 0 18 18">
                <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!-- Body -->
          <div class="mb-4">
            <label class="block text-sm font-medium">Reason</label>
            <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none">
              <option value="" disabled>Select One</option> <!-- Placeholder option -->
              <option v-for="reason in blockReasons" :value="reason.value">
                {{ reason.text }}
              </option>
            </select>

          </div>

          <!-- Your table and other content here -->
          <!-- Footer -->
          <div class="flex justify-end pt-2">

            <button @click="block = false"
                    class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2">
              Cancel
            </button>
            <button @click="toggleBlockUnblock"
                    class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-10 pr-10">
              Block
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>

</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import accounting from "accounting";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import transactions from "@/views/transactions/transactions.vue";
import GenericTable from "@/views/loan/repayments/table_repayments.vue";

export default {

  data() {
    return {
      // phone: "**********",
      phone: "",
      customer: null,
      customers: [],
      accounting: accounting,
      perPage: 5,
      moment: moment,
      block: false,
      blockReasons: [
        {text: 'Fraud', value: 1},
        {text: 'Self', value: 2},
      ],

      isOpen: false,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,

      moreParams: {
        start: "",
        end: "",
        limit: 10
      },
      selectedFilterLL: {},

      //
      activeTab: 'loan_requests',
      showTables: false,
      // Loan Req
      loan_requests: [],
      lr_total: 0,
      lr_limit: 10,
      lr_offset: 1,

      loan_req_params: {
        start: "",
        end: "",
        limit: 10,
        offset: "",
        sort: "",
        export: "",
        amount: "",
        client_id: "",
        loan_number: "",
        reference_id: "",
        loan_request_number: ""
      },
      //end Loan Req

      // Loan Lim
      loan_limits: [],
      ll_total: 0,
      ll_limit: 10,
      ll_offset: 1,

      loan_lim_params: {
        offset: "",
        limit: "",
        sort: "",
        export: "",
        start: "",
        end: "",
        client_id: "",
        loan_number: "",
        request_number: "",
      },
      //end Loan Lim

      // Trxn
      selectedFilterTrx: {},
      trxn_filters: [
        {text: 'Transaction Number', value: 1},
        {text: 'Transaction Type', value: 2},
        {text: 'Reference Type', value: 3},
      ],

      selectedFilterTrxType: {},
      filter_trnx_type: [
        {text: 'Debit', value: 1},
        {text: 'Credit', value: 2},
        {text: 'Reversal', value: 3},
      ],

      selectedFilterRefType: {},
      filter_ref_type: [
        {text: 'Bill Payments', value: 1},
        {text: 'Salary Advance Loan', value: 2},
        {text: 'Excise Duty', value: 3},
        {text: "Service As A Platform (SAAP)", value: 3},
        {text: "Bill Payments Refunds", value: 3},
        {text: "Withdrawal Charges", value: 3},
        {text: "Internet Charges", value: 3},
        {text: "Withdrawal Reversal Charges", value: 3},
        {text: "Bill Payments Refunds", value: 3},
      ],

      trxn: [],
      t_total: 0,
      t_limit: 10,
      t_offset: 1,

      trxn_params: {
        offset: "",
        limit: "",
        sort: "",
        export: "",
        start: "",
        end: "",
        source: "",
        client_id: "",
        amount: "",
        trxn_id: "",
        loan_number: "",
        client_phone: "",
        client_email: "",
        channel_name: "",
        reference_id: "",
        reference_type_id: "",
        trxn_reference_id: ""
      },

      status_description: "",

      ///
      loan_number: "",
      request_number: "",
      loan_repayments: [],
      lrps_total: 0,
      lrps_limit: 10,
      lrps_offset: 1,

      loan_repayments_params: {
        offset: "",
        limit: "",
        sort: "",
        export: "",
        start: "",
        end: "",
        client_id: "",
        amount: "",
        loan_number: "",
        client_phone: "",
        client_email: "",
        request_number: "",
        payment_transaction_number: "",
      },
      //end Loan repayments


      //
      isAcceptRejectModelOpenLR: false,
      label: "approve",
      narration: "approve",
      isAcceptRejectModelOpenLL: false,
      //
      showLRDropdown: [],
      showLLDropdown: [],
      showLRTDropdown: [],
      showTRXDropdown: [],
      showDropdownKyc: [],
      bucketUrl: "https://storage.googleapis.com/",
      loanNumber: "",
      idFrontImage: "",
      idBackImage: "",
      selfieImage: "",
      open_kyc: false,
      imagesLoaded: false,
      imagesFound: false,

    }
  },
  components: {
    GenericTable,
    Loading,
    VueDatePicker
  },
  computed: {
    placeholderImage() {
      return "https://via.placeholder.com/600x400?text=Image+Not+Available";
    }
  },
  watch: {
    selectedFilterTrxType(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.trxn_params.trxn_reference_id = newVal.value
        this.setTrx()
      }
    },

    selectedFilterRefType(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.trxn_params.reference_type_id = newVal.value
        this.setTrx()
      }
    },
  },
  mounted() {
    if (this.$route.params.phone) {
      this.phone = this.$route.params.phone
      this.searchPhone()
    }
  },

  methods: {
    ...mapActions(["getTrx", "fillMsisdn", "searchLoanAccount", "getLoanRequests", "getLimitRequests",
      "getLoanRepayments", "fillCustomer", "approveOrRejectKYC", "updateLoanAccount", "resendTAN",
      "approveLimit", "confirmKYC", "toggleSideMenu",]),
    //
    toggleSideM() {
      this.toggleSideMenu()
    },

    //
    gotToPageLR(page) {
      let vm = this
      vm.lr_offset = page
      vm.loan_req_params.offset = page
      vm.setLoanRequests()
    },
    //
    gotToPageLL(page) {
      let vm = this
      vm.ll_offset = page
      vm.loan_lim_params.offset = page
      vm.setLimitRequests()
    },
    //
    gotToPageTrnx(page) {
      let vm = this
      vm.t_offset = page
      vm.trxn_params.offset = page
      vm.setTrx()
    },

    //
    async searchPhone() {
      let app = this
      app.isLoading = true
      if (this.phone.length > 0) {
        if (this.phone.slice(0, 1) === '0') this.phone = this.phone.slice(1)
        this.moreParams.loan_number = this.phone
        await this.fillMsisdn(this.phone)

        await app.setCustomer()
      }
    },

    //
    async setCustomer() {
      let app = this
      app.customer = null
      let response = await this.searchLoanAccount(this.moreParams)

      this.status = response.status
      if (this.status === 200) {
        app.customer = response.message.data[0]
        app.customers = response.message.data


        for (let i = 0; i < app.customers.length; i++) {
          app.showDropdownKyc.push(false)
        }

        await this.fillCustomer(app.customer)
        this.status = 1

        await app.setLoanRequests()
        // await app.setLimitRequests()
        // await app.setTrx()
        // await app.setLoanRepayments()
        // await app.setKYC()

        app.showTables = true
      } else {
        await this.fillCustomer({msisdn: null})
        this.status = 0
        app.isLoading = false
      }

    },

    async fetchData(tab) {
      this.activeTab = tab;
      let app = this;
      try {
        switch (tab) {
          case 'loan_requests':
            await app.setLoanRequests()
            break;
          case 'loan_limits':
            await app.setLimitRequests()
            break;
          case 'transactions':
            await app.setTrx()
            break;
          case 'checkoff':
            await app.setCheckOffReport()
            break;
          case 'loan_repayments':
            // await app.viewRepayments()
            await app.setLoanRepayments()
            break;
          case 'kyc':
            await app.setKYC()
            break;
        }
      } catch (error) {
        console.error(`Error fetching data for ${tab}:`, error);
      }
    },

    // Fetch and set Loan Requests
    async setLoanRequests() {
      let app = this
      app.isLoading = true;
      app.loan_req_params.loan_number = app.customer.loan_number

      let response = await this.getLoanRequests(this.loan_req_params)
      // console.log("Setting Loan Requests",  JSON.stringify(response))
      if (response.status === 200) {
        app.loan_requests = response.message.data
        app.lr_total = response.message.total_count

        app.showLRDropdown = []
        for (let i = 0; i < app.loan_requests.length; i++) {
          app.showLRDropdown.push(false)
        }
      } else {
        app.loan_requests = []
      }
      app.isLoading = false
    },

    // Fetch and set Loan Limit
    async setLimitRequests() {
      let app = this
      app.isLoading = true
      app.loan_lim_params.loan_number = app.customer.loan_number
      // console.log("MORE PARAMS LL", JSON.stringify(app.loan_lim_params))

      let response = await this.getLimitRequests(app.loan_lim_params)

      if (response.status === 200) {
        app.loan_limits = response.message.data
        app.ll_total = response.message.total_count

        app.showTables = true
        app.showLLDropdown = []

        for (let i = 0; i < app.loan_limits.length; i++) {
          app.showLLDropdown.push(false)
        }
      } else {
        app.loan_limits = []
      }
      app.isLoading = false
    },

    //Fetch and set Trxn
    async setTrx() {
      let app = this
      app.isLoading = true
      this.trxn_params.client_phone = this.$store.state.msisdn ?? ""

      let response = await this.getTrx(this.trxn_params)

      if (response.status === 200) {
        app.trxn = response.message.data
        app.t_total = parseInt(response.message.total_count)
      } else {
        app.trxn = []
      }
      app.isLoading = false
    },

    // Fetch and set Loan Repayments
    async setLoanRepayments() {
      let app = this
      app.isLoading = true
      app.loan_repayments_params.loan_number = app.customer.loan_number

      let response = await this.getLoanRepayments(app.loan_repayments_params)

      if (response.status === 200) {
        app.loan_repayments = response.message.data
        app.lrps_total = response.message.data

        app.showTables = true

        app.showLRTDropdown = []
        for (let i = 0; i < app.loan_repayments.length; i++) {
          app.showLRTDropdown.push(false)
        }
      } else {
        app.loan_repayments = []
      }
      app.isLoading = false
    },

    //
    toggleBlockUnblock() {
      this.block = !this.block
    },

    async disableBulk() {
      let app = this
      const loan_number = this.customer.loan_number
      let payload = {loan_number: loan_number, marketing_status: '0'}

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this disables bulk sms for this user !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, disable!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          return await this.updateLoanAccount(payload)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {

              app.$swal.fire('Disabled!', result.value.message, 'success')
              await app.searchPhone()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async enableBulk() {
      let app = this
      const loan_number = this.customer.loan_number
      let payload = {loan_number: loan_number, status: '1'}

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this enables bulk sms for this user !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, enable!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          return await this.updateLoanAccount(payload)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {

              app.$swal.fire('Enabled!', result.value.message, 'success')
              await app.searchPhone()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    capitalizeFirstLetter(value) {
      if (!value) return '';
      value = value.toLowerCase()
      return value.charAt(0).toUpperCase() + value.slice(1);
    },
    //
    formatCurrency(number) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },

    /// Loan Requests Toggle
    //
    toggleLRDropdown(index) {
      for (let i = 0; i < this.showLRDropdown.length; i++) {
        this.showLRDropdown[i] = i === index ? !this.showLRDropdown[i] : false;
      }
    },
    //
    closeLRDropdown() {
      for (let i = 0; i < this.showLRDropdown.length; i++) {
        this.showLRDropdown[i] = false;
      }
    },

    //
    async toggleApproveLR(id, amount, status) {
      this.closeLRDropdown()
      this.id = id
      this.status = status
      this.approved_amount = amount
      this.status_description = ''
      this.label = status === 0 ? 'reject' : 'approve'
      this.isAcceptRejectModelOpenLR = !this.isAcceptRejectModelOpenLR
    },

    //
    async requestTAN(request_number) {
      this.closeLRDropdown()
      const payload = {
        request_number: request_number,
      }
      this.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this resends the code!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, send!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          return await this.resendTAN(payload)
        },
      }).then(async result => {
        if (result.value.status === 200) {
          this.$swal.fire('Sent!', result.value.message, 'success')
        } else {
          this.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },

    //
    async viewRepayments(data) {
      this.activeTab = 'loan_repayments'
      this.loan_repayments_params.request_number = data.req_number
      // console.log("data: ", JSON.stringify(data))
      await this.setLoanRepayments()
    },

    /// Loan Limit Toggle
    //
    toggleLLDropdown(index) {
      for (let i = 0; i < this.showLLDropdown.length; i++) {
        this.showLLDropdown[i] = i === index ? !this.showLLDropdown[i] : false;
      }
    },
    //
    closeLLDropdown() {
      for (let i = 0; i < this.showLLDropdown.length; i++) {
        this.showLLDropdown[i] = false;
      }
    },

    async toggleApproveLL(reference_id, status) {
      this.closeLLDropdown()
      this.reference_id = reference_id
      this.status = status
      this.narration = ''
      if (status === 3) {
        this.label = 'reject'
      }
      if (status === 1) {
        this.label = 'approve'
      }

      this.isAcceptRejectModelOpenLL = !this.isAcceptRejectModelOpenLL
    },


    async approveLL() {
      let app = this
      if (this.label !== this.narration) {
        return app.handleAlert('Please type ' + this.label + " to " + this.label + " this loan.")
      }

      const payload = {
        reference_id: app.reference_id,
        status: app.status,
        narration: 'Limit of ' + app.reference_id + ' ' + this.label,
      }
      console.log("payload : " + JSON.stringify(payload))

      await app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this approve/reject this loan limit!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, confirm!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          app.isAcceptRejectModelOpenLL = false
          return await this.approveLimit(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Submitted!', result.value.message, 'success')
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },


    //
    //
    toggleKycDropdown(index) {
      for (let i = 0; i < this.showDropdownKyc.length; i++) {
        this.showDropdownKyc[i] = i === index ? !this.showDropdownKyc[i] : false;
      }
      // console.log(" this.showDropdownKyc[i]", this.showDropdownKyc)
    },
    //
    async toggleOpenKyc() {
      this.showDropdownKyc[0] = false
      this.open_kyc = !this.open_kyc
    },

    toggleCloseKyc() {
      this.open_kyc = !this.open_kyc
    },
    //
    async toggleKyc() {
      this.loading = true
      this.showDropdownKyc[0] = false
      // this.open_kyc = !this.open_kyc

    },

    async setKYC() {
      this.imagesLoaded = false

      this.loading = false
      const payload = {
        "offset": "",
        "limit": "",
        "sort": "",
        "export": "",
        "client_id": "",
        "client_name": "",
        "client_email": "",
        "loan_number": this.customer.loan_number,
        "client_phone": ""
      };

      let response = await this.confirmKYC(payload)

      if (response.status === 200) {
        // let result = response.message

        const bucketUrl = response.message.bucket_url;

        const fileInfo = JSON.parse(response.message.data[0].file_info);
        const idFront = fileInfo[0].file_name;
        const idBack = fileInfo[1].file_name;
        const image1 = fileInfo[2].file_name;

        this.idFrontImage = bucketUrl + idFront
        this.idBackImage = bucketUrl + idBack
        this.selfieImage = bucketUrl + image1

        this.imagesLoaded = true
        this.imagesFound = true

      } else {
        this.idFrontImage = "https://via.placeholder.com/600x400?text=Image+Not+Available"
        this.idBackImage = "https://via.placeholder.com/600x400?text=Image+Not+Available"
        this.selfieImage = "https://via.placeholder.com/600x400?text=Image+Not+Available"

        this.imagesFound = false
      }
    },


    async acceptKYC() {
      let app = this
      const payload = {
        "status": "1",
        "acc": app.customer.loan_number
      };
      let response = await this.approveOrRejectKYC(payload)

      if (response.status === 200) {
        let result = response.message
        app.$swal.fire('KYC Approved', result, 'success')
      } else {
        let result = response.message
        app.$swal.fire('Error!', result, 'error')
      }

      this.toggleCloseKyc()
    },

    async rejectKYC() {
      let app = this
      const payload = {
        "status": "2",
        "acc": app.customer.loan_number
      };
      let response = await this.approveOrRejectKYC(payload)

      if (response.status === 200) {
        let result = response.message
        app.$swal.fire('KYC Rejected', result, 'success')
      } else {
        let result = response.message
        app.$swal.fire('KYC Error!', result, 'error')
      }

      this.toggleCloseKyc()
    },


  }
}
</script>
