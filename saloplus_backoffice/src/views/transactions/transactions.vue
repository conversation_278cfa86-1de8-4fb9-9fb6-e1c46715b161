<template>
  <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex font-medium p-6 pb-1 border-b gap-4 mb-5">
      <div class="flex-shrink font-medium">
        <i v-if="!this.$store.state.isSideMenuOpen" class="fa fa-bars text-black text-lg" @click="toggleSideM"></i>
        <i v-else class="fa fa-close text-black text-lg" @click="toggleSideM"></i>
      </div>

      <div class="flex-grow font-medium pb-2">
        Transactions <span v-if="searchDropdownPlaceholder!==''">- ({{ searchDropdownPlaceholder }})</span>
      </div>
    </div>


    <div class="block rounded-lg bg-white shadow-lg py-3 mx-3  mb-4 border ">

      <div class="grid grid-cols-3 gap-x-6 mx-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Filter By</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selectedFilter">
            <option value="" disabled>Select a filter</option> <!-- Placeholder option -->
            <option v-for="item in filter_by" :value="item">
              {{ item.text }}
            </option>
          </select>
        </div>

        <div class="block">
          <label class="text-xs font-medium mb-1 block">Filter By {{ selectedFilter.text }}</label>
          <div v-if="selectedFilter.value===1" class="relative mb-6">
            <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                   @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
            <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
                v-if="searchDropdown">
              <li v-for="item in organisations"
                  class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                  @click="setClientId(item)">{{ item.text }}
              </li>
            </ul>
          </div>
          <input v-else-if="selectedFilter.value===2"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="payload.loan_number"
                 placeholder="Enter Loan Number" @keyup.enter="setTrx">
          <input v-else-if="selectedFilter.value===3"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="payload.client_email"
                 placeholder="Enter Client Email" @keyup.enter="setTrx">
          <input v-else-if="selectedFilter.value===4"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="payload.client_phone"
                 placeholder="Enter Client Phone Number" @keyup.enter="setTrx">
          <input v-else
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 placeholder="Enter Number">
        </div>

        <div class="block">
          <label class="text-xs mb-1 block font-medium">Filter by Date Range</label>
          <VueDatePicker v-model="date" range :preset-ranges="presetRanges" position="center" :clearable="true"
                         :enable-time-picker="false" @closed="selectDate">
            <template #yearly="{ label, range, presetDateRange }">
              <span @click="presetDateRange(range)">{{ label }}</span>
            </template>
          </VueDatePicker>
        </div>
      </div>


      <div class="mx-2 p-2 rounded-lg border">
        <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">Trxn ID</th>
          <th class="py-2">Customer</th>
          <th class="py-2">Trxn Type</th>
          <th class="py-2">Amount</th>
          <th class="py-2">Description</th>
          <th class="py-2">Date</th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="item in transactions" :key="item.transaction_id">
          <td class="py-2 font-medium"> {{ item.transaction_id }}  </td>

          <td class="py-2"> {{ item.msisdn }} <br> {{ item.first_name }} {{ item.last_name }} </td>

          <td class="py-2">
            <span>{{ item.reference_name }}</span>
            <br>
            <span style="font-size: smaller; color: grey"> {{ item.source }}</span>
          </td>

          <td class="py-2">
            <span>{{ item.currency_code }}. {{ item.amount }}</span>
          </td>

          <td class="py-2">
            <span>{{ item.identifier_name }} | {{ item.reference_type }} | {{ item.channel_name }}</span>
            <br>
            <span style="font-size: smaller; color: grey"> {{ item.description }}</span>
          </td>

          <td class="py-2">
            <span> {{ moment(item.created).format('lll') }}</span>
          </td>

        </tr>
        </tbody>
      </table>

      <!--Pagination-->
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to {{ limit * offset }} of
          {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1" @click="gotToPage(offset-1)">&larr;</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>
    </div>

    </div>

  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";

export default {
  data() {
    return {

      selectedStatus: 'All',
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      payload: {
        offset: "",
        limit: 10,
        sort: "",
        export: "",
        start: "",
        end: "",
        source: "",
        amount: "",
        client_id: this.$store.state.merchant_account.client_id,
        loan_number: this.$route.params.loan_number ?? "",
        client_phone: "",
        client_email: "",
        channel_name: "",
        reference_type_id: "",
        trxn_reference_id: ""
      },
      days: 3,
      time3: "",
      transactions: [],
      moment: moment,
      //

      client_id: "",
      organisations: [],
      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],

      selectedFilter: {},
      filter_by: [
        {text: 'Organization Name', value: 1},
        {text: 'Loan Number', value: 2},
        {text: 'Email', value: 3},
        {text: 'Phone Number', value: 4},
      ],
      filter_by_type_id: [
        {text: 'Organization Name', value: 1},
        {text: 'Loan Number', value: 2},
        {text: 'Email', value: 3},
        {text: 'Phone Number', value: 4},
      ],
    }
  },
  components: {
    Loading,
    VueDatePicker,
  },
  //
  // watch: {
  // selectedFilter(newVal, oldVal) {
  //   if (newVal !== oldVal) {
  //     // this.payload.client_id = ""
  //     // this.payload.loan_number = ""
  //     // this.payload.client_email = ""
  //     // this.payload.client_phone = ""
  //   }
  // },
  // },
  //
  async mounted() {

    await this.setOrganisations()

    await this.setTrx()
  },

  methods: {
    ...mapActions(["getMerchants", 'getTrx', "getClients","toggleSideMenu",]),
    //
    toggleSideM() {
      this.toggleSideMenu()
    },
    //
    capitalizeFirstLetter(value) {
      if (!value) return '';
      value = value.toLowerCase()
      return value.charAt(0).toUpperCase() + value.slice(1);
    },
    //
    gotToPage(page) {
      let vm = this
      vm.payload.offset = page
      vm.offset = page
      vm.setTrx()
    },

    //
    async selectDate() {
      let vm = this
      this.payload.start = vm.formatDate(this.date[0])
      this.payload.end = vm.formatDate(this.date[1])

      await this.setTrx()
    },

    formatDate(date) {
      let d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    //
    setClientId(item) {
      this.payload.client_id = item.value
      this.searchDropdownPlaceholder = item.text
      this.searchDropdown = false
      this.searchClient = ""

      this.setTrx()
    },

    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },

    async setTrx() {
      let app = this
      app.isLoading = true
      let response = await this.getTrx(this.payload)
      if (response.status === 200) {
        this.transactions = response.message.data
        this.total = parseInt(response.message.total_count)

        // console.log("safkjnmdj  ",this.total)
      } else {
        this.transactions = []
        this.total = 0
      }

      app.isLoading = false
    },

    // Fetch and set Organisations to UI
    async setOrganisations() {
      let app = this
      app.isLoading = true;
      let response = await this.getMerchants({limit: 100})
      if (response.status === 200) {
        response.message.data.forEach(function (item) {
          let _organisations = {text: item.client_name, value: item.client_id}
          if (item.client_id === app.$route.params.client_id) {
            app.searchDropdownPlaceholder = item.client_name
          }
          app.organisations.push(_organisations)

        })
      } else {
      }
      app.isLoading = false
    },


  }
}
</script>
