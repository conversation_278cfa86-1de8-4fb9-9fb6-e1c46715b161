<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Update System User</div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <!--Full Name, Phone, Email -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium">Full Names</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-gray-400"
                 v-model="form.user_name">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium">Phone Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-gray-400" type="tel"
                 v-model="form.msisdn" disabled>
        </div>
      </div>

      <!-- Email -->
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Email</label>
        <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none "
               placeholder="" type="email" v-model="form.email_address"/>
      </div>

      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Permissions</label>
        <v-select
            v-model="form.permissions"
            :options="items"
            multiple
            placeholder="Select Permission"
        />
      </div>

      <!-- Buttons -->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'system-users'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20" @click="editUser"
                id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Save
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import { mapActions } from "vuex";

export default {
  data() {
    return {
      loading: false,
      form: {
        user_id: null,
        msisdn: null,
        email_address: null,
        user_name: null,
        permissions: [],
      },
      items: [],
    }
  },
  components: {
    Loading,
  },

  async mounted() {
    await this.setPermissions();
  },
  methods: {
    ...mapActions(["updateUser", "getSystemPermissions"]),

    async editUser() {
      let app = this;
      let permissions = [];
      for (let i = 0; i < app.form.permissions.length; i++) {
        permissions.push(app.form.permissions[i].value);
      }
      app.form.permissions = permissions;
      delete app.form.permissions_list

      const payload = this.form;

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this updates this user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateUser(payload);
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success');
              await this.$router.push({ name: 'system-users' });
              // app.resetForm();
            } else {
              app.$swal.fire('Error!', result.value.message, 'error');
            }
          });
    },

    async setPermissions() {
      let app = this;
      let payload = { page: 1, per_page: 200 };
      let response = await this.getSystemPermissions(payload);

      // console.log("KAKA response :",JSON.stringify(response.message.data))

      if (response.status === 200) {
        for (let i = 0; i < response.message.data.length; i++) {
          let item = response.message.data[i];
          app.items.push({ label: item.name, value: parseInt(item.id) });

        }

        await app.setForm()
      }
    },
    async setForm() {
      let app = this;
      let user  = this.$store.state.user;
      app.form.user_id=user.user_id
      app.form.msisdn=user.msisdn
      app.form.user_name=user.user_name
      app.form.email_address=user.email_address
      app.form.status=user.status
      app.form.permissions_list=user.permissions

      // console.log("RRRRR: ",JSON.stringify(app.form))

      let acl_list = user.permissions_list;
      let lists = [];

      for (let i = 0; i < acl_list.length; i++) {
        let label = await this.getListName(acl_list[i].id);
        lists.push({ label: label, value: acl_list[i].id });
      }

      app.form.permissions = lists;
    },

    async getListName(id) {
      let label = '';
      id = parseInt(id);
      for (let i = 0; i < this.items.length; i++) {
        if (parseInt(this.items[i].value) === id) {
          label = this.items[i].label;
          break;
        }
      }
      return label;
    },
  }
}
</script>
