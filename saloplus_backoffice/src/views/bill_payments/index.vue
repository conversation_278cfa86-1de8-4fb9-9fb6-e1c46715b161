<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Bill Payments</div>

    <div class="grid grid-cols-3 gap-x-6 mb-4 px-3">

      <div class="block">
        <label class="text-xs font-medium mb-1 block">Filter By Utility</label>
        <div class="relative mb-6">
          <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                 @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
          <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
              v-if="searchDropdown">
            <li v-for="item in bill_payments"
                class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                @click="setClientId(item)">{{ item.text }}
            </li>
          </ul>
        </div>
      </div>

      <div class="block">
      </div>

      <div class="flex justify-end">
        <div class="mt-6 text-xs">
          <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold "
                       :to="{ name: 'bill-payments-add' }">
            Create Bill Payment
          </router-link>
        </div>
      </div>

    </div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <table class="w-full mb-4 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">Ref No</th>
          <th class="py-2">Receipt No & Desc</th>
          <th class="py-2">Profile</th>
          <th class="py-2 text-center">Amount</th>
          <th class="py-2 text-center">Discount</th>
          <th class="py-2 text-center">Date</th>
          <th></th>
        </tr>
        </thead>

        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in bill_payment_trxns" :key="item.id">

          <td class="py-2 font-medium">
            <strong>{{ item.reference_number }}</strong>
          </td>

          <td class="py-2">
            <strong>+{{ item.customer ? item.customer.split(' - ')[0] : '' }}</strong>
            <br>
            <span>{{ item.customer ? item.customer.split(' - ')[1] : '' }}</span>
          </td>

          <td class="py-2 font-medium">
            <span>{{ item.receipt_number }}</span>
            <br>
            <span>{{ item.narration }}</span>
          </td>

          <td class="text-center py-2">{{ item.currency_code }}.
            {{ formatCurrency(parseFloat(item.amount).toFixed(2)) }}
          </td>

          <td class="text-center py-2">
            <span>{{ item.discount }}</span>
          </td>

          <td class="py-2 text-center">
            <span>{{ moment(item.created).format('lll') }}</span>
          </td>


        </tr>
        </tbody>


      </table>

      <!--Pagination-->
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to
          {{ limit * offset < total ? limit * offset : total }} of {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1 && offset > 1" @click="gotToPage(offset-1)">
              &larr;
            </button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2"
                    @click="gotToPage(3)">3
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'

export default {
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: this.$store.state.client_name,
      clientName: "",
      organisations: [],
      bill_payments: [],
      bill_payment_trxns: [],

      //

      //
      fullPage: true,
      total: 0,
      offset: 1,
      limit: 10,
      showDropdown: [],

      isOpen: false,
      isLoading: false,
      //

      bill_payment_params: {
        start: "",
        end: "",
        limit: 10,
        offset: "",
        sort: "",
        export: "",
        utility_id: "",
        utility_name: ""
      },

      bill_payment_trxn_params: {
        offset: "",
        limit: 10,
        start: "",
        end: "",
        sort: "",
        export: "",
        utility_id: "",
        loan_number: "",
        client_id: "",
        status: "",
        reference_number: "",
        receipt_number: "",
        client_phone: "",
      },
      time3: "",
      moment: moment,
      id: null,
      status: null,
    }
  },
  components: {
    Loading,
    VueDatePicker,
  },

  async mounted() {
    await this.setBillPayments()
    await this.setBillPaymentTransactions()
  },

  methods: {
    ...mapActions(["getBillPayments", "getBillPaymentTransactions",]),
    //
    formatCurrency(number) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    //
    formatLastLoggedOn(lastLoggedOn) {
      const momentLastLoggedOn = moment(lastLoggedOn);
      if (momentLastLoggedOn.isValid()) {
        return momentLastLoggedOn.format('lll');
      } else {
        return 'Never logged in';
      }
    },

    explodeByUnderscore(str) {
      if (!str) return '';

      const names = str.split('_');
      const firstName = names[0] || '';
      const secondName = names[1] || '';
      const thirdName = names[2] || '';

      return `${this.capitalizeFirstLetter(firstName)} ${this.capitalizeFirstLetter(secondName)} ${this.capitalizeFirstLetter(thirdName)}`;
    },

    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },

    //
    toggleSearchDropdown() {
      this.searchDropdown = !this.searchDropdown;
    },
    //
    gotToPage(page) {
      let vm = this
      vm.bill_payment_trxn_params.offset = page
      vm.offset = page
      vm.setBillPaymentTransactions()
    },

    //
    setClientId(item) {
      // if(item.text.toLowerCase()==="all"){
      //
      // }
      this.bill_payment_trxn_params.utility_id = item.value
      this.searchDropdownPlaceholder = item.text
      this.searchDropdown = false
      this.searchClient = ""
      this.setBillPaymentTransactions()
    },

    async setBillPayments() {
      let app = this
      this.isLoading = true
      let response = await this.getBillPayments(this.bill_payment_params)
      if (response.status === 200) {
        let bp0 = {text: "All", value: ""}
        app.bill_payments.push(bp0)

        response.message.data.forEach(item => {
          let bp = {text: item.bill_name, value: item.id}
          app.bill_payments.push(bp)
        })
      } else {
        app.bill_payments = []
      }

      this.isLoading = false
    },

    async setBillPaymentTransactions() {
      this.isLoading = true
      let response = await this.getBillPaymentTransactions(this.bill_payment_trxn_params)

      if (response.status === 200) {
        this.bill_payment_trxns = response.message.data
        this.total = parseInt(response.message.total_count)

        this.showDropdown = []
        for (let i = 0; i < this.bill_payment_trxns.length; i++) {
          this.showDropdown.push(false)
        }
      } else {
        this.bill_payment_trxns = []
        this.total=0
      }

      this.isLoading = false
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    async resend(user_id) {
      let app = this
      const payload = {
        user_id: user_id,
      }

      app.closeDropdown()

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this resends otp to the user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, resend!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.sendOtp(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Sent!', result.value.message, 'success')
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async editRow(row) {
      this.closeDropdown()
      await this.fillUser(row)
      // console.log("fillUser: ",JSON.stringify(row))
      await this.$router.push({name: 'system-users-edit'})
    },


  }

}
</script>
