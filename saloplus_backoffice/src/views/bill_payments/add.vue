<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Add Bill Payment</div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">

      <!--Utility Name, Description -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Utility Names</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.utility_name" placeholder="Name">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Utility Description</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.utility_desc" placeholder="Description">
        </div>

      </div>

      <!-- User Name and Pass -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">User Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                 v-model="form.user_name" placeholder="07xx xxx xxx">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">User Password</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="email"
                 v-model="form.user_pass" placeholder="<EMAIL>">
        </div>
      </div>

      <!--Buttons-->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'bill-payments'}">
          Cancel
        </router-link>

        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20" @click="createBillPayment"
                id="addSystemUser">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Submit
        </button>
      </div>

    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import moment from "moment-timezone";
import country from 'country-list-js';
import VueDatePicker from "@vuepic/vue-datepicker";

export default {
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "Search name of Organization",
      clientName: "",
      //
      // date: null,
      loading: false,
      organisations: [],

      form: {
        utility_name: "",
        utility_desc: "",
        utility_icon: "",
        user_name: "",
        user_pass: "",
        api_key: "",
      },

    }
  },
  components: {
    VueDatePicker,
    Loading
  },
  watch: {
    client_id(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.onOrganisationSelected(newVal)
      }
    },
  },
  computed: {
    filteredOrganizations() {
      console.log()
      return this.organisations.filter(org => org.text.toLowerCase().includes(this.searchClient.toLowerCase()));
    }
  },
  async mounted() {
    // Call Organisations
    await this.setOrganisations()
    await this.setRoles()
  },
  methods: {
    ...mapActions(["getMerchants", "getSystemRoles", "addUser"]),

    setClientId(item) {
      this.clientName = item.text
      this.form.account_number = item.value

      this.searchDropdown = false
      this.searchClient = ""
      this.searchDropdownPlaceholder = this.clientName
      console.log("filterOrganizations id", this.form.account_number)
    },

    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },

    // Fetch and set Organisations to UI
    async setOrganisations() {
      let app = this
      app.isLoading = true;
      let response = await this.getMerchants({limit: 100})

      if (response.status === 200) {
        response.message.forEach(function (item) {
          let _organisations = {text: item.client_name, value: item.client_account}
          // console.log("Clnt id: ", item.client_account)
          app.organisations.push(_organisations)

        })
      } else {
      }
      app.isLoading = false
    },

    async createBillPayment() {
      let app = this

      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.addUser(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              await this.$router.push({name: 'user'})
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

  }
}
</script>
