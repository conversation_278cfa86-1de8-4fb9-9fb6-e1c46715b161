<template>
  <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

  <div v-if="hasPermission!==403" class="absolute top-0 left-0 right-0 bottom-0">
    <div class="flex px-6 pt-8 pb-2">
      <div class="flex-grow font-medium">Dashboard</div>
      <div class="flex-grow text-right">
        <VueDatePicker v-model="date" range :preset-ranges="presetRanges" position="center" :clearable="false"
                       :enable-time-picker="false" @closed="selectDate">
          <template #yearly="{ label, range, presetDateRange }">
            <span @click="presetDateRange(range)">{{ label }}</span>
          </template>
        </VueDatePicker>
      </div>
    </div>

    <div class="grid grid-cols-6 gap-4 px-6 align-top">
      <div class="col-span-6 h-full overflow-auto">
        <!-- Stats for Customer -->
        <div class="grid grid-cols-3 gap-4 my-2" v-if="this.$store.state.client_id !== 0">
          <div class="block px-6 py-4 rounded-lg border-red shadow-md text-white" style="background-color: #F02459">
            <div class="text-xs">Disbursed</div>
            <div class="text-xl"> {{ accounting.format(disbursed, 2) }}</div>
          </div>

          <div class="block px-6 py-4 rounded-lg border-red shadow-md text-white" style="background-color: #41b643">
            <div class="text-xs">Interest</div>
            <div class="text-xl"> {{ accounting.format(loan_liability, 2) }}</div>
          </div>

          <div class="block px-6 py-4 rounded-lg border-red shadow-md text-white" style="background-color: #a564f1">
            <div class="text-xs">Repayment</div>
            <div class="text-xl"> {{ accounting.format(repayment, 2) }}</div>
          </div>

          <div class="block px-6 py-4 rounded-lg border-red shadow-md text-white" style="background-color: #1A203B">
            <div class="text-xs">Loan Liability</div>
            <div class="text-xl"> {{ accounting.format(loan_liability) }}</div>
          </div>

          <div class="block px-6 py-4 rounded-lg border-red shadow-md text-white" style="background-color: #F1BE24">
            <div class="text-xs ">PNL</div>
            <div class="text-xl">{{ accounting.format(transaction, 2) }}</div>
          </div>

          <div class="block px-6 py-4 rounded-lg border-red shadow-md text-white" style="background-color: #05cef3">
            <div class="text-xs ">Total Accounts</div>
            <div class="text-xl">{{ sms }}</div>
          </div>

        </div>
      </div>
      <div class="col-span-4 h-full overflow-auto pb-6">

        <!-- Stats for Super Admin -->
        <div class="grid grid-cols-2 gap-4 mb-4" v-if="this.$store.state.client_id === 0">
          <div class="block px-6 py-4 bg-white rounded-lg border shadow-md">
            <div class="text-xs">Total Stake (USD)</div>
            <div class="text-xl">{{ numberWithCommas(dashboard.total_stake) }}</div>
          </div>
          <div class="block px-6 py-4 bg-white rounded-lg border shadow-md">
            <div class="text-xs">Total Winnings (USD)</div>
            <div class="text-xl">{{ numberWithCommas(dashboard.total_winnings) }}</div>
          </div>
          <div class="block px-6 py-4 bg-white rounded-lg border shadow-md">
            <div class="text-xs">Total Bets</div>
            <div class="text-xl"> {{ parseInt(dashboard.total_bets) }}</div>
          </div>
          <div class="block px-6 py-4 bg-white rounded-lg border shadow-md">
            <div class="text-xs">Active Players</div>
            <div class="text-xl"> {{ parseInt(dashboard.active_players) }}</div>
          </div>
        </div>

        <!-- recent transactions -->
        <div class="border bg-white shadow-lg rounded-lg">
          <div class="text-xs px-6 font-bold text-neutral-400 py-4">SUMMARY TRANSACTIONS (USD)</div>
          <div class="px-6">
            <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
            <table class="w-full mb-4 table">
              <thead class="border-b-2 text-xs text-left">
              <tr class="table-row">
                <th class="py-2">Organisation</th>
                <th class="py-2">Total Loan Taken</th>
                <th class="py-2 text-center">Total Repayment</th>
                <th class="py-2">Interest</th>
                <th class="py-2">Total Count</th>
                <th class="py-2">Unique Accounts</th>
              </tr>
              </thead>
              <tbody class="text-xs text-gray-600 divide-y">
              <tr v-for="(value, key) in gameStats" :key="key">
                <td class="py-3 font-medium">{{ value.game_name }}</td>
                <td class="py-3">{{ value.total_bets }}</td>
                <td class="py-3 text-center">{{ value.total_stake }}</td>
                <td class="py-3">{{ value.total_won }}</td>
                <td class="py-3">{{
                    parseFloat(value.total_tax_on_stake) + parseFloat(value.total_tax_on_winnings)
                  }}
                </td>
                <td class="py-3">{{
                    parseFloat(value.total_stake - (parseFloat(value.total_won) + parseFloat(value.total_tax_on_stake) + parseFloat(value.total_tax_on_winnings))).toFixed(2)
                  }}
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div class="col-span-2 h-full overflow-auto">
        <!-- Commissions -->
        <div class="bg-white shadow-md border rounded-lg px-4 py-4">
          <div class="text-sm opacity-75">GGR (USD)</div>
          <div class="text-lg font-medium">
            {{ numberWithCommas((dashboard.total_stake - dashboard.total_winnings).toFixed(2)) }}
          </div>
          <div class="py-2"></div>
          <div class="text-xs opacity-75 mt-2">Stake: {{ numberWithCommas(dashboard.total_stake) }}</div>
          <div class="h-1 w-full relative bg-gray-100 rounded-full mt-2">
            <div class="bg-orange-500 absolute left-0 top-0 h-1 rounded-full"
                 :style="'width:'+ ((dashboard.total_stake - dashboard.total_winnings) / (dashboard.total_stake) *100).toFixed(2) +'%'"></div>
          </div>
          <div class="flex mt-2">
            <div class="flex-grow text-xs opacity-75">Winnings: {{ numberWithCommas(dashboard.total_winnings) }}</div>
            <div class="flex-shrink text-xs opacity-75">
              {{ ((dashboard.total_stake - dashboard.total_winnings) / (dashboard.total_stake) * 100).toFixed(2) }}%
            </div>
          </div>
        </div>

        <!-- Shortcuts -->
        <div v-if="this.$store.state.client_id === 0" class="grid grid-cols-2 my-4 gap-4">
          <router-link class="shadow-md rounded-lg bg-white py-2" :to="{ name: 'clients-add' }">
            <div class="py-2 text-center">
              <svg height="28" class="inline-block" fill="#777" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M9 6v0c0 1.65-1.35 3-3 3C4.34 9 3 7.65 3 6l0 0c-.01-1.66 1.34-3.01 2.99-3.01 1.65-.01 3 1.34 3 2.99 0 0 0 0 0 0Zm8 0v0c0 1.65-1.35 3-3 3 -1.66 0-3-1.35-3-3l0 0c-.01-1.66 1.34-3.01 2.99-3.01 1.65-.01 3 1.34 3 2.99 0 0 0 0 0 0Zm-4.07 11c.04-.33.07-.66.07-1l0-.01c0-1.58-.53-3.1-1.51-4.33l-.01 0c2.39-1.39 5.44-.57 6.83 1.83 .43.76.66 1.62.66 2.49v1H12.9ZM6 11l-.01 0c2.76-.01 5 2.23 5 4.99v1h-10v-1l0 0c-.01-2.77 2.23-5.01 4.99-5.01Z"/>
              </svg>
            </div>
            <label class="block text-center text-xs">Add Clients</label>
          </router-link>
          <router-link class="shadow-md rounded-lg bg-white py-2" :to="{ name: 'users-add' }">
            <div class="py-2 text-center">
              <svg height="28" class="inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                   stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round"
                      d="M19 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM4 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 0110.374 21c-2.331 0-4.512-.645-6.374-1.766z"/>
              </svg>
            </div>
            <label class="block text-center text-xs">Add User</label>
          </router-link>
          <a v-if="service.league" class="shadow-md rounded-lg bg-white py-2" href="#" @click="restart('league')">
            <div class="py-2 text-center">
              <svg height="28" class="inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                   stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round"
                      d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"/>
              </svg>
            </div>
            <label class="block text-center text-xs">Restart League Service</label>
          </a>
          <a v-if="service.tournament" class="shadow-md rounded-lg bg-white py-2" href="#"
             @click="restart('tournament')">
            <div class="py-2 text-center">
              <svg height="28" class="inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                   stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round"
                      d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"/>
              </svg>
            </div>
            <label class="block text-center text-xs">Restart Tournament Service</label>
          </a>
        </div>
      </div>

    </div>

  </div>

  <div v-else class="flex justify-center items-center h-full">
    <p class="text-center text-red-500">No permission</p>
  </div>
</template>

<script>
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from 'date-fns';
import '@vuepic/vue-datepicker/dist/main.css'
import Loading from 'vue-loading-overlay';
import accounting from "accounting"
import 'vue-loading-overlay/dist/vue-loading.css';

export default {
  data() {
    return {
      hasPermission: 0,
      profiles: {},
      sms: 0,
      limit: 0,
      loan_liability: 0,
      repayment: 0,
      transaction: 0,
      disbursed: 0,
      accounting: accounting,
      time3: "",
      params: {
        start: '',
        end: '',
        graph: 'monthly',
      },
      days: 0,
      type: '',
      data: {
        labels: [],
        datasets: [
          {
            label: 'Dataset 1',
            data: [],
            backgroundColor: [],
          }
        ]
      },
      data_bar: {
        labels: [],
        datasets: [
          {
            label: 'Sum Amount',
            data: [],
            borderColor: 'red',
            backgroundColor: 'red',
          }
        ]
      },
      series: [{name: 'Transactions', data: []}],
      chartOptions: {
        labels: ['New', 'Returning']
      },
      options1: {
        plotOptions: {
          radialBar: {
            dataLabels: {
              total: {
                show: true,
                label: 'TOTAL'
              }
            }
          }
        },
        labels: ['TEAM A', 'TEAM B', 'TEAM C', 'TEAM D']
      },
      options: {
        chart: {
          type: 'bar',
          height: 350
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          },
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: [],
        },
        yaxis: {
          title: {
            text: 'KES'
          }
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return "KES " + val
            }
          }
        }
      },
      options2: {
        series: [{
          name: "Desktops",
          data: [10, 41, 35, 51, 49, 62, 69, 91, 148]
        }],
        chart: {
          height: 350,
          type: 'line',
          zoom: {
            enabled: false
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'straight'
        },
        title: {
          text: 'Product Trends by Month',
          align: 'left'
        },
        grid: {
          row: {
            colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
            opacity: 0.5
          },
        },
        xaxis: {
          categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep'],
        }
      },
      loaded: false,
      key: '1',
      open: false,
      progress: false,
      chartDataStats: {
        labels: ['Active', 'New', 'Unverified', 'Dormant', 'Suspended', 'Inactive'],
        datasets: [
          {
            backgroundColor: ['#E46651', '#00D8FF', '#f1be25', '#1a203b', '#f12559', 'blue'],
            data: [0, 0, 0, 0, 0, 0]
          }
        ]
      },
      uniqueStats: 'key',

      /////// old
      ///
      dashboard: {
        total_bets: 0,
        total_stake: 0,
        players: 0,
        active_players: 0,
        total_winnings: 0
      },
      gameStats: [],
      moreParams: {
        start: "",
        filter: "",
        end: "",
        client_id: this.$store.state.client_id
      },
      date: null,
      tab: 'add-client',
      summaryData: {
        "TotalClients": 0,
        "actualBalance": 0,
        "avaliableBalance": 0,
        "staffMembers": 0,
        "currency": "KES"
      },
      isLoading: false,
      isLoadingCal: false,
      fullPage: true,
      divPage: false,
      countries: [],
      showAccountTopUp: false,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
    }
  },
  components: {
    Loading,
    VueDatePicker
  },
  async mounted() {
    if(this.$store.state.role!==1){
      await this.$router.push({name: 'organisations'})
    }

    this.loaded = false
    await this.getStats(this.params)
    this.type = 'monthly'
    this.loaded = true
  },
  methods: {
    ...mapActions(["Stats"]),

    //
    async getStats(params = null) {
      let app = this
      if (this.$store.state.role !== 1) {
        if (!params) {
          params.clientId = this.$store.state.account.id
        }
      }

      let response = await this.Stats(params)

      // console.log("stats: " + JSON.stringify(response))

      if (response.status === 200) {
        this.profiles = response.message.profiles
        this.sms = response.message.sms
        this.limit = response.message.limit
        this.loan_liability = response.message.loan_liability
        this.repayment = response.message.repayment
        this.transaction = response.message.transaction.sum
        this.disbursed = response.message.disbursed.sum
        // pie
        this.chartDataStats.datasets[0].data = [
          parseInt(response.message.profiles.active),
          parseInt(response.message.profiles.new_p),
          parseInt(response.message.profiles.unverified),
          parseInt(response.message.profiles.dormant),
          parseInt(response.message.profiles.suspended),
          parseInt(response.message.profiles.inactive)
        ]
        //graph
        this.options.xaxis.categories = response.message.graph.label
        this.series[0].data = response.message.graph.amount
      } else if (response.status === 403) {
        app.hasPermission = 403

        await app.$router.push({name: 'organisations'})

      }
    },

    //
    async setTransaction(type) {
      await this.getStats({graph: type})
      this.type = type
    },

    // Internal Method
    async selectDate() {
      let vm = this
      // console.log("this.moreParams: ",this.moreParams)
      this.moreParams.start = vm.formatDate(this.date[0])
      this.moreParams.end = vm.formatDate(this.date[1])

      await vm.getStats()
    },

    //
    formatDate(date) {
      let d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    //
    numberWithCommas(x) {
      x = parseFloat(x).toFixed(2)
      return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },

  }
  ,
}
</script>
