<template>
  <div class="relative mx-auto max-w-4xl">
    <div class="w-[420px] mx-auto mt-20 bg-white border rounded-lg h-auto p-5">
      <alert v-show="type && message" :type="type" :message="message"></alert>
      <div class="h-20 text-xl font-bold px-8 pt-6">
        <img src="../../assets/img.png" />
      </div>
      <form @submit.prevent="resetPass()" style="margin-top: 30px">
        <div class="text-xl text-center my-2 font-medium">Set new password</div>
        <div class="block my-1">
          <label class="text-sm text-neutral-500">Enter verification code</label>
          <input type="password" class="px-4 py-2 border rounded-md w-full block outline-none" placeholder="xxxxxx" v-model="form_reset.otp_code">
        </div>
        <div class="my-4 border-t"></div>
        <div class="block my-1">
          <label class="text-sm text-neutral-500">Enter new password</label>
          <input type="password" class="px-4 py-2 border rounded-md w-full block outline-none" v-model="form_reset.new_password">
        </div>
        <div class="block my-1">
          <label class="text-sm text-neutral-500">Confirm new password</label>
          <input type="password" class="px-4 py-2 border rounded-md w-full block outline-none" v-model="confirmPassword">
        </div>
        <button type="submit" class="block text-center my-4 px-4 py-2 w-full bg-primary text-white rounded-md font-bold">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat> <span id="savepasswordBTN">Save new password</span>
        </button>
      </form>
    </div>
  </div>
</template>

<script>
import Swal from 'sweetalert2/dist/sweetalert2.js'
import store from "../../store";
import $ from 'jquery'
import {mapActions} from "vuex";
import Alert from "@/components/alert.vue";
export default {
  components: {Alert},
  data: function () {
    return {
      loading: false,
      type: null,
      message: null,
      confirmPassword: "",
      password: "",
      verificationCode: "",
      form_reset: {
        username: this.$store.state.user_name,
        new_password: "",
        otp_code: "",
      },
    };
  },
  mounted() {
    // if (!this.$store.state.user_name) {
    //   this.$router.push({
    //     name: 'login'
    //   });
    // }
  },
  methods: {
    ...mapActions(["passwordReset", "fillUser", "fillMessage"]),

    async resetPass() {
      let app = this
      app.loading = true
      $('#savepasswordBTN').html(' Please Wait ...');


      // if (this.new_password !== this.confirmPassword){
      //   this.type = 'danger'
      //   this.message = "Password don't match"
      //   return
      // }
      let payload = {
        username: app.form_reset.username,
        new_password: btoa(app.form_reset.new_password),
        otp_code: btoa(app.form_reset.otp_code)
      }

      // const payload = this.form_reset
      console.log("VERIFY THIS", payload)
      let response = await this.passwordReset(payload);
      console.log("response: ", JSON.stringify(response))
      if (response.status === 200) {
        console.log("got here status: ", response.status)
        this.type = 'success'
        this.message = response.message
        await app.fillUser(app.form_reset.username);
        await app.fillMessage(app.message);
        app.$router.push({name: 'login'});
        return
      }
      else {
        console.log("got here status 1: ", response.status)
        this.type = 'danger'
        this.message = response.message
      }
      console.log("got here status 2: ", response.status)
      app.loading = false
      $('#savepasswordBTN').html('Save new password');
      app.$router.push({name: 'verify'});
    },
  }
}
</script>
