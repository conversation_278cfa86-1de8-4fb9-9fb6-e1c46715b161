<template>
  <div class="relative mx-auto max-w-4xl">
    <div class="w-[420px] mx-auto mt-20 bg-white border rounded-lg h-auto p-5">
      <alert v-show="type && message" :type="type" :message="message"></alert>
      <div class="h-20 text-xl font-bold px-8 pt-6">
        <img src="../../assets/img.png" />
      </div>
      <form @submit.prevent="forgotPass" style="margin-top: 30px">
        <div class="text-xl text-center my-2 font-medium">Forgot Password?</div>
        <div class="block my-1">
          <label class="text-sm text-neutral-500">Username</label>
          <input class="px-4 py-2 border rounded-md w-full block outline-none" v-model="form_forgot.username">
        </div>
        <button type="submit" class="text-center my-4 px-4 py-2 block w-full bg-primary text-white rounded-md font-bold">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat> <span id="forgotBTN">Next</span>
        </button>
        <router-link :to="{name: 'login'}" href="#" class="text-sm underline text-primary">Login</router-link>
      </form>
    </div>
  </div>
</template>

<script>
import Swal from 'sweetalert2/dist/sweetalert2.js'
import store from "../../store";
import $ from 'jquery'
import {mapActions} from "vuex";
import Alert from "@/components/alert.vue";
export default {
  components: {Alert},
  data: function () {
    return {
      loading: false,
      type: null,
      message: null,
      phone: "",
      password: "",
      form_forgot: {
        username: this.$store.state.user_name,
        dial_code: "254"
      },
    };
  },
  methods: {
    ...mapActions(["passwordReset", "passwordForgot", "fillUser"]),

    page(page) {
      this.active = page
    },

    async forgotPass() {
      let app = this
      app.loading = true
      const payload = this.form_forgot
      $('#forgotBTN').html(' Please Wait ...');

      console.log('Payload before passwordForgot:', payload);

      let response = await this.passwordForgot(payload);

      if (response.status === 200) {
        this.type = 'success'
        this.message = response.message
        await this.$store.dispatch('fillUserName',app.form_forgot.username);

        app.$router.push({name: 'verify'});
        return
      }
      else {
        this.type = 'danger'
        this.message = response.message
      }
      app.loading = false
      $('#forgotBTN').html('Next');

    },

    resed() {
      this.$router.push({name: 'verify'});
    },


   /* resetPassword() {
      let vm = this
      vm.loading = true
      $('#forgotBTN').html(' Please Wait ...');
      $.post({
        url: store.state.rootUrl + 'auth/v1/reset',
        post: "POST",
        data: JSON.stringify({
          username: vm.phone,
          country_code: "KEN",
        }),
        headers: {
          "X-App-Key": store.state.systemToken,
          "X-Authorization-Key": store.state.channel,
          "X-Requested-With": "XMLHttpRequest",
          "Content-Type": "application/json"
        },
        cache: false,
        contentType: false,
        processData: false,

        success: function (response, status, jQxhr) {
          $('#forgotBTN').html('Next');
          vm.loading = false
          if (response.data.code == 200) {
            vm.$cookies.set("phoneForgot",vm.phone);
            Swal.fire({
              icon: 'success',
              title: 'Password Reset successfully',
              toast: true,
              position: 'top-end',
              showConfirmButton: false,
              timer: 3000,
              timerProgressBar: true,
              didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
              }
            })

            this.$router.push({
              name: 'verify'
            });

          } else {
            Swal.fire({
              icon: 'error',
              title: response.data.message,
              toast: true,
              position: 'top-end',
              showConfirmButton: false,
              timer: 5000,
              timerProgressBar: true,
              didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
              }
            });
          }
        },
        error: function (jQxhr, status, error) {
          vm.loading = false
          $('#forgotBTN').html('Next');
          var errorMessage = "";
          if (jQxhr.responseJSON.data.message) {
            errorMessage = ": " + jQxhr.responseJSON.data.message;
          }
          Swal.fire({
            icon: 'error',
            title: jQxhr.responseJSON.statusDescription + "" + errorMessage,
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            didOpen: (toast) => {
              toast.addEventListener('mouseenter', Swal.stopTimer)
              toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
          });
        }
      });
    }*/
  }
}
</script>
