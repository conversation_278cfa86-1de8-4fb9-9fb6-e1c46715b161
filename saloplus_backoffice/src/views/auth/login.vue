<template>
  <div class="relative mx-auto max-w-4xl">
    <div class="w-[420px] mx-auto mt-20 bg-white border rounded-lg h-auto p-5">
      <alert v-show="type && message" :type="type" :message="message"></alert>
      <div class="h-20 text-xl font-bold px-8 pt-6">
        <img src="../../assets/img.png" alt="logo"/>
      </div>
      <div class="text-xl text-center my-2 font-medium" style="margin-top: 30px">Login</div>
      <form @submit.prevent="submit">
        <div class="block my-1" v-show="mc !== 1">
          <label class="text-sm text-neutral-500">Username</label>
          <input class="px-4 py-2 border rounded-md w-full block outline-none" v-model="form.username">
        </div>
        <div class="block my-1" v-show="mc !== 1">
          <label class="text-sm text-neutral-500">Password</label>
          <input type="password" class="px-4 py-2 border rounded-md w-full block outline-none" v-model="form.password">
        </div>
        <div class="block my-1" v-show="showPin && timerCount > 0 && mc !== 1">
          <label class="text-sm text-neutral-500">Enter OTP</label>
          <input type="password" class="px-4 py-2 border rounded-md w-full block outline-none"
                 v-model="form.verification_code">
          <label class="text-sm text-primary">OTP has been sent to register email / phone</label>
        </div>
        <div class="block my-1" v-show="mc === 1">
          <label class="text-sm text-neutral-500">Select Client/Brand</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="client_id">
            <option selected>Select Client</option>
            <option v-for="item in clients" :value="item.id">{{ item.name }}</option>
          </select>
        </div>
        <div class="flex gap-2" v-show="showPin && timerCount > 0 && mc !== 1">
          <label class="w-1/2">00:{{ timerCount }}</label>
          <label class="text-right w-1/2 text-sm underline text-primary"><a href="#" @click="submit()"> Resend
            OTP</a></label>
        </div>
        <button type="button"
                class="block text-center my-4 px-4 py-2  w-full bg-primary text-white rounded-md font-bold"
                v-show="showPin && timerCount > 0" @click="submitCode()">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          <span id="verifyBTN">Login</span>
        </button>
        <button type="submit" class="block text-center my-4 px-4 py-2 w-full bg-primary text-white rounded-md font-bold"
                v-show="!showPin || timerCount <= 0">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          <span id="signin">Validate & Continue</span>
        </button>
        <div class="flex gap-2">
          <router-link :to="{name: 'forgot-password'}" href="#" class="w-1/2 text-sm underline text-primary">Forgot
            password?
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script>

// import Swal from 'sweetalert2/dist/sweetalert2.js'
// import store from "../../store";
// import $ from 'jquery'
import {mapActions} from "vuex";
import Alert from "@/components/alert.vue";
import $ from "jquery";
import Swal from "sweetalert2";

export default {
  components: {Alert},
  data: function () {
    return {
      loading: false,
      type: null,
      message: null,
      phone: "",
      password: "",
      code: "",
      showPin: false,
      pin: "",
      timerCount: 120,
      form: {
        username: this.$store.state.user_name,
        password: "",
        otp_code: "",
        dial_code: "254"
      },
      client_id: null,
      clients: [],
      mc: null
    };
  },
  mounted() {
    if (this.$store.state.message) {
      this.message = this.$store.state.message
      this.type = "success"
    }
  },
  watch: {
    timerCount: {
      handler(value) {

        if (value > 0) {
          setTimeout(() => {
            this.timerCount--;
          }, 1000);
        }

      },
      immediate: true // This ensures the watcher is triggered upon creation
    },
    async client_id() {
      await this.fillClientId(this.client_id)
      await this.$router.push({name: 'dashboard'})
    }
  },
  methods: {
    ...mapActions(["LogIn", "LogInCode", "passwordForgot", "fillUserName",
      "fillMessage","fillPermissions", "fillClientId", "fillClientList"]),
    //
    async submit() {
      let app = this
      await app.fillMessage(null);
      app.loading = true
      if (!app.form.username || !app.form.password) {
        app.message = "All fields are required"
        app.type = "danger"
        app.loading = false
        return
      }

      app.form.password = btoa(app.form.password)

      const payload = app.form

      // console.log("log in req: " + JSON.stringify(app.form))
      let response = await this.LogIn(payload);
      // console.log("log in res: " + JSON.stringify(response))

      if (response.status === 200) {
        app.type = 'success'
        app.message = response.message
        app.showPin = true

        let permissionIds = this.extractPermissionIds(response.message.data.permissions)
        // console.log("res permissionIds: " + JSON.stringify(permissionIds))
        await this.fillPermissions(permissionIds)
        this.$store.state.user_name = app.form.username

        await app.$router.push({name: 'dashboard'})
      } else {
        this.message = response.message
        if (response.status === 401 || response.status === 400) {
          app.type = 'danger'

        } else if (response.status === 406) {
          this.page('reset')
          this.type = 'warning'
        } else if (response.status === 205 || response.status === 201) {
          // this.$store.state.user_name = app.form.username
          await this.forgotPass()
          // await app.$router.push({name: "verify"})
          // console.log("Verify 205 OK: " + this.$store.state.user_name + " " + JSON.stringify(response.message))
        } else if (response.status === 410) {
          this.$store.state.user_name = app.form.username
          await app.$router.push("/verify")
          // console.log("Verify 410 OK: " + this.$store.state.user_name + " " + JSON.stringify(response.message))
        } else {
          //this.page('reset')
          this.type = 'danger'
        }
      }
      app.loading = false
    },
    extractPermissionIds(permissionsData) {
      let permissionIds = []
      for (let i = 0; i < permissionsData.length; i++) {
        permissionIds.push(permissionsData[i].id)
      }
      // console.log("permissionIds:",permissionIds);
      return permissionIds
    },

    async forgotPass() {
      let app = this
      app.loading = true
      delete app.form.password
      delete app.form.otp_code
      const payload = app.form
      $('#forgotBTN').html(' Please Wait ...');


      let response = await this.passwordForgot(payload);
      if (response.status === 200) {
        this.type = 'success'
        this.message = response.message
        await this.fillUserName(app.form.username);
        //
        await app.$router.push({name: "verify"})
        return
      } else {
        this.type = 'danger'
        this.message = response.message
      }
      app.loading = false
      // $('#forgotBTN').html('Next');
    },

    async submitCode() {
      let app = this
      await app.fillMessage(null);
      app.loading = true
      if (!app.form.username || !app.form.password || !app.form.verification_code) {
        app.message = "All fields are required"
        app.type = "danger"
        app.loading = false
        return
      }
      const payload = this.form

      let response = await this.LogInCode(payload);
      if (response.status === 200) {
        if (response.message.data.mc === 1 && response.message.data.clients) {
          app.mc = 1
          app.clients = response.message.data.clients
          await app.fillClientList(app.clients);
        } else {
          await app.$router.push({name: 'dashboard'})
        }
      } else {
        this.message = response.message
        this.type = 'danger'
      }
      app.loading = false
    },

    page(page) {
      this.active = page
    },

    getPermission(name) {
      //let app = this
      if (this.$store.state.role === 1) {
        return true
      }
      // kiron
      if (name === 'Read Kiron Statistics' && this.$store.state.role === 8) {
        return true
      }
      let permissions = this.$store.state.permission
      for (let item of permissions) {
        if (item.name === name) {
          //console.log('returning true: ' + name)
          return true
        }
      }
      //console.log('returning false: ' + name)
      return false

    },

    //
  }
}
</script>
