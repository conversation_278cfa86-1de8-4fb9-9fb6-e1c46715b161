<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex px-6 py-4">
      <div class="pr-3" style="cursor: pointer" @click="expandNav">
        <svg v-if="this.$store.state.expandedNav" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
             stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round"
                d="M11.25 9l-3 3m0 0l3 3m-3-3h7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
             stroke="currentColor" class="w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round"
                d="M12.75 15l3-3m0 0l-3-3m3 3h-7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
      </div>
      <div class="flex-grow font-medium">Upload File</div>
    </div>
    <div class="block px-6 py-3">
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block">Folder<span class="text-red-600"> *</span></label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="folder">
            <option value="images">Images</option>
            <option value="team_logo">Team Logo</option>
            <option value="racing">Racing</option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="name">
        </div>
      </div>
      <div class="block">
        <label class="text-xs mb-1 block ">Image</label>
        <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="file" id="image"
               name="image" placeholder="Enter Image" accept="image/*" required>
      </div>
      <div class="block">
        <button class="flex-block px-4 py-2 bg-primary rounded-md font-medium mt-4 w-full" @click="addFile()"
                id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Upload
        </button>
        <h3 class="mt-4 text-blue-600">{{ link }}</h3>
      </div>
    </div>

  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import $ from 'jquery'
import {mapActions} from "vuex";

export default {
  data() {
    return {
      loading: false,
      link: '',
      name: '',
      folder: 'images',
    }
  },
  components: {
    Loading
  },
  methods: {
    ...mapActions(["uploadDoc"]),
    expandNav() {
      this.$store.state.expandedNav = !this.$store.state.expandedNav
    },

    async addFile() {
      let app = this
      let payload = new FormData();
      payload.append('name', app.name)
      payload.append('folder', app.folder)
      payload.append('image', $('#image')[0].files[0])

      if (document.getElementById("image").files.length === 0) {
        app.$swal.fire('Missing!', "Image is required", 'error')
        return;
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this add this file!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.uploadDoc(payload)

        },
      })
          .then(async (result) => {
            if (result.value.status === 201) {
              app.link = result.value.message
              app.$swal.fire('Added!', result.value.message, 'success')
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

  },
}
</script>
