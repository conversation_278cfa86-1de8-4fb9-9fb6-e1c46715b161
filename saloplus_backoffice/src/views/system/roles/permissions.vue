<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex px-6 py-4">
      <div class="pr-3" style="cursor: pointer" @click="expandNav">
        <svg v-if="this.$store.state.expandedNav" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
             stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round"
                d="M11.25 9l-3 3m0 0l3 3m-3-3h7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
             stroke="currentColor" class="w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round"
                d="M12.75 15l3-3m0 0l-3-3m3 3h-7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
      </div>
      <div class="flex-grow font-medium">Permissions</div>
    </div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">#</th>
          <th class="py-2">ID</th>
          <th class="py-2">Permission Name</th>
          <th class="py-2 text-center">Status</th>
          <th class="py-2 text-center">Created</th>
          <th></th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in items" :key="item.id">
          <td class="py-3 font-medium">{{ index + 1 }}</td>
          <td class="py-3 font-medium"><strong>{{ item.id }}</strong></td>
          <td class="py-3 font-medium">{{ item.name }}</td>
          <td class="py-2 text-center">
            <button v-if="parseInt(item.status) === 1" class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: green">
              Activate
            </button>
            <button v-else-if="parseInt(item.status) === 3"
                    class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: red">
              Deactivated
            </button>
            <button v-else
                    class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: purple">
              Inactive
            </button>
          </td>
          <td class="py-2 text-center">{{ moment(item.created_at).format('lll') }}</td>
        </tr>
        </tbody>
      </table>

      <div v-show="total>limit" class="flex w-full text-xs items-center">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to
          {{ limit * offset < total ? limit * offset : total }} of {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1 && offset > 1" @click="gotToPage(offset-1)">
              &larr;
            </button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2"
                    @click="gotToPage(3)">3
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>

    </div>

  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import store from "@/store";
import $ from 'jquery'
import moment from "moment";
import numeral from "numeral"
import {mapActions} from "vuex";

export default {
  data() {
    return {
      isLoading: false,
      items: [],
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      moment: moment,
    }
  },
  components: {
    Loading
  },

  mounted() {

    this.getItems()
  },
  methods: {
    ...mapActions(["getSystemPermissions"]),
    //
    expandNav() {
      this.$store.state.expandedNav = !this.$store.state.expandedNav
    },

    //
    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.page = page
      vm.getItems()
    },

    //
    async getItems() {
      let vm = this
      vm.isLoading = true;
      let payload = {page: 1, per_page: 100,}

      let response = await this.getSystemPermissions(payload)

      if (response.status) {
        let data = response.message
        vm.items = data.data
        // this.total = parseInt(response.message.total)
        vm.total = vm.items.length
      }
      vm.isLoading = false;

    },

  },
}
</script>
