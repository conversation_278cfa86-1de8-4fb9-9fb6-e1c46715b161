<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex px-6 py-4">
      <div class="pr-3" style="cursor: pointer" @click="expandNav">
        <svg v-if="this.$store.state.expandedNav" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
             stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round"
                d="M11.25 9l-3 3m0 0l3 3m-3-3h7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
             stroke="currentColor" class="w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round"
                d="M12.75 15l3-3m0 0l-3-3m3 3h-7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
      </div>
      <div class="flex-grow font-medium">Edit Role</div>
    </div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Role Name</label>
        <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.role_name">
      </div>
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Role Description</label>
        <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.description">
      </div>
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Permissions</label>
        <v-select
            class=""
            v-model="form.permissions"
            :options="items"
            multiple
            placeholder="Select Permission"
        />
      </div>
      <div class="gap-4 block text-sm text-right">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'system-roles'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2" @click="editRole()"
                id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Save
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";

export default {
  data() {
    return {
      form: {
        role_id: null,
        role_name: null,
        description: null,
        permissions: []
      },
      items: [],
      loading: false
    }
  },
  components: {
    Loading
  },
  async mounted() {
    await this.setPermissions()
  },
  methods: {
    ...mapActions(["updateSystemRole", "getSystemPermissions"]),
    //
    expandNav() {
      this.$store.state.expandedNav = !this.$store.state.expandedNav
    },

    async editRole() {
      let app = this
      app.loading = true
      let acl = []

      if (app.form.permissions.length !== 0) {
        this.form.permissions.forEach(function (item) {
          acl.push(item.value)
        })

        this.form.permissions = acl
      }

      delete this.form.permissions_list

      if (!this.checkFormValidity(app.form)) {
        // Handle the case where there are invalid elements
        console.log("There are null or empty elements in the form.");
        app.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: `Fill in all data.`
        });
        return
      }

      const payload = app.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds updates this role!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          $('#addMember').html(' Please Wait ...');
          return await this.updateSystemRole(payload)

        },
      })
          .then(async (result) => {
            $('#addMember').html('Save');
            app.loading = false
            if (result.value.status === 200) {
              app.$swal.fire({
                title: 'Updated!',
                text: result.value.message,
                icon: 'success'
              }).then(async (result) => {
                await app.$router.push({name: 'system-roles'})
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },


    async setPermissions() {
      let app = this;
      let payload = {page: 1, per_page: 200};
      let response = await this.getSystemPermissions(payload);

      if (response.status === 200) {
        for (let i = 0; i < response.message.data.length; i++) {
          let item = response.message.data[i];
          app.items.push({label: item.name, value: parseInt(item.id)});

        }

        await app.setForm()
      }
    },

    async setForm() {
      let app = this;
      let role = this.$store.state.roleData;

      app.form.role_id = role.role_id
      app.form.role_name = role.role_name
      app.form.description = role.description ?? ""
      app.form.permissions_list = role.permissions_list

      let acl_list = role.permissions_list;
      let lists = [];

      for (let i = 0; i < acl_list.length; i++) {
        let label = await this.getListName(acl_list[i].id);
        lists.push({label: label, value: acl_list[i].id});
      }

      app.form.permissions = lists;

    },

    async getListName(id) {
      let label = '';
      id = parseInt(id);
      for (let i = 0; i < this.items.length; i++) {
        if (parseInt(this.items[i].value) === id) {
          label = this.items[i].label;
          break;
        }
      }
      return label;
    },
  }
}
</script>
