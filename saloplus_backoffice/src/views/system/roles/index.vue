<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex px-6 py-4">

      <div class="flex-grow font-medium">Roles</div>
    </div>

    <div class="w-full flex mb-4 px-6 text-xs gap-2">
      <div class="flex-grow gap-2 flex">
        <input class="inline-block px-4 py-2 rounded-md border bg-neutral-100 outline-none" placeholder="Search"/>
      </div>
      <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold" :to="{ name: 'system-roles-add' }">Add
        Role
      </router-link>
    </div>

    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-3">#</th>
          <th class="py-3">Role Name</th>
          <th class="text-center py-3">Status</th>
          <th class="py-3">Date Created</th>
          <th class="py-3">Action</th>
          <th></th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in items" :key="item.role_id">
          <td class="py-3 w-1 pr-8">{{ index + 1 }}</td>
          <td class="py-3 font-medium">{{ item.role_name }}</td>
          <td class="text-center  py-3">
            <button class="inline-block px-4 py-2 rounded-md text-white" :class="getStatusBg(item.status)">
              {{ parseInt(item.status) === 1 ? 'Active' : parseInt(item.status) === 3 ? 'Deactivated' : 'Inactive' }}
            </button>
          </td>
          <td class="py-3">{{ moment(item.created_at).format('lll') }}</td>
          <td class="py-3 text-center relative w-24">
            <button class="px-3 py-1 border rounded-md" @click="editRole(item)">Edit Role</button>
          </td>
        </tr>
        </tbody>
      </table>

      <div v-show="total>limit" class="flex w-full text-xs items-center mb-2">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to
          {{ limit * offset < total ? limit * offset : total }} of {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3"
                    v-show="Math.ceil(total/limit) > 1 && offset > 1"
                    @click="gotToPage(offset-1)">
              &larr;
            </button>

            <button class="p-2 px-4"
                    :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">
              1
            </button>

            <button class="p-2 px-4"
                    :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">
              2
            </button>

            <button class="p-2 px-4"
                    :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2"
                    @click="gotToPage(3)">
              3
            </button>

            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>

    </div>

  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import store from "@/store";
import $ from 'jquery'
import moment from "moment";
import numeral from "numeral"
import {mapActions} from "vuex";

export default {
  data() {
    return {
      isOpen: false,
      isLoading: false,
      items: [],
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      moment: moment,
      permissions: [],
    }
  },
  components: {
    Loading
  },

  mounted() {

    this.getItems()
  },
  methods: {
    ...mapActions(["getSystemRoles", "fillData", "fillRole"]),

    //
    expandNav() {
      this.$store.state.expandedNav = !this.$store.state.expandedNav
    },

    //
    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.getItems()
    },

    //
    async editRole(userData) {
      let app = this
      await this.fillRole(userData)
      app.$router.push({name: 'system-roles-edit'})
    },

    //
    async getItems() {
      let vm = this
      vm.isLoading = true;
      let payload = {page: vm.offset, per_page: vm.limit}

      let response = await this.getSystemRoles(payload)
      if (response.status === 200) {
        vm.items = response.message
        vm.total = vm.items.length
        // vm.total = parseInt(response.message.total_count)
        console.log("LLL", vm.total)
      }

      vm.isLoading = false;
    },

    //
    getRoleBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-amber-400'
        case 4:
          return 'bg-teal-600'
        case 5:
          return 'bg-sky-600'
        default:
          return 'bg-purple-600'
      }
    },

    //
    getStatusBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-red-600'
        default:
          return 'bg-purple-600'
      }
    },

    //
    async permissionList(list) {
      this.permissions = list
      this.isOpen = true
    },

  },
}
</script>
