<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">System Users</div>


    <div v-if="this.$store.state.isSuperRole" class="w-full flex mb-4 px-6 text-xs gap-2 bg">
      <div class="flex-grow gap-2 flex"></div>

      <div class="px-4 mt-0">
        <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold " :to="{ name: 'system-users-add' }">
          Add System User
        </router-link>
      </div>
    </div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <table class="w-full mb-4 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">User</th>
          <th class="py-2">Client Acc</th>
          <th class="py-2">Username</th>
          <th class="py-2">Role</th>
          <th class="py-2">Logins Attempts</th>
          <th class="py-2">Last Login</th>
          <th class="text-center py-2">Status</th>
          <th class="text-center">Actions</th>
          <th></th>
        </tr>
        </thead>

        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in data" :key="item.id">
          <td class="py-3 font-medium">{{ explodeByUnderscore(item.user_name) }}</td>

          <td class="py-3">
            <span>{{ item.client_name }}</span>
            <br>
            <span>{{ item.account_number }}</span>
          </td>

          <td class="py-3">
            <span>{{ item.email_address }}</span>
            <br>
            <span>+{{ item.msisdn }}</span>
          </td>

          <td class="py-3">{{ getRoleName(item.role_id) }}</td>

          <td class="py-3">
            <span style="color: #1bdc00">Successful:{{ item.cumulative_success_login }}</span>
            <br>
            <span style="color: #dd0000">Failed:{{ item.cumlative_failed_attempts }}</span>
          </td>

          <td class="py-3">{{ formatLastLoggedOn(moment(item.last_logged_on).format('lll')) }}</td>

          <td class="py-3 text-center">
            <button v-if="parseInt(item.status) === 1" class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: green">
              Active
            </button>
            <button v-else-if="parseInt(item.status) === 3"
                    class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: red">
              Deactivated
            </button>
            <button v-else
                    class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: purple">
              Inactive
            </button>
          </td>

          <td class="py-2 text-center relative w-24">
            <div class="relative inline-block">
              <button class="px-3 py-1 flex items-center space-x-1"
                      @click="toggleDropdown(index)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 250px; text-align: left;">
                <ul class="py-2 ">

                  <li v-if="parseInt(item.status) === 1"
                      @click.prevent="editRow(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200"> Edit User
                      <b>{{ explodeByUnderscore(item.user_name) }}</b></a>
                  </li>

                  <li @click="resend(parseInt(item.user_id))">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Send OTP to
                      <b>{{ explodeByUnderscore(item.user_name) }}</b></a>
                  </li>

                </ul>
              </div>

            </div>
          </td>
        </tr>
        </tbody>


      </table>

      <!--Pagination-->
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to
          {{ limit * offset < total ? limit * offset : total }} of {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1 && offset > 1" @click="gotToPage(offset-1)">
              &larr;
            </button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2"
                    @click="gotToPage(3)">3
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'

export default {
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: this.$store.state.client_name,
      clientName: "",
      organisations: [],
      data: [],
      roles: [],

      //
      fullPage: true,
      total: 0,
      offset: 1,
      limit: 10,
      showDropdown: [],

      isOpen: false,
      isLoading: false,
      //

      moreParams: {
        start: "",
        end: "",
        limit: 10,
        page: 1,
        status: "3",
        client_id: "",
      },
      time3: "",
      moment: moment,
      id: null,
      status: null,
    }
  },
  components: {
    Loading,
    VueDatePicker,
  },

  watch: {
    searchClient(newVal, oldVal) {
      if (newVal !== oldVal && newVal !== "") {
        this.filterOrganizations();
      }
    }
  },
  async mounted() {
    await this.setRoles()

    if (!this.$store.state.isSuperRole) {
      this.moreParams.client_id = this.$route.params.client_id
    }

    await this.setUsers()
  },

  methods: {
    ...mapActions(["getUsers", "getSystemRoles", "sendOtp", "fillUser",]),

    //
    formatLastLoggedOn(lastLoggedOn) {
      const momentLastLoggedOn = moment(lastLoggedOn);
      if (momentLastLoggedOn.isValid()) {
        return momentLastLoggedOn.format('lll');
      } else {
        return 'Never logged in';
      }
    },

    explodeByUnderscore(str) {
      if (!str) return '';

      const names = str.split('_');
      const firstName = names[0] || '';
      const secondName = names[1] || '';
      const thirdName = names[2] || '';

      return `${this.capitalizeFirstLetter(firstName)} ${this.capitalizeFirstLetter(secondName)} ${this.capitalizeFirstLetter(thirdName)}`;
    },

    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },

    //
    gotToPage(page) {
      let vm = this
      vm.moreParams.offset = page
      vm.offset = page
      vm.setUsers()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    async resend(user_id) {
      let app = this
      const payload = {
        user_id: user_id,
      }

      app.closeDropdown()

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this resends otp to the user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, resend!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.sendOtp(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Sent!', result.value.message, 'success')
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async editRow(row) {
      this.closeDropdown()
      await this.fillUser(row)
      console.log("fillUser: ", JSON.stringify(row))
      await this.$router.push({name: 'system-users-edit'})
    },

    async setUsers() {
      this.isLoading = true
      let response = await this.getUsers(this.moreParams)
      this.data = response.message.data

      this.total = parseInt(response.message.total_count)
      // console.log("USERS NI: ", JSON.stringify(this.data))

      this.showDropdown = []
      for (let i = 0; i < this.data.length; i++) {
        this.showDropdown.push(false)
      }

      this.isLoading = false
    },

    // Fetch System roles
    async setRoles() {
      let app = this
      let response = await this.getSystemRoles({limit: 100})
      if (response.status === 200) {
        response.message.forEach(function (item) {
          let role = {text: item.role_name, value: item.role_id}
          app.roles.push(role)
        })
      }
    },

    // get role name from role id
    getRoleName(role_id) {
      let role = this.roles.find(role => role.value === role_id)
      return role ? role.text : 'N/A'
    },

  }

}
</script>
