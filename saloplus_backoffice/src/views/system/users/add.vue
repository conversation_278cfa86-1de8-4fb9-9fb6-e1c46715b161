<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Add System User ({{searchDropdownPlaceholder}})</div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">

      <div v-if="this.$store.state.merchant_account===null" class="relative mb-6">
        <label class="text-xs font-medium mb-1 block">Filter By Organisation <strong
            v-show="clientName">({{ clientName }})</strong></label>
        <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
               @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
        <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
            v-if="searchDropdown">
          <li v-for="item in organisations" class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
              @click="setClientId(item)">{{ item.text }}
          </li>
        </ul>
      </div>

      <!--Full Name, Phone, Email -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">First Names</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.first_name" placeholder="John">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Middle Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.middle_name" placeholder="Smith">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Last Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.last_name" placeholder="Doe">
        </div>
      </div>

      <!-- Phone, Email -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Phone Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                 v-model="form.msisdn" placeholder="07xx xxx xxx">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Email Address</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="email"
                 v-model="form.email_address" placeholder="<EMAIL>">
        </div>
      </div>

      <!--Nationality, Identify Type, ID number -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Nationality</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.nationality">
            <option value="" disabled selected>Select Country</option> <!-- Placeholder option -->
            <option v-for="nationality in nationalities" :value="nationality.value">
              {{ nationality.text }}
            </option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Identify Type</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.identifier_type">
            <option value="" disabled selected>Select ID Type</option> <!-- Placeholder option -->
            <option v-for="type in identifier_types" :value="type.value">
              {{ type.text }}
            </option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">ID Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number"
                 v-model="form.national_id" placeholder="12345678">
        </div>
      </div>


      <!--Organisation Dropdown -->
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Assign Role</label>
        <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.role_id">
          <option value="" disabled selected>Choose one Role</option> <!-- Placeholder option -->
          <option v-for="role in roles" :value="role.value">
            {{ role.text }}
          </option>
        </select>
      </div>

      <!--Buttons-->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'system-users'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20" @click="createUser"
                id="addSystemUser">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Submit
        </button>
      </div>

    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import moment from "moment-timezone";
import country from 'country-list-js';
import VueDatePicker from "@vuepic/vue-datepicker";

export default {
  data() {
    return {
      isLoading: false,
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      loading: false,
      organisations: [],
      roles: [],
      form: {
        dial_code: "254",
        msisdn: '',
        email_address: '',
        first_name: '',
        middle_name: '',
        last_name: '',
        role_id: '',
        national_id: '',
        nationality: 'KENYA',
        identifier_type: 'NATIONAL_ID',
        account_number: ''
      },
      nationalities: [
        {text: 'KENYA', value: 'KENYA'}
      ],
      identifier_types: [
        {text: 'NATIONAL ID', value: 'NATIONAL_ID'},
        {text: 'HUDUMA ID', value: 'HUDUMA_ID'},
        {text: 'PASSPORT', value: 'PASSPORT'},
        {text: 'ALIEN ID', value: 'ALIEN_ID'}
      ],
    }
  },
  components: {
    VueDatePicker,
    Loading
  },
  watch: {
    client_id(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.onOrganisationSelected(newVal)
      }
    },
  },

  async mounted() {
    // Call Organisations
    if(this.$store.state.merchant_account!==null){
      this.form.account_number=this.$store.state.merchant_account.client_account
    }
    console.log("RUTO ENDA:", JSON.stringify( this.form.account_number))

    await this.setOrganisations()
    await this.setRoles()
  },
  methods: {
    ...mapActions(["getMerchants", "getSystemRoles", "addUser"]),

    setClientId(item) {
      this.clientName = item.text
      this.form.account_number = item.value

      this.searchDropdown = false
      this.searchClient = ""
      this.searchDropdownPlaceholder = this.clientName
      console.log("filterOrganizations id", this.form.account_number)
    },

    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },

    // Fetch and set Organisations to UI
    async setOrganisations() {
      let app = this
      app.isLoading = true;
      let response = await this.getMerchants({limit: 100})

      if (response.status === 200) {
        response.message.data.forEach(function (item) {
          let _organisations = {text: item.client_name, value: item.client_account}
          app.organisations.push(_organisations)

        })
      } else {
        app.organisations = []
      }
      app.isLoading = false
    },

    // Fetch System roles
    async setRoles() {
      let app = this
      let response = await this.getSystemRoles({limit: 100})
      if (response.status === 200) {
        response.message.forEach(function (item) {
          let role = {text: item.role_name, value: item.role_id}
          app.roles.push(role)
        })
      }
    },

    async createUser() {
      let app = this

      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.addUser(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              await this.$router.push({name: 'user'})
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

  }
}
</script>
