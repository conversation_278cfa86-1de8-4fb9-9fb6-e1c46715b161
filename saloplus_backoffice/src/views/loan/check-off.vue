<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">
      Loan Check-Off
    </div>

    <div class="grid grid-cols-3 gap-x-6 mb-4 px-6">
      <div class="block">
        <label class="text-xs font-medium mb-1 block">Filter By Organisation</label>
        <div class="relative mb-6">
          <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                 @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
          <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
              v-if="searchDropdown">
            <li v-for="item in organisations"
                class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                @click="setClientId(item)">{{ item.text }}
            </li>
          </ul>
        </div>
      </div>

      <!--      <div class="block">
              <label class="text-xs font-medium mb-1 block">Filter By {{ selectedFilter.text }}</label>
              <div v-if="selectedFilter.value===1" class="relative mb-6">
                <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                       @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
                <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
                    v-if="searchDropdown">
                  <li v-for="item in organisations"
                      class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                      @click="setClientId2(item)">{{ item.text }}
                  </li>
                </ul>
              </div>
              <input v-else-if="selectedFilter.value===2"
                     class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                     v-model="moreParams.loan_number"
                     placeholder="Enter Loan Number" @keyup.enter="applyFilters">
              <input v-else-if="selectedFilter.value===3"
                     class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                     v-model="moreParams.client_email"
                     placeholder="Enter Client Email" @keyup.enter="applyFilters">
              <input v-else-if="selectedFilter.value===4"
                     class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                     v-model="moreParams.client_phone"
                     placeholder="Enter Client Phone Number" @keyup.enter="applyFilters">
              <input v-else
                     class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                     placeholder="Enter Number">
            </div>

            <div class="block">
              <label class="text-xs mb-1 block font-medium">Filter by Date Range</label>
              <VueDatePicker v-model="date" range :preset-ranges="presetRanges" position="center" :clearable="true"
                             :enable-time-picker="false" @closed="selectDate">
                <template #yearly="{ label, range, presetDateRange }">
                  <span @click="presetDateRange(range)">{{ label }}</span>
                </template>
              </VueDatePicker>
            </div>-->
    </div>


    <div class="rounded-lg bg-white shadow-lg mx-3 px-6 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <div v-if="checkOffReports.length>=1">
        <table class="w-full mb-12 table">
          <thead class="border-b-2 text-xs text-left">
          <tr class="table-row">
            <th class="py-2">#</th>
            <th class="py-2">Name</th>
            <th class="py-2">Loan Acc No</th>
            <th class="py-2">Phone</th>
            <th class="py-2">Document Type</th>
            <th class="py-2">Max Limit</th>
            <th class="py-2">Wallet</th>
            <th class="py-2">
              <select v-model="selectedStatus" @change="filterByStatus">
                <option value="All">All</option>
                <option value="1000">Active</option>
                <option value="1002">New</option>
                <option value="1003">Unverified</option>
                <option value="1004">Dormant</option>
                <option value="1005">Suspended</option>
                <option value="1006">Deactivated</option>

                <!-- Add more options for other status values -->
              </select>
            </th>
            <th class="text-center">Action</th>
            <th></th>
          </tr>
          </thead>
          <tbody class="text-xs text-grey-600 divide-y">
          <tr v-for="(loanAccount,index) in filteredLoanAccounts" :key="loanAccount.user_id">
            <td class="py-2 w-1 pr-8">{{ index + 1 }}</td>
            <td class="py-2">
              <span class="font-medium">{{ (loanAccount.first_name) }} {{ (loanAccount.last_name) }}</span>
              <br><span style="font-size: 11px; color: grey">{{ loanAccount.email_address }}</span>
            </td>

            <td class="py-2"><strong>{{ loanAccount.loan_number }}</strong></td>
            <td class="py-2">
              <strong>+{{ loanAccount.msisdn }}</strong>
              <br><span style="font-size: 11px; color: grey">{{ (loanAccount.network) }}</span>
            </td>

            <td class="py-2">
              <strong>{{ loanAccount.nationality }} | </strong>
              {{ loanAccount.national_id }}
              <br>
              <strong>{{ loanAccount.gender }} | </strong> {{ moment(loanAccount.dob).format('ll') }}
            </td>

            <td class="py-2">
              <strong>KES. {{ parseFloat(loanAccount.max_approved_loan_amount).toFixed(2) }}</strong>
            </td>

            <td class="py-2">
              <span>Actual: {{ parseFloat(loanAccount.actual_balance).toFixed(2) }}</span>
              <br>
              <span>Loan: {{ parseFloat(loanAccount.loan_balance).toFixed(2) }}</span>
            </td>

            <td class="text-center py-3">
              <button v-if="parseInt(loanAccount.status) === 1000"
                      :class="getStatusClass(1000)"
                      :style="getStatusStyle(1000)">
                {{ getStatusText(1000) }}
              </button>
              <button v-else-if="parseInt(loanAccount.status) === 1002"
                      :class="getStatusClass(1002)"
                      :style="getStatusStyle(1002)">
                {{ getStatusText(1002) }}
              </button>
              <button v-else-if="parseInt(loanAccount.status) === 1003"
                      :class="getStatusClass(1003)"
                      :style="getStatusStyle(1003)">
                {{ getStatusText(1003) }}
              </button>
              <button v-else-if="parseInt(loanAccount.status) === 1004"
                      :class="getStatusClass(1004)"
                      :style="getStatusStyle(1004)">
                {{ getStatusText(1004) }}
              </button>
              <button v-else-if="parseInt(loanAccount.status) === 1005"
                      :class="getStatusClass(1005)"
                      :style="getStatusStyle(1005)">
                {{ getStatusText(1005) }}
              </button>
              <button v-else-if="parseInt(loanAccount.status) === 1006"
                      :class="getStatusClass(1006)"
                      :style="getStatusStyle(1006)">
                {{ getStatusText(1006) }}
              </button>
            </td>

            <td class="py-2 text-center relative w-24">
              <div class="relative inline-block">
                <button
                    v-if="parseInt(loanAccount.status) === 1000 || parseInt(loanAccount.status) === 1005"
                    class="px-3 py-1 flex items-center space-x-1"
                    @click="toggleDropdown(index)">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                       stroke="#000000" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </button>

                <button v-else class="px-3 py-1 flex items-center space-x-1">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                       stroke="#808080" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </button>

                <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                     style="width: 230px; text-align: left;">
                  <ul class="py-2">
                    <li v-if="parseInt(loanAccount.status) === 1000"
                        @click="activateSuspend(loanAccount.loan_number, 1005)">
                      <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Deactivate
                        <strong>{{ loanAccount.first_name }}</strong></a>
                    </li>
                    <li v-else-if="parseInt(loanAccount.status) === 1005 || parseInt(loanAccount.status) === 1002"
                        @click="activateSuspend(loanAccount.loan_number, 1000)">
                      <a class="block px-3 py-2 cursor-pointer hover:bg-red-200">Activate
                        <strong>{{ loanAccount.first_name }}</strong></a>
                    </li>
                    <li v-if="parseInt(loanAccount.black_list_state) === 0"
                        @click="toggleBlacklist(loanAccount.loan_number, loanAccount.status)">
                      <a class="block px-3 py-2 cursor-pointer hover:bg-gray-200">Blacklist</a>
                    </li>
                    <li v-else @click="toggleBlockUnblock(0,loanAccount.loan_number, loanAccount.status)">
                      <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Unblock</a>
                    </li>
                    <li @click="toggleCRBIPRS(loanAccount.loan_number)">
                      <a class="block px-3 py-2 cursor-pointer hover:bg-yellow-200">CRB/IPRS Check</a>
                    </li>
                    <li @click="editAccount(loanAccount)">
                      <a class="block px-3 py-2 cursor-pointer hover:bg-purple-200">Edit Account</a>
                    </li>
                    <li @click="viewTrx(loanAccount)">
                      <a class="block px-3 py-2 cursor-pointer hover:bg-indigo-200">View Transactions</a>
                    </li>
                    <li @click="toggleKyc(loanAccount.loan_number)">
                      <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Check KYC</a>
                    </li>
                  </ul>
                </div>

              </div>
            </td>

          </tr>
          </tbody>
        </table>

        <!--Pagination-->
        <div v-show="total>limit" class="flex w-full text-xs items-center pb-2">
          <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
          <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to {{ limit * offset }} of
            {{ total }}
          </div>
          <div class="flex-grow text-right" v-show="total > limit">
            <div class="inline-block bg-white border rounded-md divide-x">
              <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1" @click="gotToPage(offset-1)">&larr;</button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                      @click="gotToPage(2)">2
              </button>
              <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                      v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
              </button>

              <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                      v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
                {{ Math.ceil(total / limit) }}
              </button>
              <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <p class="text-center py-4">No data available.</p>
      </div>
    </div>


    <!--Modals-->

  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
// import numeral from "numeral"
import {mapActions} from "vuex";
import VueDatePicker from "@vuepic/vue-datepicker";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";

export default {
  data() {
    return {
      selectedStatus: 'All', // Initially, no filter (show all)
      filteredLoanAccounts: this.checkOffReports, // Initially same as checkOffReports

      ///
      isLoading: false,
      fullPage: true,
      blacklist_reason_hint: "Reason",
      blacklist_reason: "",
      moment: moment,
      open: false,
      open_kyc: false,
      blackListModelOpen: false,
      loading: true,
      idFrontImage: "",
      idBackImage: "",
      selfieImage: "",
      imagesLoaded: false,
      imagesFound: false,
      loanNumber: null,
      sortOrder: [
        {
          field: "created_at",
          direction: "desc"
        }
      ],
      payload: {},
      moreParams: {
        offset: "",
        limit: "",
        sort: "",
        export: "",
        client_id: "",
        type_id: ""

      },

      organisations: [],
      checkOffReports: [],
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],

      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",

      selectedFilter: {},
      filter_by: [
        {text: 'Organization Name', value: 1},
        {text: 'Loan Number', value: 2},
        {text: 'Email', value: 3},
        {text: 'Phone Number', value: 4},
      ],

      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],

    }
  },
  components: {
    VueDatePicker,
    Loading
  },
  computed: {
    placeholderImage() {
      return "https://via.placeholder.com/600x400?text=Image+Not+Available";
    }
  },
  async mounted() {
    this.moreParams.client_id = this.$route.params.client_id ?? this.$store.state.merchant_account.client_id

    // Call Organisations
    await this.setOrganisations()

  },
  methods: {
    ...mapActions(["getMerchants", "getCheckoffReport", "updateLoanAccount", "fillLoanAcc", "confirmKYC",
      "approveOrRejectKYC", "validateAcc",]),

    //
    filterByStatus() {
      console.log("this.selectedStatus", this.selectedStatus)
      if (this.selectedStatus === "All") {
        this.filteredLoanAccounts = this.checkOffReports; // Show all if no filter
      } else {
        this.filteredLoanAccounts = this.checkOffReports.filter(account => parseInt(account.status) === parseInt(this.selectedStatus));
      }
    },

    //
    gotToPage(page) {
      let vm = this
      vm.moreParams.offset = page
      vm.offset = page
      vm.setCheckoffReport()
    },

    //
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    //
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    //
    async selectDate() {
      this.moreParams.start = this.formatDate(this.date[0])
      this.moreParams.end = this.formatDate(this.date[1])
      await this.setCheckoffReport()
    },

    //
    setClientId(item) {
      this.moreParams.client_id = item.value
      this.searchDropdownPlaceholder = item.text
      this.searchDropdown = false
      this.searchClient = ""
      this.setCheckoffReport()
    },

    //
    toggleSearchDropdown() {
      this.searchDropdown = !this.searchDropdown;
    },

    //
    async setCheckoffReport() {
      this.isLoading = true
      let response = await this.getCheckoffReport(this.moreParams)

      if (response.status === 200) {
        this.checkOffReports = response.message.data
        this.total = parseInt(response.message.total_count)

        this.showDropdown = []
        for (let i = 0; i < this.checkOffReports.length; i++) {
          this.showDropdown.push(false)
        }
      }
      this.filterByStatus()
      this.isLoading = false
    },

    //
    async setOrganisations() {
      let app = this
      app.isLoading = true;
      let response = await this.getMerchants({limit: 100})
      if (response.status === 200) {
        response.message.data.forEach(item => {
          let organisation = {text: item.client_name, value: item.client_id}
          app.organisations.push(organisation)
        })
      }
      this.isLoading = false
    },

    //
    async toggleBlacklist(loan_number = null, pin_status = null) {
      this.closeDropdown()
      if (loan_number) {
        this.loan_number = loan_number
        this.pin_status = pin_status
      }
      this.blackListModelOpen = !this.blackListModelOpen
    },

    //
    async toggleBlockUnblock(status, loan_number, pin_status) {
      let app = this

      app.payload = {
        blacklist: status,
        blacklist_reason: this.blacklist_reason,
        pin_status: pin_status,
        loan_number: loan_number,
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this updates this loan account!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          console.log(res)
          return await this.updateLoanAccount(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              await app.setLoanAccounts()
              app.block = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    async activateSuspend(loan_number, pin_status) {
      this.closeDropdown()
      let app = this

      const payload = {
        pin_status: pin_status,
        loan_number: loan_number,
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this updates this loan account!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          console.log(res)
          return await this.updateLoanAccount(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              await app.setLoanAccounts()
              app.open = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    async toggleCRBIPRS(loan_number = null) {
      this.closeDropdown()
      if (loan_number) {
        this.loan_number = loan_number
      }
      this.open = !this.open
    },

    //
    async editAccount(row) {
      this.closeDropdown()
      await this.fillLoanAcc(row)
      await this.$router.push({name: 'loan-accounts-edit'})
    },

    //
    async toggleKyc(loan_number) {
      this.loading = true
      this.closeDropdown()
      this.loanNumber = loan_number
      await this.setKYC(loan_number)
      this.open_kyc = !this.open_kyc
      this.loading = false

    },

    toggleKyc1() {
      this.open_kyc = !this.open_kyc
    },

    async setKYC(loan_number) {
      this.imagesLoaded = false

      const payload = {
        "offset": "",
        "limit": "",
        "sort": "",
        "export": "",
        "client_id": "",
        "client_name": "",
        "client_email": "",
        "loan_number": loan_number,
        "client_phone": ""
      };

      let response = await this.confirmKYC(payload)

      if (response.status === 200) {
        // let result = response.message

        const bucketUrl = response.message.bucket_url;

        const fileInfo = JSON.parse(response.message.data[0].file_info);
        const idFront = fileInfo[0].file_name;
        const idBack = fileInfo[1].file_name;
        const image1 = fileInfo[2].file_name;

        this.idFrontImage = bucketUrl + idFront
        this.idBackImage = bucketUrl + idBack
        this.selfieImage = bucketUrl + image1

        this.imagesLoaded = true
        this.imagesFound = true

      } else {
        console.log(`confirmKYC:Else`, response)
        // app.$swal.fire('Error!', result, 'error')

        this.idFrontImage = "https://via.placeholder.com/600x400?text=Image+Not+Available"
        this.idBackImage = "https://via.placeholder.com/600x400?text=Image+Not+Available"
        this.selfieImage = "https://via.placeholder.com/600x400?text=Image+Not+Available"

        this.imagesFound = false
      }
    },

    async acceptKYC() {
      let app = this
      console.log("ACCEPT KYC")
      const payload = {
        "status": "1",
        "acc": app.loanNumber
      };
      let response = await this.approveOrRejectKYC(payload)

      if (response.status === 200) {
        let result = response.message
        console.log(`Image fileInfo: `);

        app.$swal.fire('KYC Approved', result, 'success')

      } else {
        let result = response.message
        console.log("Accept KYC Uploads err: " + JSON.stringify(response.data))
        console.log(`Accept KYC Err: ${response}`)
        app.$swal.fire('Error!', result, 'error')

      }

      this.toggleKyc1()

    },

    async rejectKYC() {
      let app = this
      console.log("Reject KYC")
      const payload = {
        "status": "2",
        "acc": app.loanNumber
      };
      let response = await this.approveOrRejectKYC(payload)

      if (response.status === 200) {
        let result = response.message
        console.log(`Image fileInfo: `);

        app.$swal.fire('KYC Rejected', result, 'success')

      } else {
        let result = response.message
        console.log("Accept KYC Uploads err: " + JSON.stringify(response.data))
        console.log(`Accept KYC Err: ${response}`)
        app.$swal.fire('KYC Error!', result, 'error')

      }

    },

    async viewTrx(acc) {
      this.isLoading = true
      await this.$router.push({name: 'transactions', params: {loan_number: acc.loan_number}})
    },

    getRoleBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-amber-400'
        case 4:
          return 'bg-teal-600'
        case 5:
          return 'bg-sky-600'
        default:
          return 'bg-purple-600'
      }
    },
    getStatusText(status) {
      switch (status) {
        case 1000:
          return 'Active';
        case 1002:
          return 'New';
        case 1003:
          return 'Unverified';
        case 1004:
          return 'Dormant';
        case 1005:
          return 'Suspended';
        case 1006:
          return 'Suspended';
        default:
          return '';
      }
    },
    getStatusClass(status) {
      switch (status) {
        case 1000:
          return 'inline-block px-3 py-1 rounded-md text-white'; // Define your CSS classes for different statuses
        case 1002:
          return 'inline-block px-3 py-1 rounded-md text-white';
        case 1003:
          return 'orange-button';
        case 1004:
          return 'purple-button';
        case 1005:
          return 'inline-block px-3 py-1 rounded-md text-white';
        case 1006:
          return 'red-button';
        default:
          return '';
      }
    },
    getStatusStyle(status) {
      switch (status) {
        case 1000:
          return 'background-color: green;'; // Define your inline styles for different statuses
        case 1002:
          return 'background-color: blue;';
        case 1003:
          return 'background-color: orange;';
        case 1004:
          return 'background-color: purple;';
        case 1005:
        case 1006:
          return 'background-color: red;';
        default:
          return '';
      }
    },

  },
}
</script>
