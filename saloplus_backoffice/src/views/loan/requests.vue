<template>

  <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">

    <div class="flex font-medium p-6 pb-1 border-b gap-4 mb-5">
      <div class="flex-shrink font-medium">
        <i v-if="!this.$store.state.isSideMenuOpen" class="fa fa-bars text-black text-lg" @click="toggleSideM"></i>
        <i v-else class="fa fa-close text-black text-lg" @click="toggleSideM"></i>
      </div>

      <div class="flex-grow font-medium pb-2">
        Loan Requests - ({{ searchDropdownPlaceholder }})
      </div>
    </div>

    <div class="rounded-lg bg-white shadow-lg mx-5 my-4 py-3 border">

      <!-- Filters -->
      <div class="grid grid-cols-3 gap-x-6 mb-4 mx-4  ">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Select Filter Type</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selectedFilter">
            <option v-for="item in filter_by" :value="item">
              {{ item.text }}
            </option>
          </select>
        </div>

        <div class="block">
          <label class="text-xs font-medium mb-1 block">Filter By {{ selectedFilter.text }}</label>
          <div v-if="selectedFilter.value===1" class="relative mb-6">
            <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                   @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
            <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
                v-if="searchDropdown">
              <li v-for="item in filteredOrganizations"
                  class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                  @click="setClientId2(item)">{{ item.text }}
              </li>
            </ul>
          </div>
          <input v-else-if="selectedFilter.value===2"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="moreParams.loan_number"
                 placeholder="Enter Loan Number" @keyup.enter="applyFilters">
          <input v-else-if="selectedFilter.value===3"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="moreParams.loan_request_number"
                 placeholder="Enter Loan Request Number" @keyup.enter="applyFilters">
          <input v-else-if="selectedFilter.value===4"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="moreParams.loan_request_number"
                 placeholder="Enter Reference Number" @keyup.enter="applyFilters">
          <input v-else-if="selectedFilter.value===5"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="moreParams.client_phone"
                 placeholder="Enter Phone Number" @keyup.enter="applyFilters">
          <input v-else
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 placeholder="Enter Number">
        </div>

        <div class="block">
          <label class="text-xs mb-1 block font-medium">Filter by Date Range</label>
          <VueDatePicker
              v-model="date"
              range
              :preset-ranges="presetRanges"
              position="center"
              :clearable="true"
              :enable-time-picker="false"
              @closed="selectDate">
            <template #yearly="{ label, range, presetDateRange }">
              <span @click="presetDateRange(range)">{{ label }}</span>
            </template>
          </VueDatePicker>
        </div>
      </div>

      <!-- Filter by status -->
      <div class="grid grid-cols-3 mx-4 ">
        <div class="block mb-4">
          <label for="statusFilter" class="text-sm font-medium">Filter by Status:</label>
          <select
              id="statusFilter"
              v-model="moreParams.status"
              @change="setLoanRequests"
              class="border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-green-500 focus:outline-none ml-2 py-1 px-2 text-sm text-gray-800 transition duration-150 ease-in-out hover:border-green-500"
          >
            <option value="">All</option>
            <option value="8">Approved</option>
            <option value="5">Submitted (Pending approval)</option>
            <option value="2">Partially Paid</option>
            <option value="3">Rejected</option>
            <option value="4">Unverified</option>
          </select>
        </div>

        <div class="block"></div>

        <div class="block">
          <!--          <label class="text-xs mt-4 block font-medium">Export Data</label>-->
          <div class="text-sm text-right">
            <button v-if="!download.request && !download.loading"
                    class="inline-block px-4 py-2 rounded-md bg-blue-500 text-white"
                    @click.prevent="downLoadLoanRequests()">
              Request Download
            </button>
            <button v-else-if="!download.request && download.loading"
                    class="inline-block px-4 py-2 rounded-md bg-blue-500 text-white">Requesting...
            </button>
            <download v-else :file-title="download.fileTitle" :headers="download.headers" :items="download.items"/>
          </div>

        </div>
      </div>

      <div class="border-b my-2"/>

      <div class="mx-2 p-2 rounded-lg border">
        <table class="w-full mb-4 table">
          <thead class="border-b-2 text-xs text-left">
          <tr class="table-row">
            <th class="py-2">Request No</th>
            <th class="py-2">Profile acc</th>
            <th class="py-2">Product Name</th>
            <th class="text-center py-2">Req'd & Approved Amounts</th>
            <th class="text-center py-2">Repayment Amt</th>
            <th class="text-center py-2">Date</th>
            <th class="text-center py-2">Status</th>
            <th class="text-center">Actions</th>
          </tr>
          </thead>

          <tbody v-if="loan_requests.length>0" class="text-xs  divide-y divide-black-200">
          <tr v-for="(loan_request, index) in loan_requests" :key="loan_request.client_ac">

            <td class="py-2 font-medium"><strong>{{ loan_request.req_number }}</strong></td>

            <td class="py-2">
              <span>+{{ loan_request.mobile }} - {{ loan_request.name }}</span>
              <br>
              <span> {{ loan_request.merchant_name }}</span>
            </td>

            <td class="py-2">
              {{ loan_request.product_name }} ({{ parseFloat(loan_request.interest_charged) * 100 }}%)
            </td>

            <td class="text-center py-2">
              Req: {{ formatCurrency(parseFloat(loan_request.requested_amount).toFixed(2)) }}
              <br>
              App: {{ formatCurrency(parseFloat(loan_request.approved_amount).toFixed(2)) }}
            </td>

            <!--            <td class="text-center py-2">{{ formatCurrency(parseFloat(loan_request.approved_amount).toFixed(2)) }}</td>-->

            <td class="text-center py-2">{{ formatCurrency(getRepaymentAmount(loan_request).toFixed(2)) }}</td>

            <td class="text-center py-2"><span>{{ moment(loan_request.created).format('lll') }}</span></td>

            <td class="text-center py-2">
              <button v-if="parseInt(loan_request.approval_status) === 1"
                      class="inline-block px-3 py-1 rounded-md text-white bg-green-500">
                Fully Paid
              </button>
              <button v-else-if="parseInt(loan_request.approval_status) === 2"
                      class="inline-block px-3 py-1 rounded-md text-white bg-orange-500">
                Partially Paid
              </button>
              <button v-else-if="parseInt(loan_request.approval_status) === 3"
                      class="inline-block px-3 py-1 rounded-md text-white bg-red-500">
                Rejected
              </button>
              <button v-else-if="parseInt(loan_request.approval_status) === 4"
                      class="inline-block px-3 py-1 rounded-md text-white bg-purple-500">
                Unverified
              </button>
              <button v-else-if="parseInt(loan_request.approval_status) === 5"
                      class="inline-block px-3 py-1 rounded-md text-white bg-teal-500">
                Pending
              </button>
              <button v-else-if="parseInt(loan_request.approval_status) === 6"
                      class="inline-block px-3 py-1 rounded-md text-white bg-brown-500">
                Unpaid
              </button>
              <button v-else-if="parseInt(loan_request.approval_status) === 7"
                      class="inline-block px-3 py-1 rounded-md text-white bg-yellow-500">
                Failure
              </button>
              <button v-else-if="parseInt(loan_request.approval_status) === 8"
                      class="inline-block px-3 py-1 rounded-md text-white bg-green-500">
                Approved
              </button>
            </td>

            <td class="text-center py-2 relative w-24">
              <div class="relative inline-block">
                <button
                    v-if="parseInt(loan_request.approval_status) === 5 || parseInt(loan_request.approval_status) === 5
                    || parseInt(loan_request.approval_status) === 4 || parseInt(loan_request.approval_status) === 2"
                    class="px-3 py-1 flex items-center space-x-1"
                    @click="toggleDropdown(index)">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                       stroke="#000000" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </button>
                <button v-else class="px-3 py-1 flex items-center space-x-1">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                       stroke="#808080" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </button>
                <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10">
                  <ul class="py-2">
                    <li v-if="parseInt(loan_request.approval_status) === 5"
                        @click="toggleApprove(loan_request.req_number, loan_request.requested_amount, 1)">
                      <a class="block px-16 py-2 cursor-pointer hover:bg-green-200">Approve</a>
                    </li>
                    <li v-if="parseInt(loan_request.approval_status) === 5 ||parseInt(loan_request.approval_status) === 4"
                        @click="toggleApprove(loan_request.req_number, loan_request.requested_amount, 0)">
                      <a class="block px-16 py-2 cursor-pointer hover:bg-red-200">Reject</a>
                    </li>
                    <li v-if="parseInt(loan_request.approval_status) === 4"
                        @click="requestTAN(loan_request.req_number)">
                      <a class="block px-16 py-2 cursor-pointer hover:bg-purple-200">Resend Code</a>
                    </li>
                    <li v-if="parseInt(loan_request.approval_status) === 2"
                        @click="viewRepayments(loan_request)">
                      <a class="block px-16 py-2 cursor-pointer hover:bg-purple-200">View Repayments</a>
                    </li>
                  </ul>
                </div>
              </div>
            </td>
          </tr>

          </tbody>

          <div v-else class="my-8 flex justify-center">
            No Results
          </div>

        </table>

        <div class="flex w-full text-xs items-center" v-show="total>limit">
          <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
          <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to
            {{ limit * offset < total ? limit * offset : total }} of {{ total }}
          </div>
          <div class="flex-grow text-right" v-show="total > limit">
            <div class="inline-block bg-white border rounded-md divide-x">
              <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1 && offset > 1" @click="gotToPage(offset-1)">
                &larr;
              </button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                      @click="gotToPage(2)">2
              </button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2"
                      @click="gotToPage(3)">3
              </button>
              <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                      v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
              </button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                      v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
                {{ Math.ceil(total / limit) }}
              </button>
              <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Modal -->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': isAcceptRejectModelOpen, 'opacity-0 pointer-events-none': !isAcceptRejectModelOpen }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Loan Review</p>
            <div class="modal-close cursor-pointer z-50" @click="isAcceptRejectModelOpen = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                   viewBox="0 0 18 18">
                <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!-- Body -->
          <div class="mb-4">
            <label class="block text-sm font-medium">Please type
              <strong class="text-blue-500">{{ label }}</strong> to {{ label }} this loan.</label>
            <input
                :placeholder="label"
                type="text"
                v-model="status_description"
                class="mt-1 p-2 border rounded-md w-full"
            />
          </div>
          <!-- Footer -->
          <div class="flex justify-end pt-2">
            <button @click="toggleApprove(1, 1, 1)"
                    class="px-4 py-2 bg-transparent rounded-lg text-blue-500 hover:bg-gray-100 hover:text-blue-400">
              Cancel
            </button>
            <button @click="approve"
                    class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400">
              {{ capitalizeFirstLetter(label) }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import Download from "@/components/downloadCSV.vue";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";

export default {
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      isLoading: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      isAcceptRejectModelOpen: false,
      label: "approve",
      showDropdown: [],
      date: null,
      presetRanges: [
        {label: "All", range: ["", ""]},
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
      loan_requests: [],
      organisations: [],
      client_id: "",
      status: "",
      approved_amount: "",
      status_description: "",
      form: {
        dial_code: "254",
        msisdn: null,
        email_address: null,
        full_name: null,
        employee_number: null,
        employment_date: null,
        gender: null,
        dob: null,
        netable_salary: 1000,
        validate_check: 0,
        national_id: null,
        nationality: 'KENYA',
        identifier_type: 'NATIONAL_ID',
        id: null
      },
      moreParams: {
        status: "",
        start: "",
        end: "",
        limit: 10,
        offset: "",
        sort: "",
        export: "",
        amount: "",
        client_id: "",
        loan_number: "",
        reference_id: "",
        loan_request_number: "",
        client_phone: "",
      },

      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      moment: moment,

      selectedFilter: {text: 'Organization Name', value: 1},
      filter_by: [
        {text: 'Organization Name', value: 1},
        {text: 'Loan Number', value: 2},
        {text: 'Request Number', value: 3},
        {text: 'Reference Number', value: 4},
        {text: 'Phone Number', value: 5},
      ],


      download: {request: false, loading: false, headers: {}, items: [], fileTitle: ''},
    }
  },
  components: {
    Download,
    Loading,
    VueDatePicker
  },
  computed: {
    filteredOrganizations() {
      return this.organisations.filter(org => org.text.toLowerCase().includes(this.searchClient.toLowerCase()));
    }
  },
  watch: {
    selectedFilter(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.client_id = ""
        this.moreParams.loan_number = ""
        this.moreParams.loan_request_number = ""
      }
    },
    // watch date
    date(newValue) {
      if (!newValue) {
        this.selectDate(); // Call method when date is cleared
      }
    },
  },
  async mounted() {
    this.moreParams.client_id = this.$route.params.client_id //?? this.$store.state.merchant_account.client_id

    // Call Organisations
    await this.setOrganisations()

    // set date to current month
    this.date = [startOfMonth(new Date()), endOfMonth(new Date())]
    this.moreParams.start = this.formatDate(this.date[0])
    this.moreParams.end = this.formatDate(this.date[1])

    await this.selectDate()
    // await this.setLoanRequests()


  },
  methods: {
    ...mapActions(["getMerchants", "getLoanRequests", "resendTAN", "approveLoan", "toggleSideMenu",]),
    //
    toggleSideM() {
      this.toggleSideMenu()
    },
    //
    formatCurrency(number) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    //
    capitalizeFirstLetter(value) {
      if (!value) return '';
      return value.charAt(0).toUpperCase() + value.slice(1);
    },
    //
    setClientId2(item) {
      this.moreParams.client_id = item.value
      this.searchDropdownPlaceholder = item.text
      this.searchDropdown = false
      this.searchClient = ""
      this.setLoanRequests()
    },
    //
    toggleSearchDropdown() {
      this.searchDropdown = !this.searchDropdown;
    },
    //
    gotToPage(page) {
      this.offset = page
      this.moreParams.offset = page
      this.setLoanRequests()
    },
    //
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    //
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    //
    async selectDate() {
      if (this.date !== null) {
        this.moreParams.start = this.formatDate(this.date[0])
        this.moreParams.end = this.formatDate(this.date[1])
      } else {
        this.moreParams.start = ""
        this.moreParams.end = ""
      }
      await this.setLoanRequests()
    },

    //
    async applyFilters() {
      await this.setLoanRequests()
    },
    //
    formatDate(date) {
      let d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();
      if (month.length < 2) month = '0' + month;
      if (day.length < 2) day = '0' + day;
      return [year, month, day].join('-');
    },
    //
    async setOrganisations() {
      let app = this
      this.isLoading = true;
      let response = await this.getMerchants({limit: 100})
      if (response.status === 200) {
        response.message.data.forEach(item => {
          let organisation = {text: item.client_name, value: item.client_id}
          if (item.client_id === app.$route.params.client_id) {
            app.searchDropdownPlaceholder = item.client_name
          }
          this.organisations.push(organisation)
        })
        // this.setClientId(this.organisations[0])
      }
      this.isLoading = false
    },

    //
    async setLoanRequests() {
      this.isLoading = true;
      let response = await this.getLoanRequests(this.moreParams)

      if (response.status === 200) {
        this.loan_requests = response.message.data
        this.total = response.message.total_count

        this.showDropdown = []
        for (let i = 0; i < this.loan_requests.length; i++) {
          this.showDropdown.push(false)
        }
      } else {
        this.loan_requests = []
      }
      this.isLoading = false
    },

    //
    getRepaymentAmount(loan_request) {
      let amount = 0
      let principal = parseFloat(loan_request.approved_amount)
      let interest = parseFloat(loan_request.interest_charged) * 100
      let loan_term = parseFloat(loan_request.repayment_period_in_months)
      let processing_fee = parseFloat(loan_request.procesing_fee)
      let excise_duty = parseFloat(loan_request.excise_duty)

      // calculate compound interest
      amount = principal + (principal * (interest / 100) * loan_term) + processing_fee + excise_duty

      return amount
    },

    //
    async toggleApprove(id, amount, status) {
      this.closeDropdown()
      this.id = id
      this.status = status
      this.approved_amount = amount
      this.status_description = ''
      this.label = status === 0 ? 'reject' : 'approve'
      this.isAcceptRejectModelOpen = !this.isAcceptRejectModelOpen
    },

    async approve() {
      if (this.label !== this.status_description) {
        return this.handleAlert('Please type ' + this.label + " to " + this.label + " this loan.")
      }
      const payload = {
        id: this.id,
        status: this.status,
        approved_amount: this.approved_amount,
        status_description: 'Loan of ' + this.approved_amount + ' ' + this.label,
      }
      this.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this approve/reject this loan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, confirm!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          this.isAcceptRejectModelOpen = false
          return await this.approveLoan(payload)
        },
      }).then(async result => {
        if (result.value.status === 200) {
          this.$swal.fire('Submitted!', result.value.message, 'success')
        } else {
          this.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },

    async requestTAN(request_number) {
      this.closeDropdown()
      const payload = {
        request_number: request_number,
      }
      this.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this resends the code!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, send!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          return await this.resendTAN(payload)
        },
      }).then(async result => {
        if (result.value.status === 200) {
          this.$swal.fire('Sent!', result.value.message, 'success')
        } else {
          this.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },

    // view repayments, fill in the loan number and navigate to the repayments page
    async viewRepayments(loan_request) {
      this.$store.commit('setLoanRequestNumber', loan_request.req_number)
      this.$store.commit('setLoanNumber', loan_request.req_number)
      await this.$router.push({name: 'loan-repayments'})
    },

    //
    async downLoadLoanRequests() {
      this.isLoading = true

      this.moreParams.export = "1"
      // const params = new URLSearchParams();
      //
      // for (const key in this.moreParams) {
      //   if (this.moreParams.hasOwnProperty(key)) {
      //     params.append(key, this.moreParams[key]);
      //   }
      // }
      //
      // const queryString = params.toString();

      // console.log("Params: " + JSON.stringify(this.moreParams));

      let response = await this.getLoanRequests(this.moreParams)

      // this.download.items = response.message.data
      // loop through the items and calculate the repayment amount and create a new array
      response.message.data.forEach(item => {
        // console.log("JIAJ: ",JSON.stringify(item))
        let repayment_amount = parseFloat(item.approved_amount) + ((parseFloat(item.approved_amount) * parseFloat(item.interest_charged)) * parseFloat(item.repayment_period_in_months)) + parseFloat(item.procesing_fee) + parseFloat(item.excise_duty)

        let request = {
          //if name is not available, use the mobile number, else use the both
          // name:item.name ? item.mobile + '\n' + item.name : item.mobile,
          req_number: item.req_number,
          name: item.name ?? "N/A",
          mobile: item.mobile,
          account_number: item.account_number,
          approved_amount: 'KES. ' + item.approved_amount,
          interest_charged: 'KES. ' + item.interest_charged * 100,
          procesing_fee: 'KES. ' + item.procesing_fee,
          excise_duty: 'KES. ' + item.excise_duty,
          repayment_amount: 'KES. ' + repayment_amount.toFixed(2),
        }

        this.download.items.push(request)
      })


      this.download.headers = {
        req_number: 'LOAN REQUEST NO',
        name: 'CUSTOMER',
        mobile: 'MOBILE',
        approved_amount: 'PRINCIPAL',
        interest_charged: 'INTEREST',
        procesing_fee: 'PROCESSING FEE',
        excise_duty: 'EXCISE DUTY',
        repayment_amount: 'TOTAL REPAYMENT AMOUNT',
      }
      this.download.fileTitle = 'Loan Requests as at - ' + moment(new Date()).format('lll')
      this.download.loading = false
      this.download.request = true

      this.isLoading = false
    },


    //
  }
}
</script>
