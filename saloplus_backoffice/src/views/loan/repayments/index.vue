<template>

  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">

    <div class="flex font-medium p-6 pb-1 border-b gap-4 mb-5">
      <div class="flex-shrink font-medium">
        <i v-if="!this.$store.state.isSideMenuOpen" class="fa fa-bars text-black text-lg" @click="toggleSideM"></i>
        <i v-else class="fa fa-close text-black text-lg" @click="toggleSideM"></i>
      </div>

      <div class="flex-grow font-medium pb-2"> Loan Repayments</div>
    </div>

    <!-- Table -->
    <GenericTable :loan_number="loan_number" :request_number="request_number"/>

  </div>
</template>

<script>
import GenericTable from '@/views/loan/repayments/table_repayments.vue';
import {mapActions} from "vuex";
import CustomButton from "@/components/custom_button.vue";
import Loading from "vue-loading-overlay";

export default {
  components: {
    Loading,
    CustomButton,
    GenericTable,
  },
  data() {
    return {
      loan_number: '',
      request_number: '',
    };
  },
  methods: {
    ...mapActions(['toggleSideMenu',]),
    //
    toggleSideM() {
      this.toggleSideMenu()
    },
  },

};
</script>


