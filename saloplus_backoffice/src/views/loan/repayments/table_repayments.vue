<template>
  <div>
    <!-- Loading Spinner -->
    <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

    <!-- Filter and Table -->
    <div class="block py-4 rounded-lg bg-red-200 shadow-lg mx-3 border">
      <div class="grid grid-cols-3">
        <!-- Input to filter by request number -->
        <div class="px-6 block">
          <label class="text-xs font-medium mb-1 block">Filter Repayment By Request No.</label>
          <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="loan_repayments_params.request_number"
                 placeholder="Enter Reference Number" @keyup.enter="setLoanRepayments">
        </div>
      </div>

      <div class="border-b my-2"/>

      <div class="px-6">
        <!-- Table -->
        <table class="w-full mb-4 table">
          <thead class="border-b-2 text-xs text-left">
          <tr class="table-row">
            <th class="py-2">Transaction Code</th>
            <th class="text-center py-2">Customer</th>
            <th class="text-center py-2">Amount</th>
            <th class="text-center py-2">Reducing Balance</th>
            <th class="text-center py-2">Repayment Source</th>
            <th class="py-2">Date</th>
          </tr>
          </thead>
          <tbody class="text-xs text-gray-600 divide-y">
          <tr v-for="(item, index) in loan_repayments" :key="item.id">
            <td class="py-2 font-medium">{{ index + 1 }}</td>
            <td class="py-2 font-medium">{{ item.trxn_code }}</td>
            <td class="py-2">
              <span>{{ item.payer ? item.payer.split('-')[1] : '' }}</span>
              <br/>
              <span>+{{ item.payer ? item.payer.split('-')[0] : '' }}</span>
            </td>
            <td class="text-center py-2">{{ item.currency_code ?? "KES" }}.{{
                formatCurrency(item.repayment_amount)
              }}
            </td>
            <td class="text-center py-2">{{ item.currency_code ?? "KES" }}.{{
                formatCurrency(item.reducing_balance)
              }}
            </td>
            <td class="text-center py-2"><strong>{{ item.repayment_source }}</strong></td>
            <td class="py-2">{{ moment(item.created).format('lll') }}</td>
          </tr>
          </tbody>
        </table>

        <!-- Pagination -->
        <div class="flex w-full text-xs items-center" v-show="total > limit">
          <div v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
          <div v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to {{ limit * offset }} of {{ total }}</div>
          <div class="flex-grow text-right" v-show="total > limit">
            <div class="inline-block bg-white border rounded-md divide-x">
              <button class="p-2 px-3" v-show="Math.ceil(total / limit) > 1" @click="gotToPage(offset - 1)">&larr;
              </button>
              <button class="p-2 px-4" :class="{'bg-gray-100': offset === 1}" @click="gotToPage(1)">1</button>
              <button class="p-2 px-4" v-show="Math.ceil(total / limit) > 2" @click="gotToPage(2)">2</button>
              <button class="p-2 px-4" v-show="Math.ceil(total / limit) > 3">...</button>
              <button class="p-2 px-4" v-show="offset >= 3" @click="gotToPage(offset)">{{ offset }}</button>
              <button class="p-2 px-4" v-show="Math.ceil(total / limit) > 4"
                      @click="gotToPage(Math.ceil(total / limit))">{{ Math.ceil(total / limit) }}
              </button>
              <button class="p-2 px-3" v-show="(offset * limit) < total" @click="gotToPage(offset + 1)">&rarr;</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import Loading from 'vue-loading-overlay';

export default {
  props: {
    loan_number: {
      type: String,
      required: true
    },
    request_number: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      loan_repayments: [],
      total: 0,
      limit: 10,
      offset: 1,
      loan_repayments_params: {
        offset: 0,
        limit: 10,
        loan_number: this.loan_number,
        request_number: this.request_number,
      },
      moment: moment,
    };
  },
  components: {
    Loading,
  },
  methods: {
    ...mapActions(["getLoanRepayments"]),

    async setLoanRepayments() {
      this.isLoading = true;
      let response = await this.getLoanRepayments(this.loan_repayments_params);

      if (response.status === 200) {
        this.loan_repayments = response.message.data;
        this.total = response.message.total_count;
      } else {
        this.loan_repayments = [];
      }
      this.isLoading = false;
    },

    formatCurrency(value) {
      return parseFloat(value).toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    },

    gotToPage(page) {
      this.offset = page;
      this.loan_repayments_params.offset = page;
      this.setLoanRepayments();
    }
  },

  async mounted() {
    await this.setLoanRepayments();
  }
};
</script>


<!--
<template>
  &lt;!&ndash; Loading &ndash;&gt;
  <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

  &lt;!&ndash; Table &ndash;&gt;
  <div class="block  py-4 rounded-lg bg-red-200 shadow-lg mx-3 border ">
    <div class="grid grid-cols-3">
      <div class="px-6 block">
        <label class="text-xs font-medium mb-1 block">Filter Repayment By Request No.</label>
        <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
               v-model="loan_repayments_params.request_number"
               placeholder="Enter Reference Number" @keyup.enter="setLoanRepayments">
      </div>
    </div>

    <div class="border-b my-2"/>

    <div class="px-6">
      <table class="w-full mb-4 table">
        &lt;!&ndash; Headers &ndash;&gt;
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">Transaction Code</th>
          <th class="text-center py-2">Customer</th>
          <th class="text-center py-2">Amount</th>
          <th class="text-center py-2">Reducing Balance</th>
          <th class="text-center py-2">Repayment Source</th>
          <th class="py-2">Date</th>
        </tr>
        </thead>
        &lt;!&ndash; Body &ndash;&gt;
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in loan_repayments" :key="item.id">
          <td class="py-2 font-medium">
            <strong>{{ index + 1 }}</strong>
          </td>
          <td class="py-2 font-medium">
            <strong>{{ item.trxn_code }}</strong>
          </td>

          <td class="py-2">
            <span>{{ item.payer ? item.payer.split('-')[1] : '' }}</span>
            <br>
            <span>+{{ item.payer ? item.payer.split('-')[0] : '' }}</span>
          </td>

          <td class="text-center py-2">{{ item.currency_code ?? "KES" }}.
            {{ formatCurrency(parseFloat(item.repayment_amount).toFixed(2)) }}
          </td>

          <td class="text-center py-2">{{ item.currency_code ?? "KES" }}.
            {{ formatCurrency(parseFloat(item.reducing_balance).toFixed(2)) }}
          </td>

          <td class="text-center py-2">
            <strong>{{ item.repayment_source }}</strong>
          </td>

          <td class="py-2">
            <span> {{ moment(item.created).format('lll') }}</span>
          </td>

        </tr>
        </tbody>
      </table>

      &lt;!&ndash;Pagination&ndash;&gt;
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{
            total
          }}
        </div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to
          {{ limit * offset }} of
          {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(offset-1)">
              &larr;
            </button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}"
                    v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3"
                    @click="gotToPage(offset)">
              {{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4"
                    @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total"
                    @click="gotToPage(offset+1)">
              &rarr;
            </button>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import {mapActions} from "vuex";
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";

export default {
  data() {
    return {
      // Default variables
      isLoading: false,
      moment: moment,

      // Data variables
      loan_repayments: [],
      total: 0,
      limit: 10,
      offset: 1,
      loan_repayments_params: {
        offset: 0,
        limit: 10,
        sort: "",
        export: "",
        start: "",
        end: "",
        client_id: "",
        amount: "",
        loan_number: this.$store.state.loan_number ?? "",
        client_phone: "",
        client_email: "",
        request_number: this.$store.state.request_number ?? "",
        payment_transaction_number: "",
      },

    };

  },
  components: {
    Loading,
  },
  async mounted() {
    await this.setLoanRepayments();
  },
  methods: {
    ...mapActions(["getLoanRepayments",]),

    // Fetch and set Loan Repayments
    async setLoanRepayments() {
      let app = this
      app.isLoading = true
      app.loan_repayments_params.loan_number = this.$store.state.loan_number ?? ""

      let response = await this.getLoanRepayments(app.loan_repayments_params)

      if (response.status === 200) {
        app.loan_repayments = response.message.data
        app.total = response.message.total_count
        console.log("Total: " + app.total)

        app.showTables = true

        app.showLRTDropdown = []
        for (let i = 0; i < app.loan_repayments.length; i++) {
          app.showLRTDropdown.push(false)
        }
      } else {
        app.loan_repayments = []
      }
      app.isLoading = false
    },

    //
    gotToPage(page) {
      let app = this
      app.offset = page
      app.loan_repayments_params.offset = page
      app.loan_repayments_params.limit = app.limit
      // log
      console.log("Offset: " + app.offset + "\nLimit: " + app.limit, "\nTotal: " + app.total)

      app.setLoanRepayments()
    },

    // Format currency
    formatCurrency(value) {
      return parseFloat(value).toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    },

  },
};
</script>

-->
