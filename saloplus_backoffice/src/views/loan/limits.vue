<template>

  <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">
      Loan Limit Requests & Increase - ({{ searchDropdownPlaceholder }})
    </div>


    <div class="rounded-lg bg-white shadow-lg mx-3 py-3 border ">
      <div class="px-6">
        <div class="grid grid-cols-3 gap-x-6 mb-4">
          <div class="block">
            <label class="text-xs mb-1 block font-medium ">Select Filter Type</label>
            <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selectedFilter">
              <option value="" disabled>Select a filter</option> <!-- Placeholder option -->
              <option v-for="item in filter_by" :value="item">
                {{ item.text }}
              </option>
            </select>
          </div>

          <div class="block">
            <label class="text-xs font-medium mb-1 block">Filter By {{ selectedFilter.text }}</label>
            <div v-if="selectedFilter.value===1" class="relative mb-6">
              <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                     @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
              <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
                  v-if="searchDropdown">
                <li v-for="item in organisations"
                    class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                    @click="setClientId2(item)">{{ item.text }}
                </li>
              </ul>
            </div>
            <input v-else-if="selectedFilter.value===2"
                   class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                   v-model="moreParams.loan_number"
                   placeholder="Enter Loan Number" @keyup.enter="applyFilters">
            <input v-else-if="selectedFilter.value===3"
                   class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                   v-model="moreParams.request_number"
                   placeholder="Enter Request Number" @keyup.enter="applyFilters">
            <input v-else
                   class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                   placeholder="Enter Number">
          </div>

          <div class="block">
            <label class="text-xs mb-1 block font-medium">Filter by Date Range</label>
            <VueDatePicker v-model="date" range :preset-ranges="presetRanges" position="center" :clearable="true"
                           :enable-time-picker="false" @closed="selectDate">
              <template #yearly="{ label, range, presetDateRange }">
                <span @click="presetDateRange(range)">{{ label }}</span>
              </template>
            </VueDatePicker>
          </div>
        </div>

        <table class="w-full mb-4 table">
          <thead class="border-b-2 text-xs text-left">
          <tr class="table-row">
            <th class="py-2">Request No</th>
            <th class="py-2">Organisation</th>
            <th class="py-2">Profile</th>
            <th class="py-2">Employee No</th>
            <th class="text-center py-2">Current Limit</th>
            <th class="text-center py-2">Requested Limit</th>
            <th class="text-center py-2">Date</th>
            <th class="text-center py-2">Status</th>
            <th class="text-center">Actions</th>
            <th></th>
          </tr>
          </thead>
          <tbody v-if="loan_limits.length>0" class="text-xs text-gray-600 divide-y">
          <tr v-for="(loan_limit, index) in loan_limits" :key="loan_limit.client_ac">
            <td class="py-2 font-medium">
              <strong>{{ loan_limit.reference_id }}</strong>
            </td>

            <td class="py-2 font-medium">
              <strong>{{ loan_limit.client_name }}</strong>
            </td>

            <td class="py-2 font-medium">
              <strong>{{ capitalizeFirstLetter(loan_limit.first_name) }}
                {{ capitalizeFirstLetter(loan_limit.middle_name) }}</strong>
              <br>
              <span>+{{ loan_limit.msisdn }}</span>
            </td>


            <td class="py-2 font-medium">
              <strong>{{ loan_limit.employee_number }}</strong>
            </td>
            <td class="text-center py-2"> {{ formatCurrency(parseFloat(loan_limit.current_limit).toFixed(2)) }}</td>
            <td class="text-center py-2">{{ formatCurrency(parseFloat(loan_limit.requested_limit).toFixed(2)) }}</td>
            <td class="text-center py-2">
              <span> {{ moment(loan_limit.created).format('lll') }}</span>
            </td>
            <!--Statuses-->
            <td class="text-center py-2">
              <button v-if="parseInt(loan_limit.status) === 1"
                      class="inline-block px-3 py-1 rounded-md text-white bg-green-500">
                Approved
              </button>

              <button v-else-if="parseInt(loan_limit.status) === 2"
                      class="inline-block px-3 py-1 rounded-md text-white bg-orange-500">
                Pending
              </button>

              <button v-else-if="parseInt(loan_limit.status) === 3"
                      class="inline-block px-3 py-1 rounded-md text-white bg-red-500">
                Rejected
              </button>
            </td>
            <!--Actions-->
            <td class="py-2 text-center relative w-24">
              <div class="relative inline-block">
                <button
                    v-if="parseInt(loan_limit.status) === 2"
                    class="px-3 py-1 flex items-center space-x-1"
                    @click="toggleDropdown(index)">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                       stroke="#000000" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </button>

                <button v-else class="px-3 py-1 flex items-center space-x-1">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                       stroke="#808080" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </button>

                <!--ShowDropdown-->
                <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10">
                  <ul class="py-2">
                    <li v-if="parseInt(loan_limit.status) === 2"
                        @click="toggleApprove(loan_limit.reference_id,  1)">
                      <a class="block px-16 py-2 cursor-pointer hover:bg-green-200">Approve</a>
                    </li>
                    <li v-if="parseInt(loan_limit.status) === 2"
                        @click="toggleApprove(loan_limit.reference_id, 3)">
                      <a class="block px-16 py-2 cursor-pointer hover:bg-red-200">Reject</a>
                    </li>
                  </ul>
                </div>

              </div>
            </td>
          </tr>
          </tbody>
          <div v-else class="my-8 flex justify-center">
            No Results
          </div>

        </table>

        <div class="flex w-full text-xs items-center" v-show="total>limit">
          <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
          <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to
            {{ limit * offset < total ? limit * offset : total }} of {{ total }}
          </div>
          <div class="flex-grow text-right" v-show="total > limit">
            <div class="inline-block bg-white border rounded-md divide-x">
              <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1 && offset > 1" @click="gotToPage(offset-1)">
                &larr;
              </button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                      @click="gotToPage(2)">2
              </button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2"
                      @click="gotToPage(3)">3
              </button>
              <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
              <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                      v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
              </button>

              <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                      v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
                {{ Math.ceil(total / limit) }}
              </button>
              <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- Modal -->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': isAcceptRejectModelOpen, 'opacity-0 pointer-events-none': !isAcceptRejectModelOpen }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Loan Review</p>
            <div class="modal-close cursor-pointer z-50" @click="isAcceptRejectModelOpen = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                   viewBox="0 0 18 18">
                <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!-- Body -->
          <div class="mb-4">
            <label class="block text-sm font-medium">Please type
              <strong class="text-blue-500">{{ label }}</strong> to {{ label }} this loan.</label>
            <input
                :placeholder="label"
                type="text"
                v-model="narration"
                class="mt-1 p-2 border rounded-md w-full"
            />
          </div>

          <!-- Your table and other content here -->
          <!-- Footer -->
          <div class="flex justify-end pt-2">

            <button @click="toggleApprove(1, 1, 1)"
                    class="px-4 py-2 bg-transparent rounded-lg text-blue-500 hover:bg-gray-100 hover:text-blue-400">
              Cancel
            </button>
            <button @click="approve"
                    class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 ">
              {{ capitalizeFirstLetter(label) }}
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import store from "../../store";
import $ from 'jquery'
import moment from "moment";
import numeral from "numeral"
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import Download from "@/components/downloadCSV.vue";
// import {tr} from "vuejs3-datepicker/src/components/datepicker/locale";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";

export default {
  data() {
    return {
      isLoading: false,
      fullPage: false,
      total: 0,
      limit: 10,
      offset: 1,
      moment: moment,

      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      status: "",
      reference_id: "",
      narration: "",
      //
      selectedFilter: {text: 'Organization Name', value: 1},
      filter_by: [
        {text: 'Organization Name', value: 1},
        {text: 'Loan Number', value: 2},
        {text: 'Request Number', value: 3},
      ],

      isAcceptRejectModelOpen: false,
      label: "approve",
      showDropdown: [],
      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],

      loan_limits: [],
      organisations: [],
      client_id: "",
      moreParams: {
        offset: "",
        limit: "10",
        sort: "",
        export: "",
        start: "",
        end: "",
        source: "",
        client_id: "",
        amount: "",
        loan_number: "",
        client_phone: "",
        client_email: "",
        channel_name: "",
        reference_type_id: "",
        trxn_reference_id: "",
      },
    }
  }
  ,
  components: {
    Download,
    Loading,
    VueDatePicker
  },
  //
  watch: {
    // watch date
    date(newValue) {
      if (!newValue) {
        this.selectDate(); // Call method when date is cleared
      }
    },
  },
  //
  async mounted() {
    this.moreParams.client_id = this.$route.params.client_id ?? this.$store.state.merchant_account.client_id
    // Call Organisations
    await this.setOrganisations()

    // set date to current month
    // this.date = [startOfMonth(new Date()), endOfMonth(new Date())]
    // this.moreParams.start = this.formatDate(this.date[0])
    // this.moreParams.end = this.formatDate(this.date[1])
    //
    // await this.selectDate()
    await this.setLimitRequests()


  },
  methods: {
    ...
        mapActions(["getLimitRequests", "getMerchants", "approveLimit"]),
    capitalizeFirstLetter(value) {
      if (!value) return '';
      value = value.toLowerCase()
      return value.charAt(0).toUpperCase() + value.slice(1);
    }
    ,
    formatCurrency(number) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },

    //
    setClientId2(item) {
      this.moreParams.client_id = item.value
      this.searchDropdownPlaceholder = item.text
      this.searchDropdown = false
      this.searchClient = ""
      this.setLimitRequests()
    },

    //
    toggleSearchDropdown() {
      this.searchDropdown = !this.searchDropdown;
    },

    ///
    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.moreParams.offset = page
      vm.setLimitRequests()
    },
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    async selectDate() {
      if (this.date !== null) {
        this.moreParams.start = this.formatDate(this.date[0])
        this.moreParams.end = this.formatDate(this.date[1])
      } else {
        this.moreParams.start = ""
        this.moreParams.end = ""
      }

      await this.setLimitRequests()
    },

    //
    async applyFilters() {
      await this.setLimitRequests()
    },
    //
    formatDate(date) {
      let d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },


    // Fetch and set Organisations to UI
    async setOrganisations() {
      let app = this
      app.isLoading = true;
      let response = await this.getMerchants({limit: 100})
      if (response.status === 200) {
        response.message.data.forEach(function (item) {
          let _organisations = {text: item.client_name, value: item.client_id}
          if (item.client_id === app.$route.params.client_id) {
            app.searchDropdownPlaceholder = item.client_name
          }
          app.organisations.push(_organisations)

        })
      } else {
      }
      app.isLoading = false
    },

    async toggleApprove(reference_id, status) {
      this.closeDropdown()
      this.reference_id = reference_id
      this.status = status
      this.narration = ''
      if (status === 3) {
        this.label = 'reject'
      }
      if (status === 1) {
        this.label = 'approve'
      }

      this.isAcceptRejectModelOpen = !this.isAcceptRejectModelOpen
    },

    async approve() {
      let app = this
      if (this.label !== this.narration) {
        return app.handleAlert('Please type ' + this.label + " to " + this.label + " this loan.")
      }

      const payload = {
        reference_id: app.reference_id,
        status: app.status,
        narration: 'Limit of ' + app.reference_id + ' ' + this.label,
      }

      await app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this approve/reject this loan limit!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, confirm!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.isAcceptRejectModelOpen = false
          return await this.approveLimit(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Submitted!', result.value.message, 'success')
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async setLimitRequests() {
      let app = this
      app.isLoading = true
      // console.log("MORE PARAMS", this.moreParams)
      let response = await this.getLimitRequests(this.moreParams)

      if (response.status === 200) {
        this.loan_limits = response.message.data
        app.total = response.message.total_count

        app.showDropdown = []
        for (let i = 0; i < app.loan_limits.length; i++) {
          app.showDropdown.push(false)
        }
      }
      app.isLoading = false
    },

  },


}
</script>
