<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Edit Loan Account</div>

    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">

      <!--Full Name, Phone, Email -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Full Names</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-gray-400"
                 :value="(form.first_name ?? '') + ' ' + (form.last_name ?? '')"
                 disabled>
        </div>

        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Email</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-gray-400"
                 v-model="form.email_address" disabled>
        </div>

        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Phone Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-gray-400" type="tel"
                 v-model="form.msisdn" disabled>
        </div>

      </div>

      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Loan Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-gray-400"
                 placeholder="" type="text" v-model="form.loan_number" disabled/>
        </div>

        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Employee Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.employee_number">
        </div>

        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Net Payable Salary</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="email"
                 v-model="form.net_payable_salary">
        </div>

      </div>

      <!--Employee No, Date of Emp, Netable Salary -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Date of Employment</label>
          <VueDatePicker v-model="form.employment_date" position="center" :clearable="true"
                         :enable-time-picker="false" @closed="selectDate">
            <template #yearly="{ label, range, presetDateRange }">
              <span @click="presetDateRange(range)">{{ label }}</span>
            </template>
          </VueDatePicker>
        </div>

        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Blacklist</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.validate_check">
            <option v-for="check in checks" :value="check.value">
              {{ check.text }}
            </option>
          </select>
        </div>

      </div>

      <!--Nationality, Identify Type, ID number -->
      <div class="grid grid-cols-3 gap-4 mb-4">

      </div>

      <!--Buttons-->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'loan-accounts'}">
          Cancel
        </router-link>

        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20" @click="editAccount"
                id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Save
        </button>

      </div>

    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import moment from "moment-timezone";
import country from 'country-list-js';
import Download from "@/components/downloadCSV.vue";

export default {
  data() {
    return {
      date: null,
      loading: false,
      organisations: [],
      form: {},
      genders: [
        {text: 'MALE', value: 'MALE'},
        {text: 'FEMALE', value: 'FEMALE'},
      ],
      nationalities: [
        {text: 'KENYA', value: 'KENYA'}
      ],
      identifier_types: [
        {text: 'NATIONAL ID', value: 'NATIONAL_ID'},
        {text: 'HUDUMA ID', value: 'HUDUMA_ID'},
        {text: 'PASSPORT', value: 'PASSPORT'},
        {text: 'ALIEN ID', value: 'ALIEN_ID'}
      ],
      checks: [
        {text: 'YES', value: 1},
        {text: 'NO', value: 0},
      ],
    }
  },
  components: {
    Loading,
    VueDatePicker,
  },
  watch: {
    country_name(newVal, oldVal) {
      this.form.country_name = this.country_name
      if (newVal !== oldVal) {
        let found = country.findByName(this.country_name);
        this.form.default_currency = found.currency.code
        this.form.currency_list = found.currency.code
      }
    }
  },
  async mounted() {

    // Call Organisations
    // await this.setOrganisations()
    this.setForm()
  },
  methods: {
    ...mapActions(["updateLoanAccount",]),


    async selectDate() {
      let vm = this
      console.log("DATES XXX:", vm.form.employment_date)

    },

    //
    async editAccount() {
      let app = this
      if (!this.checkFormValidity(app.form)) {
        // Handle the case where there are invalid elements
        // console.log("There are null or empty elements in the form.");
        app.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: `Fill in all data.`
        });
        return
      }

      const payload = app.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new loan account!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateLoanAccount(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              app.resetForm()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    setForm: function () {
      let data = this.$store.state.loan_account
      this.form = data
      this.form.full_name = (data.first_name + " " + data.last_name) ?? "";
    }
  }
}
</script>
