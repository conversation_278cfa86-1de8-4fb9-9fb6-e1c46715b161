<template>
  <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex font-medium p-6 pb-1 border-b gap-4 mb-5">
      <div class="flex-shrink font-medium">
        <i v-if="!this.$store.state.isSideMenuOpen" class="fa fa-bars text-black text-lg" @click="toggleSideM"></i>
        <i v-else class="fa fa-close text-black text-lg" @click="toggleSideM"></i>
      </div>

      <div class="flex-grow font-medium pb-2">
        Loan Accounts - ({{ searchDropdownPlaceholder }})
      </div>
    </div>


<!--    <div class="grid grid-cols-3 gap-x-16 mb-4 px-3">
      <div class="block"/>

      <div class="block"/>

      <div class="text-xs flex justify-end">
        <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold"
                     :to="{ name: 'loan-accounts-add', params: { client_id: this.$store.state.merchant_account.client_account } }">
          Add Loan Account
        </router-link>
      </div>

    </div>-->

    <div class="block py-4 mt-4 rounded-lg bg-white shadow-lg mx-3 border ">

      <div class="grid grid-cols-3 gap-x-6 mx-4 mb-4 ">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Select Filter Type</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selectedFilter">
            <option value="" disabled>Select a filter</option>
            <option v-for="item in filter_by" :value="item">
              {{ item.text }}
            </option>
          </select>
        </div>

        <div class="block">
          <label class="text-xs font-medium mb-1 block">Filter By {{ selectedFilter.text }}</label>
          <div v-if="selectedFilter.value===1" class="relative mb-6">
            <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                   @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
            <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
                v-if="searchDropdown">
              <li v-for="item in organisations"
                  class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                  @click="setClientId2(item)">{{ item.text }}
              </li>
            </ul>
          </div>
          <input v-else-if="selectedFilter.value===2"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="moreParams.loan_number"
                 placeholder="Enter Loan Number" @keyup.enter="applyFilters">
          <input v-else-if="selectedFilter.value===3"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="moreParams.client_email"
                 placeholder="Enter Client Email" @keyup.enter="applyFilters">
          <input v-else-if="selectedFilter.value===4"
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 v-model="moreParams.client_phone"
                 placeholder="Enter Client Phone Number" @keyup.enter="applyFilters">
          <input v-else
                 class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none"
                 placeholder="Enter Number">
        </div>

        <div class="block">
          <label class="text-xs mb-1 block font-medium">Filter by Date Range</label>
          <VueDatePicker v-model="date" range :preset-ranges="presetRanges" position="center" :clearable="true"
                         :enable-time-picker="false" @closed="selectDate">
            <template #yearly="{ label, range, presetDateRange }">
              <span @click="presetDateRange(range)">{{ label }}</span>
            </template>
          </VueDatePicker>
        </div>
      </div>

      <div class="mx-4 mb-4 grid grid-cols-2">
      <div class="block">
        <label for="statusFilter" class="text-sm font-medium">Filter by Status:</label>
        <select
            id="statusFilter"
            v-model="status"
            @change="setLoanAccounts"
            class="border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-green-500 focus:outline-none ml-2 py-1 px-2 text-sm text-gray-800 transition duration-150 ease-in-out hover:border-green-500"
        >
          <option value="">All</option>
          <option value="1000">Active</option>
          <option value="1002">New</option>
          <option value="1003">Unverified</option>
          <option value="1004">Dormant</option>
          <option value="1005">Suspended</option>
        </select>
      </div>

        <div class=" text-xs flex justify-end">
          <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold"
                       :to="{ name: 'loan-accounts-add', params: { client_id: this.$store.state.merchant_account.client_account } }">
            Add Loan Account
          </router-link>
        </div>

      </div>

      <div class="border-b my-2"/>

      <div class="mx-2 p-2 rounded-lg border">
      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">#</th>
          <th class="py-2">Name</th>
          <th class="py-2">Loan Acc No</th>
          <th class="py-2">Phone</th>
          <th class="py-2">Document Type</th>
          <th class="py-2">Max Limit</th>
          <th class="py-2">Wallet</th>
          <th class="py-2 text-center">Status</th>
          <th class="py-2 text-center">Date</th>
          <th class="text-center">Action</th>
        </tr>
        </thead>
        <tbody class="text-xs text-grey-600 divide-y">
        <tr v-for="(loanAccount,index) in loanAccounts" :key="loanAccount.user_id">
          <td class="py-2 w-1 pr-8">{{ index + 1 }}</td>

          <td class="py-2">
            <span class="font-medium">{{ (loanAccount.first_name) }} {{ (loanAccount.last_name) }}</span>
            <br><span style="font-size: 11px; color: grey">{{ loanAccount.email_address }}</span>
          </td>

          <td class="py-2"><strong>{{ loanAccount.loan_number }}</strong></td>

          <td class="py-2">
            <strong>+{{ loanAccount.msisdn }}</strong>
            <br><span style="font-size: 11px; color: grey">{{ (loanAccount.network) }}</span>
          </td>

          <td class="py-2">
            <strong>{{ loanAccount.nationality }} | </strong>
            {{ loanAccount.national_id }}
            <br>
            <strong>{{ loanAccount.gender }} | </strong> {{ moment(loanAccount.dob).format('ll') }}
          </td>

          <td class="py-2">
            <strong>KES. {{ parseFloat(loanAccount.max_approved_loan_amount).toFixed(2) }}</strong>
          </td>

          <td class="py-2">
            <span>Actual: {{ parseFloat(loanAccount.actual_balance).toFixed(2) }}</span>
            <br>
            <span>Loan: {{ parseFloat(loanAccount.loan_balance).toFixed(2) }}</span>
          </td>

          <td class="text-center py-3">
            <button v-if="parseInt(loanAccount.status) === 1000"
                    :class="getStatusClass(1000)"
                    :style="getStatusStyle(1000)">
              {{ getStatusText(1000) }}
            </button>
            <button v-else-if="parseInt(loanAccount.status) === 1002"
                    :class="getStatusClass(1002)"
                    :style="getStatusStyle(1002)">
              {{ getStatusText(1002) }}
            </button>
            <button v-else-if="parseInt(loanAccount.status) === 1003"
                    :class="getStatusClass(1003)"
                    :style="getStatusStyle(1003)">
              {{ getStatusText(1003) }}
            </button>
            <button v-else-if="parseInt(loanAccount.status) === 1004"
                    :class="getStatusClass(1004)"
                    :style="getStatusStyle(1004)">
              {{ getStatusText(1004) }}
            </button>
            <button v-else-if="parseInt(loanAccount.status) === 1005"
                    :class="getStatusClass(1005)"
                    :style="getStatusStyle(1005)">
              {{ getStatusText(1005) }}
            </button>
            <button v-else-if="parseInt(loanAccount.status) === 1006"
                    :class="getStatusClass(1006)"
                    :style="getStatusStyle(1006)">
              {{ getStatusText(1006) }}
            </button>
          </td>

          <td class="py-2 text-center">
            <span> {{ moment(loanAccount.created).format('ll') }}</span>
          </td>

          <td class="py-2 text-center relative w-24">
            <div class="relative inline-block">
              <button
                  v-if="parseInt(loanAccount.status) === 1000 || parseInt(loanAccount.status) === 1005"
                  class="px-3 py-1 flex items-center space-x-1"
                  @click="toggleDropdown(index)">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <button v-else class="px-3 py-1 flex items-center space-x-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#808080" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 230px; text-align: left;">
                <ul class="py-2">
                  <li v-if="parseInt(loanAccount.status) === 1000"
                      @click="activateSuspend(loanAccount.loan_number, 1005)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">
                      Deactivate <strong>{{ loanAccount.first_name }}</strong>
                    </a>
                  </li>
                  <li v-else-if="parseInt(loanAccount.status) === 1005 || parseInt(loanAccount.status) === 1002"
                      @click="activateSuspend(loanAccount.loan_number, 1000)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-red-200">Activate
                      <strong>{{ loanAccount.first_name }}</strong></a>
                  </li>
                  <li v-if="parseInt(loanAccount.black_list_state) === 0"
                      @click="toggleBlacklist(loanAccount.loan_number, loanAccount.status)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-gray-200">Blacklist</a>
                  </li>
                  <li v-else @click="toggleBlockUnblock(0,loanAccount.loan_number, loanAccount.status)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Unblock</a>
                  </li>
                  <li @click="toggleCRBIPRS(loanAccount.loan_number)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-yellow-200">CRB/IPRS Check</a>
                  </li>
                  <li @click="editAccount(loanAccount)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-purple-200">Edit Account</a>
                  </li>
                  <li @click="viewTrx(loanAccount)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-indigo-200">View Transactions</a>
                  </li>
                  <li @click="toggleKyc(loanAccount.loan_number)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Check KYC</a>
                  </li>
                  <li @click="searchAccount(loanAccount)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">
                      Search <strong>{{ loanAccount.first_name }}</strong>
                    </a>
                  </li>
                </ul>
              </div>

            </div>
          </td>

        </tr>
        </tbody>
      </table>

      <!--Pagination-->
      <div v-show="total>limit" class="flex w-full text-xs items-center pb-2">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to {{ limit * offset }} of
          {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1" @click="gotToPage(offset-1)">&larr;</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>
    </div>
    </div>


    <!--Modals-->

    <!-- KYC Modal -->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': open_kyc, 'opacity-0 pointer-events-none': !open_kyc }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm KYC</p>
            <div class="modal-close cursor-pointer z-50" @click="open_kyc = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                   viewBox="0 0 18 18">
                <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!-- Body -->
          <div class="mb-4">
            <div class="row">
              <div class="grid grid-cols-3 gap-4 mb-4">
                <div class="block" style="padding: 1rem;">
                  <div class="column">
                    <h4>ID Front</h4>
                    <div class="ui card">
                      <div class="image">
                        <img :src="idFrontImage || placeholderImage" alt="ID Front" class="kyc-image"/>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="block" style="padding: 1rem;">
                  <div class="column">
                    <h4>ID Back</h4>
                    <div class="ui card">
                      <div class="image">
                        <img :src="idBackImage || placeholderImage" alt="ID Back" class="kyc-image"/>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="block" style="padding: 1rem;">
                  <div class="column">
                    <h4>Selfie</h4>
                    <div class="ui card">
                      <div class="image">
                        <img :src="selfieImage || placeholderImage" alt="Selfie" class="kyc-image"/>
                      </div>
                    </div>
                  </div>
                </div>

              </div>


            </div>
          </div>
          <!-- Footer -->
          <div class="flex justify-end pt-2">
            <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" v-if="imagesFound===true"
                    @click="rejectKYC">Reject
            </button>
            <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-5 pr-5"
                    v-if="imagesFound===true" @click="acceptKYC">Accept
            </button>
            <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2"
                    v-if="imagesFound===false && imagesLoaded===false" @click="toggleKyc1">Close
            </button>
          </div>
        </div>
      </div>
    </div>

    <!--Blacklist Modal-->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': blackListModelOpen, 'opacity-0 pointer-events-none': !blackListModelOpen }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Blacklist Account</p>
            <div class="modal-close cursor-pointer z-50" @click="blackListModelOpen = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                   viewBox="0 0 18 18">
                <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!-- Body -->
          <div class="mb-4">
            <label class="block text-sm font-medium">Blacklist Reason</label>
            <input
                :placeholder="blacklist_reason_hint"
                type="text"
                v-model="blacklist_reason"
                class="mt-1 p-2 border rounded-md w-full"
            />
          </div>

          <!-- Your table and other content here -->
          <!-- Footer -->
          <div class="flex justify-end pt-2">

            <button @click="blackListModelOpen = false"
                    class="px-4 py-2 bg-transparent rounded-lg text-blue-500 hover:bg-gray-100 hover:text-blue-400">
              Cancel
            </button>
            <button @click="toggleBlockUnblock"
                    class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400">
              Block
            </button>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from "@vuepic/vue-datepicker";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";

export default {
  data() {
    return {
      ///
      isLoading: false,
      fullPage: true,
      blacklist_reason_hint: "Reason",
      blacklist_reason: "",
      moment: moment,
      open: false,
      open_kyc: false,
      blackListModelOpen: false,
      loading: true,
      idFrontImage: "",
      idBackImage: "",
      selfieImage: "",
      imagesLoaded: false,
      imagesFound: false,
      loanNumber: null,
      sortOrder: [
        {
          field: "created_at",
          direction: "desc"
        }
      ],
      payload: {},
      moreParams: {
        start: "",
        end: "",
        limit: 10,
        client_id: "",
        client_email: "",
        client_phone: "",
        status: "",
      },
      status: "",

      organisations: [],
      loanAccounts: [],
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],

      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",

      selectedFilter: {text: 'Organization Name', value: 1},
      filter_by: [
        {text: 'Organization Name', value: 1},
        {text: 'Loan Number', value: 2},
        {text: 'Email', value: 3},
        {text: 'Phone Number', value: 4},
      ],

      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],

    }
  },
  components: {
    VueDatePicker,
    Loading
  },
  computed: {
    placeholderImage() {
      return "https://via.placeholder.com/600x400?text=Image+Not+Available";
    }
  },

  // watch for changes
  watch: {
    // watch date
    date(newValue) {
      if (!newValue) {
        this.selectDate(); // Call method when date is cleared
      }
    },
  },

  //
  async mounted() {
    // console.log("KLKL: ", JSON.stringify(this.$route.params.client_id))
    this.moreParams.client_id = this.$route.params.client_id
    // Call Organisations
    await this.setOrganisations()

    // set date to current month
    // this.date = [startOfMonth(new Date()), endOfMonth(new Date())]
    // this.moreParams.start = this.formatDate(this.date[0])
    // this.moreParams.end = this.formatDate(this.date[1])
    //
    // await this.selectDate()
    await this.setLoanAccounts()

  },
  methods: {
    ...mapActions(["getMerchants", "getLoanAccounts", "updateLoanAccount", "fillLoanAcc", "confirmKYC",
      "approveOrRejectKYC", "validateAcc", "toggleSideMenu",]),
    //
    toggleSideM() {
      this.toggleSideMenu()
    },

    //
    gotToPage(page) {
      let vm = this
      vm.moreParams.offset = page
      vm.offset = page
      vm.setLoanAccounts()
    },

    //
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    //
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    //
    async selectDate() {
      if (this.date !== null) {
        this.moreParams.start = this.formatDate(this.date[0])
        this.moreParams.end = this.formatDate(this.date[1])
      } else {
        this.moreParams.start = ""
        this.moreParams.end = ""
      }
      await this.setLoanAccounts()
    },

    //
    async applyFilters() {
      await this.setLoanAccounts()
    },

    //
    setClientId2(item) {
      this.moreParams.client_id = item.value
      this.searchDropdownPlaceholder = item.text
      this.searchDropdown = false
      this.searchClient = ""
      this.setLoanAccounts()
    },
    //
    toggleSearchDropdown() {
      this.searchDropdown = !this.searchDropdown;
    },
    //
    async setLoanAccounts() {
      this.isLoading = true
      let response = await this.getLoanAccounts(this.moreParams)

      if (response.status === 200) {
        this.loanAccounts = response.message.data
        this.total = parseInt(response.message.total_count)

        this.showDropdown = []
        for (let i = 0; i < this.loanAccounts.length; i++) {
          this.showDropdown.push(false)
        }
      } else {
        this.loanAccounts = []
      }

      this.isLoading = false
    },

    //
    async setOrganisations() {
      let app = this
      app.isLoading = true;
      let response = await this.getMerchants({limit: 100})
      if (response.status === 200) {
        response.message.data.forEach(item => {
          let organisation = {text: item.client_name, value: item.client_id}
          if (item.client_id === app.$route.params.client_id) {
            app.searchDropdownPlaceholder = item.client_name
          }
          app.organisations.push(organisation)
        })
      }
      this.isLoading = false
    },

    //
    async toggleBlacklist(loan_number = null, pin_status = null) {
      this.closeDropdown()
      if (loan_number) {
        this.loan_number = loan_number
        this.pin_status = pin_status
      }
      this.blackListModelOpen = !this.blackListModelOpen
    },

    //
    async toggleBlockUnblock(status, loan_number, pin_status) {
      let app = this

      app.payload = {
        blacklist: status,
        blacklist_reason: this.blacklist_reason,
        pin_status: pin_status,
        loan_number: loan_number,
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this updates this loan account!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          console.log(res)
          return await this.updateLoanAccount(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              await app.setLoanAccounts()
              app.block = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    async activateSuspend(loan_number, pin_status) {
      this.closeDropdown()
      let app = this

      const payload = {
        pin_status: pin_status,
        loan_number: loan_number,
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this updates this loan account!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          console.log(res)
          return await this.updateLoanAccount(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              await app.setLoanAccounts()
              app.open = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    async toggleCRBIPRS(loan_number = null) {
      this.closeDropdown()
      if (loan_number) {
        this.loan_number = loan_number
      }
      this.open = !this.open
    },

    //
    async editAccount(row) {
      this.closeDropdown()
      await this.fillLoanAcc(row)
      await this.$router.push({name: 'loan-accounts-edit'})
    },


    //
    async searchAccount(item) {
      this.closeDropdown()
      // console.log("Search Account: ", JSON.stringify(item))
      await this.$router.push({name: 'customers', params: {phone: item.msisdn}})
    },
    //
    async toggleKyc(loan_number) {
      this.loading = true
      this.closeDropdown()
      this.loanNumber = loan_number
      await this.setKYC(loan_number)
      this.open_kyc = !this.open_kyc
      this.loading = false

    },

    toggleKyc1() {
      this.open_kyc = !this.open_kyc
    },

    async setKYC(loan_number) {
      this.imagesLoaded = false

      const payload = {
        "offset": "",
        "limit": "",
        "sort": "",
        "export": "",
        "client_id": "",
        "client_name": "",
        "client_email": "",
        "loan_number": loan_number,
        "client_phone": ""
      };

      let response = await this.confirmKYC(payload)

      if (response.status === 200) {
        // let result = response.message

        const bucketUrl = response.message.bucket_url;

        const fileInfo = JSON.parse(response.message.data[0].file_info);
        const idFront = fileInfo[0].file_name;
        const idBack = fileInfo[1].file_name;
        const image1 = fileInfo[2].file_name;

        this.idFrontImage = bucketUrl + idFront
        this.idBackImage = bucketUrl + idBack
        this.selfieImage = bucketUrl + image1

        this.imagesLoaded = true
        this.imagesFound = true

      } else {
        console.log(`confirmKYC:Else`, response)
        // app.$swal.fire('Error!', result, 'error')

        this.idFrontImage = "https://via.placeholder.com/600x400?text=Image+Not+Available"
        this.idBackImage = "https://via.placeholder.com/600x400?text=Image+Not+Available"
        this.selfieImage = "https://via.placeholder.com/600x400?text=Image+Not+Available"

        this.imagesFound = false
      }
    },

    async acceptKYC() {
      let app = this
      console.log("ACCEPT KYC")
      const payload = {
        "status": "1",
        "acc": app.loanNumber
      };
      let response = await this.approveOrRejectKYC(payload)

      if (response.status === 200) {
        let result = response.message
        console.log(`Image fileInfo: `);

        app.$swal.fire('KYC Approved', result, 'success')

      } else {
        let result = response.message
        console.log("Accept KYC Uploads err: " + JSON.stringify(response.data))
        console.log(`Accept KYC Err: ${response}`)
        app.$swal.fire('Error!', result, 'error')

      }

      this.toggleKyc1()

    },

    async rejectKYC() {
      let app = this
      console.log("Reject KYC")
      const payload = {
        "status": "2",
        "acc": app.loanNumber
      };
      let response = await this.approveOrRejectKYC(payload)

      if (response.status === 200) {
        let result = response.message
        console.log(`Image fileInfo: `);

        app.$swal.fire('KYC Rejected', result, 'success')

      } else {
        let result = response.message
        console.log("Accept KYC Uploads err: " + JSON.stringify(response.data))
        console.log(`Accept KYC Err: ${response}`)
        app.$swal.fire('KYC Error!', result, 'error')

      }

    },

    async viewTrx(acc) {
      this.isLoading = true
      await this.$router.push({name: 'transactions', params: {loan_number: acc.loan_number}})
    },

    getRoleBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-amber-400'
        case 4:
          return 'bg-teal-600'
        case 5:
          return 'bg-sky-600'
        default:
          return 'bg-purple-600'
      }
    },
    getStatusText(status) {
      switch (status) {
        case 1000:
          return 'Active';
        case 1002:
          return 'New';
        case 1003:
          return 'Unverified';
        case 1004:
          return 'Dormant';
        case 1005:
          return 'Suspended';
        case 1006:
          return 'Suspended';
        default:
          return '';
      }
    },
    getStatusClass(status) {
      switch (status) {
        case 1000:
          return 'inline-block px-3 py-1 rounded-md text-white'; // Define your CSS classes for different statuses
        case 1002:
          return 'inline-block px-3 py-1 rounded-md text-white';
        case 1003:
          return 'orange-button';
        case 1004:
          return 'purple-button';
        case 1005:
          return 'inline-block px-3 py-1 rounded-md text-white';
        case 1006:
          return 'red-button';
        default:
          return '';
      }
    },
    getStatusStyle(status) {
      switch (status) {
        case 1000:
          return 'background-color: green;'; // Define your inline styles for different statuses
        case 1002:
          return 'background-color: blue;';
        case 1003:
          return 'background-color: orange;';
        case 1004:
          return 'background-color: purple;';
        case 1005:
        case 1006:
          return 'background-color: red;';
        default:
          return '';
      }
    },

  },
}
</script>
