<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Add Loan Account</div>

    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <!--Organisation Dropdown -->
      <!--      <div class="relative mb-6">
              <label class="text-xs mb-1 block font-medium">Filter By Organisation <strong
                  v-show="clientName">({{ clientName }})</strong></label>
              <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                     @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
              <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
                  v-if="searchDropdown">
                <li v-for="item in filteredOrganizations"
                    class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                    @click="setClientId(item)">{{ item.text }}
                </li>
              </ul>
            </div>-->

      <!--Full Name, Phone, Email -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Full Names</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.full_name"
                 placeholder="John Doe Smith">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Phone Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                 v-model="form.msisdn" placeholder="07xx xxx xxx">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Email Address</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="email"
                 v-model="form.email_address" placeholder="<EMAIL>">
        </div>
      </div>

      <!--Employee No, Date of Emp, Netable Salary -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Employee Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.employee_number"
                 placeholder="EMP 001">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Date of Employment</label>
          <VueDatePicker placeholder="dd/mm/yyyy" v-model="dateDOE" position="center" :clearable="true"
                         :enable-time-picker="false" @closed="selectDate">
            <template #yearly="{ label, range, presetDateRange }">
              <span @click="presetDateRange(range)">{{ label }}</span>
            </template>
          </VueDatePicker>

        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Netable Salary</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="email"
                 v-model="form.netable_salary" placeholder="40,000">
        </div>
      </div>

      <!--Nationality, Identify Type, ID number -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Validate Check</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.validate_check">
            <option v-for="gender in checks" :value="gender.value">
              {{ gender.text }}
            </option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Gender</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.gender">
            <option value="" disabled>Select Gender</option> <!-- Placeholder option -->
            <option v-for="gender in genders" :value="gender.value">
              {{ gender.text }}
            </option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Date of Birth</label>
          <VueDatePicker placeholder="dd/mm/yyyy" v-model="dateDOB" position="center" :clearable="true"
                         :enable-time-picker="false" @closed="selectDate">
            <template #yearly="{ label, range, presetDateRange }">
              <span @click="presetDateRange(range)">{{ label }}</span>
            </template>
          </VueDatePicker>

        </div>
      </div>

      <!--Nationality, Identify Type, ID number -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Nationality</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.nationality">
            <option value="" disabled selected>Select Country</option> <!-- Placeholder option -->
            <option v-for="nationality in nationalities" :value="nationality.value">
              {{ nationality.text }}
            </option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Identify Type</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.identifier_type">
            <option value="" disabled selected>Select ID Type</option> <!-- Placeholder option -->
            <option v-for="type in identifier_types" :value="type.value">
              {{ type.text }}
            </option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">ID Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="email"
                 v-model="form.national_id" placeholder="********">
        </div>
      </div>

      <!--Buttons-->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2"
                     :to="{name: 'loan-accounts', params: { client_id: this.$route.params.client_id } }">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20" @click="addAccount"
                id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Submit
        </button>
      </div>

    </div>
  </div>
</template>

<script>

import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import moment from "moment-timezone";
import country from 'country-list-js';
import VueDatePicker from "@vuepic/vue-datepicker";

export default {
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      isLoading: false,
      loading: false,
      organisations: [],
      dateDOB: null,
      dateDOE: null,
      form: {
        dial_code: "254",
        msisdn: null,
        email_address: null,
        full_name: null,
        employee_number: null,
        employment_date: null,
        gender: null,
        dob: null,
        netable_salary: "",
        validate_check: 0,
        national_id: null,
        nationality: 'KENYA',
        identifier_type: 'NATIONAL_ID',
        id: this.$route.params.client_id
      },
      genders: [
        {text: 'MALE', value: 'MALE'},
        {text: 'FEMALE', value: 'FEMALE'},
      ],
      nationalities: [
        {text: 'KENYA', value: 'KENYA'}
      ],
      identifier_types: [
        {text: 'NATIONAL ID', value: 'NATIONAL_ID'},
        {text: 'HUDUMA ID', value: 'HUDUMA_ID'},
        {text: 'PASSPORT', value: 'PASSPORT'},
        {text: 'ALIEN ID', value: 'ALIEN_ID'}
      ],
      checks: [
        {text: 'YES', value: 1},
        {text: 'NO', value: 0},
      ],
    }
  },
  components: {
    VueDatePicker,
    Loading
  },
  // watch: {
  //   searchClient(newVal, oldVal) {
  //     if (newVal !== oldVal && newVal !== "") {
  //       // this.filterOrganizations();
  //     }
  //   }
  // },
  computed: {
    filteredOrganizations() {
      console.log()
      return this.organisations.filter(org => org.text.toLowerCase().includes(this.searchClient.toLowerCase()));
    }
  },
  async mounted() {
    // Call Organisations
    // await this.setOrganisations()
    this.form.id = this.$route.params.client_id //?? this.$store.state.merchant_account.client_id

    // console.log("KLKLddddd", JSON.stringify(this.$store.state.merchant_account.client_account))
    // console.log("AddLoan Acc id:", this.$route.params.client_id)
    // console.log("AddLoan Acc client_id:", JSON.stringify(this.form))
  },
  methods: {
    ...mapActions(["getMerchants", "addLoanAccount",]),

    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },
    async selectDate() {
      let vm = this
      vm.form.employment_date = vm.formatDate(this.dateDOE)
      vm.form.dob = vm.formatDate(this.dateDOB)
      console.log("DATES DOE :", vm.form.employment_date)
      console.log("DATES DOB :", vm.form.dob)
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Fetch and set Organisations to UI
    async setOrganisations() {
      let app = this
      app.isLoading = true;
      let response = await this.getMerchants({limit: 100})
      if (response.status === 200) {
        response.message.data.forEach(function (item) {
          let _organisations = {text: item.client_name, value: item.client_id, value2: item.client_account}
          app.organisations.push(_organisations)
        })
      } else {
      }
      app.isLoading = false
    },

    //
    checkFormValidity(payload) {
      for (const key in payload) {
        if (payload[key] === null || payload[key] === '') {
          return false;
        }
      }
      return true;
    },

    //
    async addAccount() {
      let app = this
      if (!this.checkFormValidity(app.form)) {
        // Handle the case where there are invalid elements
        console.log("There are null or empty elements in the form.");
        app.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: `Fill in all data.`
        });
        return
      }

      const payload = app.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new loan account!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.addLoanAccount(payload)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              app.resetForm()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

  }
}
</script>
