<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Add Loan Product</div>

    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <!--Organisation Dropdown -->
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Select Product</label>
        <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="product_id">
          <option value="" disabled selected>Select a Product</option> <!-- Placeholder option -->
          <option v-for="product in products" :value="product.value">
            {{ product.text }}
          </option>
        </select>
      </div>

      <!--Max Loan Amount, Min Loan Amount, Interest (in%) -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Min Loan Amount</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number"
                 v-model="form.min_loan_amount"
          placeholder="1000">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Max Loan Amount</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number"
                 v-model="form.max_loan_amount"  placeholder="50,000">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Interest (in%)</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number"
                 v-model="form.interest_rate" placeholder="15">
        </div>
      </div>

      <!--Loan Name, Repayment Period (Months) -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block "> Loan Name </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.product_name"
                 placeholder="Mid-Month">
        </div>

        <div class="block">
          <label class="text-xs mb-1 block "> Repayment Period (Months) </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number"
                 v-model="form.repayment_period_in_months"  placeholder="1">
        </div>
      </div>


      <!--Buttons-->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'loan-products'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20" @click="addProduct"
                id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Submit
        </button>
      </div>

    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import moment from "moment-timezone";
import country from 'country-list-js';
import VueDatePicker from "@vuepic/vue-datepicker";

export default {
  data() {
    return {
      loading:false,
      form: {
        account_number: this.$store.state.organizationAccount.client_account,
        product_id: null,
        product_name: "",
        min_loan_amount: null,
        max_loan_amount: null,
        interest_rate: null,
        repayment_period_in_months: null,
      },
      product_id: null,
      products: [],
      moreParams: {
        limit: 1000
      }
    }
  },
  components: {
    VueDatePicker,
    Loading
  },
  mounted() {
    if (!this.$store.state.organizationAccount.client_account){
      this.$router.push({ name: ''})
    }
    this.setProducts()
  },
  watch: {
    product_id(newVal, oldVal){
      let app = this
      app.form.product_id = this.product_id
      if (newVal !== oldVal){
        this.products.forEach(function (item){
          if (item.value === app.product_id){
            app.form.min_loan_amount = item.min
            app.form.max_loan_amount = item.max
          }
        })
      }
    }
  },
  methods: {
    ...mapActions(["addLoanProduct", "getLoanProducts"]),

    //
    async addProduct() {
      let app = this

      console.log("this.product_id : " + this.product_id)
      console.log("this.form : " + JSON.stringify(app.form))
      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new loan account!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.addLoanProduct(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              app.resetForm()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async setProducts() {
      let app = this
      let response = await this.getLoanProducts(app.moreParams)
      if (response.status === 200){
        response.message.forEach(function (item) {
          let list = { text: item.product_name, value: parseInt(item.product_id), min: item.minimum_loan_amount, max: item.maximum_loan_amount }
          app.products.push(list)
        })
      }
    },

    resetForm: function () {
      this.form.product_id = 1
      this.form.min_loan_amount = 500
      this.form.max_loan_amount = 10000
    }
  },

}
</script>
