<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">Edit Loan Account</div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <!--Product Name, Product Description -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium"> Product Name </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                 v-model="form.product_name" >
        </div>
        <div class="block">
          <label class="text-xs mb-1 block font-medium"> Product Description </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.product_desc">
        </div>

      </div>

      <!-- Interest Rate, Repayment Period (Months) -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block font-medium"> Interest Rate </label>
          <input class="w-full block px-4 py-2 border bg-white
          rounded-md outline-none" v-model="form.interest_rate">
        </div>

        <div class="block">
          <label class="text-xs mb-1 block font-medium"> Repayment Period (Months) </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="email"
                 v-model="form.repayment_period_in_months">
        </div>
      </div>

      <!--Buttons-->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'loan-products'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20" @click="editAccount"
                id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Submit
        </button>
      </div>

    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import moment from "moment-timezone";
import country from 'country-list-js';
import Download from "@/components/downloadCSV.vue";

export default {
  data() {
    return {
      loading:false,
      form: {
        product_id: null,
        product_name: null,
        product_desc: null,
        interest_rate:0,
        repayment_period_in_months: 1,
      },
      products: [],
      moreParams: {
        limit: 1000
      }
    }
  },
  components: {
    Loading,
    VueDatePicker,
  },

  mounted() {
    //this.setProducts()
    this.setForm()
  },

  methods: {
    ...mapActions(["updateLoanProduct", "getSystemProducts"]),
    //
    async editAccount() {
      let app = this

     app.form.interest_rate=parseInt(app.form.interest_rate)/100
      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this updates this loan product!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateLoanProduct(payload)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.$swal.fire({
                title: 'Updated!',
                text: result.value.message,
                icon: 'success'
              }) .then(async (result) => {
                await this.$router.push({name: 'loan-products'})
              })
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async setProducts() {
      let app = this
      let response = await this.getSystemProducts(app.moreParams)
      if (response.status === 200){
        response.message.data.forEach(function (item) {
          let list = { text: item.product_name, value: parseInt(item.product_id) }
          app.products.push(list)
        })
      }
    },

    setForm: function () {
      let data = this.$store.state.loan_account
      this.form.product_id = parseInt(data.product_id)
      this.form.product_name = data.product_name
      this.form.product_desc = data.product_description
      this.form.interest_rate = data.interest_rate
      this.form.repayment_period_in_months = parseInt(data.repayment_period)
    }
  },
}
</script>
