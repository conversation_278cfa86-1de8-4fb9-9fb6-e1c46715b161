<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-1 border-b mb-5">
      Loan Products- ({{ this.$store.state.merchant_account.client_name }})
    </div>

    <div v-if="this.$store.state.isSuperRole" class="w-full flex mb-4 px-6 text-xs gap-2">
      <div class="flex-grow gap-2 flex"></div>
      <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold" :to="{ name: 'loan-products-add' }">
        Add Loan Product
      </router-link>
    </div>

    <div class="rounded-lg bg-white shadow-lg mx-3 px-6  border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">#</th>
          <th class="py-2">Name</th>
          <th class="py-2">Period</th>
          <th class="py-2">Interest</th>
          <th class="py-2">Late Fine Interest</th>
          <th class="py-2">Loan Amount</th>
          <th v-if="this.$store.state.role===1" class="text-center">Action</th>
          <th></th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(loanProduct,index) in data" :key="loanProduct.client_product_id">

          <td class="py-2 w-1 pr-8">{{ index + 1 }} </td>

          <td class="py-2"> <strong> {{ loanProduct.product_name }} </strong> </td>

          <td class="py-2"> {{ loanProduct.repayment_period }} (months)</td>

          <td class="py-2"> <strong style="color: green;"> {{ loanProduct.interest_rate }} </strong> </td>

          <td class="py-2"> <strong style="color: green;"> {{ parseFloat(loanProduct.late_fine_interest_rate) * 100 }}% </strong> </td>

          <td class="py-2">
            <strong>{{ loanProduct.currency_code }}. {{ formatCurrency(loanProduct.minimum_loan_amount) }} -
              {{ formatCurrency(loanProduct.maximum_loan_amount) }} </strong>
          </td>

          <td v-if="this.$store.state.role===1" class="py-2 text-center relative w-24">
            <div class="relative inline-block">
              <button class="px-3 py-1 flex items-center space-x-1" @click="toggleDropdown(index)">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10">
                <ul class="py-2">
                  <li @click="editRow(loanProduct)">
                    <a class="block px-16 py-2 cursor-pointer hover:bg-green-200">Edit</a>
                  </li>
                </ul>
              </div>

            </div>
          </td>

        </tr>
        </tbody>
      </table>

      <!--Pagination-->
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to {{ limit * offset }} of
          {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1" @click="gotToPage(offset-1)">&larr;</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import numeral from "numeral"
import {mapActions} from "vuex";

export default {
  data() {
    return {
      isLoading: false,
      fullPage: true,
      moreParams: {
        start: "",
        end: "",
        limit: 100,
        client_id: this.$store.state.merchant_account.client_id,
      },
      days: 3,
      time3: "",
      perPage: 10,
      data: [],
      moment: moment,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
    }
  },
  components: {
    Loading
  },

  mounted() {
    this.setLoanProducts()
  },

  methods: {
    ...mapActions(["getLoanProducts", "validateAcc", "fillLoanProduct"]),
    formatCurrency(number) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    ///
    gotToPage(page) {
      let vm = this
      vm.moreParams.offset = page
      vm.offset = page
      vm.setLoanAccounts()
    },
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },
    async editRow(row) {
      this.closeDropdown()
      await this.fillLoanProduct(row)
      await this.$router.push({name: 'loan-products-edit'})
    },

    async setLoanProducts() {
      this.isLoading = true
      let response = await this.getLoanProducts(this.moreParams)
      this.data = response.message

      this.showDropdown = []
      for (let i = 0; i < this.data.length; i++) {
        this.showDropdown.push(false)
      }


      this.isLoading = false

    }
  }


}
</script>
