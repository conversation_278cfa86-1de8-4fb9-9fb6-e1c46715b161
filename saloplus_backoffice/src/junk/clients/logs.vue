<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-4">Logs</div>
    <div class="block px-6 my-3">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="order-2 md:order-1">
          <div class="bg-white shadow-md rounded-md pb-3">
            <div class="px-4 py-2 md:px-6 flex flex-row justify-between items-center">
              <h2 class="text-lg font-medium text-gray-800 md:mr-4">Login Stats</h2>
            </div>
            <table class="text-xs gap-y-4">
              <tr>
                <td class="pr-4 py-2 px-4 md:px-6">Login Attempts (Successful/Failed):</td>
                <td>{{ user_login.success_attempts }} / {{ user_login.cumulative_failed_attempts }}</td>
              </tr>
              <tr>
                <td class="pr-4 py-2 px-4 md:px-6">Last Login (Successful/Failed):</td>
                <td>{{ moment(user_login.last_successful_login_date).format('lll') }} / {{ moment(user_login.last_failed_login_date).format('lll') }}</td>
              </tr>
              <tr>
                <td class="pr-4 py-2 px-4 md:px-6">Created / Activation Date:</td>
                <td>{{ moment( user_login.created_at).format('lll') }} / {{ moment(user_login.activation_date).format('lll')}}</td>
              </tr>
            </table>
          </div>
        </div>
        <div class="order-1 md:order-2">
          <div class="bg-white shadow-md rounded-md pb-3">
            <div class="px-4 py-2 md:px-6 flex flex-row justify-between items-center">
              <h2 class="text-lg font-medium text-gray-800 md:mr-4">Permissions ( {{ role }})</h2>
            </div>
            <ul class="list-disc b-bot px-6">
              <li v-for="list in permissions">{{ list.name }}</li>
              <!-- ... -->
            </ul>
          </div>
        </div>
      </div>

    </div>
    <div class="px-6">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage" />
      <table class="w-full mb-4 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">Action</th>
          <th class="py-2">Description</th>
          <th class="py-2">Created</th>
          <th></th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="item in items" :key="item.id">
          <td class="py-2.5 font-medium">{{item.action}}</td>
          <td class="py-2.5">{{item.description}}</td>
          <td class="py-2.5">{{moment(item.created).format('lll')}}</td>
        </tr>
        </tbody>
      </table>
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{offset + 1}} to {{limit}} of {{total}}</div>
        <div class="flex-shrink" v-show="offset > 0">{{((offset * limit)-limit) + 1}} to {{limit * offset < total ? limit * offset : total}} of {{total}}</div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1 && offset > 1" @click="gotToPage(offset-1)">&larr;</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1" @click="gotToPage(2)">2</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2" @click="gotToPage(3)">3</button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}" v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{offset}}</button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}" v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">{{Math.ceil(total/limit)}}</button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import store from "../../store";
import $ from 'jquery'
import moment from "moment";
import numeral from "numeral"
import {mapActions} from "vuex";
import accounting from "accounting";
export default {
  data() {
    return {
      isOpen: false,
      isLoading: false,
      items: [],
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      phone: null,
      customer: { mobile: '' },
      status: null,
      accounting: accounting,
      moment: moment,
      moreParams: {
        start: "",
        filter: "",
        end: "",
      },
      permissions: [],
      user_login: {},
      role: '',
    }
  },
  components: {
    Loading
  },

  async mounted() {
    // await this.getItems()
  },
  methods: {
    ...mapActions(["getUserLogs"]),
    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.getItems()
    },
    async getItems() {
      let vm = this
      vm.isLoading = true;
      this.moreParams.page = vm.offset
      this.moreParams.per_page = vm.limit

      let response = await this.getUserLogs(this.moreParams)
      this.items = response.message.audit_logs.data

      this.total = parseInt(response.message.audit_logs.total)

      this.role = response.message.role
      this.permissions = response.message.permissions
      this.user_login = response.message.user_login

      vm.isLoading = false;
    },
    getStatusSlip(id){
      id = parseInt(id)
      switch (id) {
        case 2:
          return 'bg-green-600'
        case 1:
          return 'bg-orange-500'
        case 5:
          return 'bg-red-600'
        default:
          return 'bg-purple-600'
      }
    },
    getStatus(id){
      id = parseInt(id)
      switch (id) {
        case 0:
          return 'bg-green-600'
        case 5:
          return 'bg-orange-500'
        case 3:
          return 'bg-red-600'
        default:
          return 'bg-purple-600'
      }
    },
    async setClients() {
      let app = this
      let payload = { page: 1, per_page: 100 }
      let response = await this.getClients(payload)
      if (response.status === 200){
        app.clients = response.message.data
      }
    },

    toggleEdit(data) {
      this.form.id = data.odd_type_id
      this.form.odd_type_alias = data.odd_type_alias
      this.form.priority = data.priority
      this.isOpen = !this.isOpen
    },

    async editRow(){
      let app = this
      let payload = this.form
      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this market name!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateKironMarket(payload)

        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.resetForm()
              app.$swal.fire({
                title: 'Updated!',
                text: result.value.message,
                icon: 'success'
              }) .then(async (result) => {
                await app.getItems()
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async activate(id){
      let app = this
      let payload = {
        id: id,
        status: 1
      }
      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this activates this market name!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, activate!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateKironMarket(payload)

        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.resetForm()
              app.$swal.fire({
                title: 'Activated!',
                text: result.value.message,
                icon: 'success'
              }) .then(async (result) => {
                await app.reload()
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async deactivate(id){
      let app = this
      let payload = {
        id: id,
        status: 3
      }
      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this deactivates this market name!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, deactivate!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateKironMarket(payload)

        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.resetForm()
              app.$swal.fire({
                title: 'Deactivated!',
                text: result.value.message,
                icon: 'success'
              }) .then(async (result) => {
                await app.reload()
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    resetForm(){
      this.form.id = null
      this.form.odd_type_alias = null
      this.form.priority = null
      this.isOpen = false
    },
    getCompetition(id){
      //let app = this
      let name = "English League"
      id = parseInt(id)
      if (id === 2){
        name = "Spanish League"
      } else if (id === 4){
        name = "Italian League"
      } else if (id === 7){
        name = "Germany League"
      } else if (id === 8){
        name = "French League"
      } else if (id === 5){
        name = "World Cup Tournament"
      }
      return name
    },

    async viewBetslip(bet_id) {
      this.isLoading = true
      let response = await this.getBetDetails(bet_id+'?competition_id='+this.competition_id)
      if (response.status === 200){
        this.bets = response.message
        this.isLoading = false
        this.isOpen = true
      } else {
        this.isLoading = false
        this.$swal.fire('Error!', response.message, 'error')
      }
    },

    async setCustomer(params) {
      let response = await this.searchCustomerProfile(params)
      this.customer = response.message
      this.status = response.status
      if (response.status === 200){
        await this.getItems()
      }
    },

    async searchPhone(){
      let app = this
      if (this.phone.length > 0){
        if (this.phone.slice(0, 1) === '0') this.phone = this.phone.slice(1)
        this.moreParams.msisdn = this.phone
        await this.fillMsisdn(this.phone)
        await app.setCustomer(app.moreParams)
      }
    },

  }
}
</script>
