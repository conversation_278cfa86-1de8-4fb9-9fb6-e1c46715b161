<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-4 border-b">Add User</div>
    <div class="block px-6 py-3">
      <div v-if="parseInt(this.$store.state.client_id) === 0" class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Client</label>
        <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.client_id">
          <option selected>Select Client</option>
          <option value="0">Kiron Lite</option>
          <option v-for="item in clients" :value="item.id">{{ item.client_name }}</option>
        </select>
      </div>
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">First Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.first_name">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Last Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.last_name">
        </div>
      </div>
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block">Email address</label>
          <input type="email" class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.email_address">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block text-neutral-500">Phone number</label>
          <input type="tel" class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.mobile_number">
        </div>
      </div>
      <div v-if="parseInt(this.$store.state.client_id) === 0" class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Role</label>
        <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.role_id">
          <option selected>Select Role</option>
          <option v-for="item in roles" :value="item.id">{{ item.name }}</option>
        </select>
      </div>
      <div class="gap-4 block text-sm text-right">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'users'}">Cancel</router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2" @click="createUser()" id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat> Add User
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
export default {
  data() {
    return {
      form: {
        country_code: "254",
        client_id: this.$store.state.client_id,
        mobile_number: null,
        email_address: null,
        first_name: null,
        middle_name: null,
        last_name: null,
        role_id: null,
      },
      clients: [],
      roles: [],
      loading: false
    }
  },
  components: {
    Loading
  },
  async mounted() {
    await this.setClients()
    await this.setRoles()
  },
  methods: {
    ...mapActions(["Register", "getSystemRoles", "getClients"]),
    async setRoles() {
      let app = this
      let payload = { page: 1, per_page: 100 }
      let response = await this.getSystemRoles(payload)
      if (response.status === 200){
        app.roles = response.message.data
      }
    },
    async setClients() {
      let app = this
      let payload = { page: 1, per_page: 100 }
      let response = await this.getClients(payload)
      if (response.status === 200){
        app.clients = response.message.data
      }
    },
    async createUser() {
      let app = this
      app.loading = true
      $('#addMember').html(' Please Wait ...');

      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.Register(payload)
        },
      })
          .then(async (result) => {
            $('#addMember').html('Add Client');
            app.loading = false
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              await app.$router.push({name: 'users'})
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

  }
}
</script>
