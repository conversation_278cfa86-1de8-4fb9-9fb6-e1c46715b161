<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-4 border-b">Edit User</div>
    <div class="block px-6 py-3">
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">First Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.first_name">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Last Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.last_name">
        </div>
      </div>
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block">Email address</label>
          <input type="email" class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.email_address" disabled>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block text-neutral-500">Phone number</label>
          <input type="tel" class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.mobile_number" disabled>
        </div>
      </div>
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Permissions</label>
        <v-select
            class=""
            v-model="form.permissions"
            :options="items"
            multiple
            placeholder="Select Permission"
        />
      </div>
      <div class="gap-4 block text-sm text-right">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'roles'}">Cancel</router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2" @click="editRow()" id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat> Save
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
export default {
  data() {
    return {
      form: {
        id: null,
        mobile_number: null,
        email_address: null,
        first_name: null,
        middle_name: null,
        last_name: null,
        permissions: null,
        role_id: null,
        status: "1",
      },
      items: [],
      loading: false
    }
  },
  components: {
    Loading
  },
  async mounted() {
    await this.setPermissions()
    await this.setForm()
  },
  methods: {
    ...mapActions(["updateUser", "getSystemRoles", "getSystemPermissions"]),

    async setPermissions() {
      let app = this
      let payload = { page: 1, per_page: 200 }

      let response = await this.getSystemPermissions(payload)
      if (response.status === 200){
        response.message.data.forEach(function (item) {
          let list = { label: item.name, value: parseInt(item.id) }
          app.items.push(list)
        })
      }
    },

    async editRow(){
      let app = this
      app.loading = true
      $('#addMember').html(' Please Wait ...');
      let acl = []
      this.form.permissions.forEach(function (item) {
        acl.push(item.value)
      })
      this.form.permissions = acl

      let payload = this.form
      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds updates this role!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateUser(payload)

        },
      })
          .then(async (result) => {
            $('#addMember').html('Save');
            app.loading = false
            if (result.value.status === 200) {
              app.$swal.fire({
                title: 'Updated!',
                text: result.value.message,
                icon: 'success'
              }) .then(async (result) => {
                await app.$router.push({name: 'users'})
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async setForm() {
      let app = this

      let data = this.$store.state.user_name

      this.form.id = data.profile_id
      this.form.email_address = data.email_address
      this.form.first_name = data.first_name
      this.form.middle_name = data.middle_name
      this.form.last_name = data.surname
      this.form.mobile_number = data.mobile_number
      this.form.role_id = parseInt(data.acl_id)

      let acl_list = JSON.parse(data.permissions)

      let lists = []
      for (const item of acl_list) {
        let label = await app.getListName(item)
        let list = {label: label, value: item}
        lists.push(list)
      }

      this.form.permissions = lists
    },

    async getListName(id){
      let label = ''
      id = parseInt(id)
      this.items.forEach(function (item) {
        if (parseInt(item.value) === id){
          label = item.label
          return item.label
        }
      })
      return label
    }

  }
}
</script>
