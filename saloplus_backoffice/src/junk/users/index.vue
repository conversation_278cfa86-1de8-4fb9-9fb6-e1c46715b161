<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <div class="flex-grow font-medium p-6 pb-4">Users</div>
    <div class="w-full flex mb-4 px-6 text-xs gap-2">
      <div class="flex-grow gap-2 flex">
        <input class="inline-block px-4 py-2 rounded-md border bg-neutral-100 outline-none" placeholder="Search" v-model="moreParams.filter" @keyup="getUsers"/>
      </div>
      <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold" :to="{ name: 'users-add' }">Add user</router-link>
    </div>
    <div class="px-6">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage" />
      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2" v-if="parseInt(this.$store.state.client_id) === 0">Client Name</th>
          <th class="py-2">Full Names</th>
          <th class="py-2">Email Address</th>
<!--          <th class="py-2">Email</th>-->
          <th class="text-center py-2">Role</th>
          <th class="text-center py-2">Status</th>
          <th class="py-2">Last Login</th>
          <th class="py-2">Created</th>
          <th class="text-center">Action</th>
          <th></th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(user,index) in users" :key="user.user_id">
          <td class="py-3" v-if="parseInt(this.$store.state.client_id) === 0">{{user.client_name}}</td>
          <td class="py-3 font-medium">{{user.first_name}} {{user.surname}}</td>
          <td class="py-3">{{user.email_address}}</td>
<!--          <td class="py-3">{{user.email_address}}</td>-->
          <td class="text-center py-3">
            <button class="inline-block px-4 py-2 rounded-md text-white" :class="getRoleBg(user.acl_id)">{{user.acl_name}}</button>
          </td>
          <td class="text-center py-3">
            <button class="inline-block px-4 py-2 rounded-md text-white" :class="getStatusBg(user.user_state)">
              {{ parseInt(user.user_state) === 1 ? 'Active' : parseInt(user.user_state) === 3 ? 'Deactivated' : 'Inactive' }}
            </button>
          </td>
          <td class="py-3">{{moment(user.last_successful_login_date).format('lll')}}</td>
          <td class="py-3">{{moment(user.created_at).format('lll')}}</td>
          <td class="py-3 text-center relative w-24">
            <button class="px-3 py-1 border rounded-md" @click="showDrop(index)">More</button>
            <ul v-show="showDropdown[index]" class="absolute z-10 top-0 left-0 mt-12 py-2 w-full bg-white rounded-md shadow-lg">
              <li class="px-4 py-2 cursor-pointer hover:bg-gray-200" @click="permissionList(user.permissions_list)" v-if="user.permissions_list.length > 0">Permissions</li>
              <li v-if="parseInt(this.$store.state.client_id) === 0" class="px-4 py-2 cursor-pointer hover:bg-gray-200" @click="editRow(user)">Edit</li>
              <li v-if="parseInt(this.$store.state.client_id) === 0" class="px-4 py-2 cursor-pointer hover:bg-gray-200" @click="assignClient(user)">Assign Client</li>
              <li class="px-4 py-2 cursor-pointer hover:bg-gray-200" @click="otp(user.profile_id)">Resend OTP</li>
              <li class="px-4 py-2 cursor-pointer hover:bg-gray-200" v-if="parseInt(user.user_state) === 3" @click="activate(user.profile_id)">Deactivate</li>
              <li class="px-4 py-2 cursor-pointer hover:bg-gray-200" v-else @click="deactivate(user.profile_id)">Deactivate</li>
            </ul>
          </td>
        </tr>
        </tbody>
      </table>
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{offset + 1}} to {{limit}} of {{total}}</div>
        <div class="flex-shrink" v-show="offset > 0">{{((offset * limit)-limit) + 1}} to {{limit * offset < total ? limit * offset : total}} of {{total}}</div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1 && offset > 1" @click="gotToPage(offset-1)">&larr;</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1" @click="gotToPage(2)">2</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2" @click="gotToPage(3)">3</button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}" v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{offset}}</button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}" v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">{{Math.ceil(total/limit)}}</button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Modal -->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center" :class="{ 'opacity-100 pointer-events-auto': isOpen, 'opacity-0 pointer-events-none': !isOpen }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!--Title-->
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Permissions</p>
            <div class="modal-close cursor-pointer z-50" @click="isOpen = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!--Body-->
          <ul class="list-disc b-bot">
            <li v-for="list in permissions">{{ list.name }}</li>
            <!-- ... -->
          </ul>
          <!--Footer-->
          <div class="flex justify-end pt-2">
            <button @click="isOpen = false" class="px-4 bg-transparent p-3 rounded-lg text-indigo-500 hover:bg-gray-100 hover:text-indigo-400 mr-2">Close</button>
          </div>
        </div>
      </div>
    </div>

    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center" :class="{ 'opacity-100 pointer-events-auto': isClient, 'opacity-0 pointer-events-none': !isClient }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!--Title-->
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Assign Clients</p>
            <div class="modal-close cursor-pointer z-50" @click="isClient = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!--Body-->
          <div class="block mb-4">
            <label class="text-xs mb-1 block font-medium">Clients</label>
            <v-select
                class=""
                v-model="client_list"
                :options="clients"
                multiple
                placeholder="Select Client"
            />
          </div>
          <!--Footer-->
          <div class="flex justify-end pt-2">
            <button @click.prevent="addClient" class="px-4 bg-transparent p-3 rounded-lg text-indigo-500 hover:bg-gray-100 hover:text-indigo-400 mr-2">Save</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import store from "../../store";
import $ from 'jquery'
import moment from "moment";
import numeral from "numeral"
import {mapActions} from "vuex";
export default {
  data() {
    return {
      isOpen: false,
      isClient: false,
      isLoading: false,
      users: [],
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showEdit: false,
      selectStaff: null,
      showDropdown: [],
      selectedOption: '',
      moment: moment,
      moreParams: {
        start: "",
        filter: "",
        end: "",
        client_id: this.$store.state.client_id
      },
      permissions: [],
      clients: [],
      client_list: [],
      profile_id: null

    }
  },
  components: {
    Loading
  },

  async mounted() {

    await this.getUsers()
    if (parseInt(this.$store.state.client_id) === 0) {
      await this.setClients()
    }
  },
  methods: {
    ...mapActions(["getSystemUsers", "fillUser", "resendOTP", "updateUser", "getClients", "assign"]),
    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.getUsers()
    },
    async getUsers() {
      let vm = this
      vm.isLoading = true;
      this.moreParams.page = vm.offset
      this.moreParams.per_page = vm.limit

      let response = await this.getSystemUsers(this.moreParams)
      if (response.status === 200) {
        this.users = response.message.data
        for (let i = 0; i < vm.users.length; i++) {
          vm.showDropdown.push(false)
        }

        this.total = parseInt(response.message.total)
      }  else {
        vm.$swal.fire('Error!', response.message, 'error')
      }
      vm.isLoading = false;
    },
    async setClients() {
      let app = this
      let payload = { page: 1, per_page: 100 }
      let response = await this.getClients(payload)
      if (response.status === 200){
        response.message.data.forEach(function (item) {
          let list = { label: item.client_name, value: parseInt(item.id) }
          app.clients.push(list)
        })
      } else {
        app.$swal.fire('Error!', response.message, 'error')
      }
    },
    getRoleBg(id){
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 6:
          return 'bg-blue-600'
        case 8:
          return 'bg-purple-600'
        case 9:
          return 'bg-sky-600'
        default:
          return 'bg-teal-600'
      }
    },
    getStatusBg(id){
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-red-600'
        default:
          return 'bg-purple-600'
      }
    },
    showDrop(index){
      let vm = this
      let bool = true
      if (vm.showDropdown[index]){
        bool = false
      }
      vm.showDropdown = []
      for (let i = 0; i < vm.users.length; i++) {
        vm.showDropdown.push(false)
      }
      vm.showDropdown[index] = bool
    },
    async permissionList(list) {
      let vm = this
      this.permissions = list
      this.isOpen = true
      vm.showDropdown = []
      for (let i = 0; i < vm.users.length; i++) {
        vm.showDropdown.push(false)
      }
    },

    async editRow(data) {
      await this.fillUser(data)
      await this.$router.push({name: 'users-edit'})
    },

    async assignClient(data) {
      let app = this

      app.profile_id = data.profile_id

      let acl_list = []
      if (data.client_ids){
        acl_list = JSON.parse(data.client_ids)
      }

      let lists = []
      for (const item of acl_list) {
        let label = await app.getListName(item)
        let list = {label: label, value: item}
        lists.push(list)
      }

      this.client_list = lists
      this.isClient = true
    },

    async getListName(id){
      let label = ''
      id = parseInt(id)
      this.clients.forEach(function (item) {
        if (parseInt(item.value) === id){
          label = item.label
          return item.label
        }
      })
      return label
    },

    async otp(profileId){
      // Use sweetalert2
      let app = this
      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this resends OTP to this user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, send!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.resendOTP(profileId)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.$swal.fire({
                title: 'Reset!',
                text: result.value.message,
                icon: 'success'
              }) .then(async (result) => {
                await app.getUsers()
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async activate(profileId) {
      let app = this

      const payload = { status: 1, id: profileId }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this activates this user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, activate!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateUser(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Activated!', result.value.message, 'success')
              await app.getUsers()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async deactivate(profileId) {
      let app = this

      const payload = { status: 3, id: profileId }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this deactivates this user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, deactivate!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateUser(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Deactivated!', result.value.message, 'success')
              await app.getUsers()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async addClient() {
      let app = this

      let acl = []
      this.client_list.forEach(function (item) {
        acl.push(item.value)
      })
      const payload = { client_ids: acl, profile_id: this.profile_id }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this add clients to this user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.assign(payload)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              await app.getUsers()
              this.isClient = false
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

  },
  filters: {
    formatDate(value) {
      if (!value) return "";
      return moment(value).format("DD MMM YYYY");
    },
    formatAmount: function (value) {
      return numeral(value).format("0,0.00");
    },

  }
}
</script>
