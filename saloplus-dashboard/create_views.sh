#!/bin/bash

# Migration script to copy and convert pages from old app to new app
echo "🚀 Starting migration from saloplus_backoffice to saloplus-dashboard..."

# Create directories if they don't exist
mkdir -p src/views/Loans/Products
mkdir -p src/views/Loans/Accounts
mkdir -p src/views/Loans/Repayments
mkdir -p src/views/System/Roles
mkdir -p src/views/System/Users
mkdir -p src/views/Financial/BillPayments

echo "📁 Created directory structure..."

cat > src/views/Loans/LoanAccounts.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">Loan Accounts</h1>
      <p class="text-gray-600 mt-1">Manage customer loan accounts</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">Loan accounts content goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

cat > src/views/Loans/LoanProducts.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">Loan Products</h1>
      <p class="text-gray-600 mt-1">Configure and manage loan products</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">Loan products content goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

cat > src/views/Loans/LoanRepayments.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">Loan Repayments</h1>
      <p class="text-gray-600 mt-1">Track and manage loan repayments</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">Loan repayments content goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

# Create Financial views
cat > src/views/Financial/Transactions.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">Transactions</h1>
      <p class="text-gray-600 mt-1">View and manage all financial transactions</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">Transactions content goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

cat > src/views/Financial/Withdrawals.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">Withdrawals</h1>
      <p class="text-gray-600 mt-1">Process and manage withdrawal requests</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">Withdrawals content goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

cat > src/views/Financial/BillPayments.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">Bill Payments</h1>
      <p class="text-gray-600 mt-1">Manage bill payment services</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">Bill payments content goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

cat > src/views/Financial/AddBillPayment.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">Add Bill Payment</h1>
      <p class="text-gray-600 mt-1">Create a new bill payment entry</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">Add bill payment form goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

# Create Customer views
cat > src/views/Customers/CustomerSearch.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">Customer Search</h1>
      <p class="text-gray-600 mt-1">Search and find customer information</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">Customer search functionality goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

# Create System views
cat > src/views/System/SystemUsers.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">System Users</h1>
      <p class="text-gray-600 mt-1">Manage system users and their access</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">System users content goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

cat > src/views/System/SystemRoles.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">System Roles</h1>
      <p class="text-gray-600 mt-1">Configure user roles and permissions</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">System roles content goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

cat > src/views/System/SystemPermissions.vue << 'VIEWEOF'
<template>
  <div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900">System Permissions</h1>
      <p class="text-gray-600 mt-1">Manage system permissions and access control</p>
    </div>
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <p class="text-gray-700">System permissions content goes here...</p>
    </div>
  </div>
</template>
VIEWEOF

echo "All view files created successfully!"
