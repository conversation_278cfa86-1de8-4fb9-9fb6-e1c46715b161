# ActionButton Component

A reusable, customizable action button component for the SaloPlus Dashboard with round shape support and default icons.

## Features

- **Round Action Buttons**: Fully rounded buttons perfect for table actions
- **Default Icons**: Built-in icons for common actions with fallback support
- **Custom Icons**: Ability to pass custom icons via slots
- **Multiple Variants**: Pre-configured button styles for different actions
- **Responsive Sizes**: Multiple size options (xs, sm, md, lg)
- **Shape Options**: Round, square, or rounded corners
- **Accessibility**: Built-in tooltip support and proper ARIA attributes

## Usage

### Basic Usage

```vue
<template>
  <!-- Edit Button -->
  <ActionButton
    variant="edit"
    size="sm"
    shape="round"
    :icon-only="true"
    tooltip="Edit Client"
    @click="editItem"
  />
  
  <!-- Delete Button -->
  <ActionButton
    variant="delete"
    size="sm"
    shape="round"
    :icon-only="true"
    tooltip="Delete Client"
    @click="deleteItem"
  />
  
  <!-- Custom Icon Button -->
  <ActionButton
    variant="primary"
    size="md"
    shape="round"
    tooltip="Custom Action"
    @click="customAction"
  >
    <template #icon>
      <CustomIcon class="h-5 w-5" />
    </template>
  </ActionButton>
</template>

<script setup>
import ActionButton from '@/components/ActionButton.vue'
import { CustomIcon } from '@heroicons/vue/24/outline'

const editItem = () => {
  console.log('Edit clicked')
}

const deleteItem = () => {
  console.log('Delete clicked')
}

const customAction = () => {
  console.log('Custom action clicked')
}
</script>
```

### Available Variants

- `primary` - Blue primary button
- `secondary` - Gray secondary button
- `success` - Green success button
- `danger` - Red danger button
- `warning` - Yellow warning button
- `info` - Cyan info button
- `edit` - Blue edit button with pencil icon
- `delete` - Red delete button with trash icon
- `view` - Gray view button with eye icon
- `approve` - Green approve button with check icon
- `reject` - Red reject button with X icon
- `add` - Blue add button with plus icon
- `download` - Indigo download button with download icon
- `settings` - Gray settings button with cog icon
- `users` - Purple users button with users icon
- `transactions` - Green transactions button with money icon

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | String | `'primary'` | Button style variant |
| `size` | String | `'md'` | Button size (xs, sm, md, lg) |
| `shape` | String | `'round'` | Button shape (round, square, rounded) |
| `disabled` | Boolean | `false` | Disable the button |
| `loading` | Boolean | `false` | Show loading state |
| `type` | String | `'button'` | Button type (button, submit, reset) |
| `text` | String | `''` | Button text |
| `tooltip` | String | `''` | Tooltip text |
| `iconOnly` | Boolean | `false` | Hide text and show only icon |

### Events

| Event | Description |
|-------|-------------|
| `click` | Emitted when button is clicked |

### Slots

| Slot | Description |
|------|-------------|
| `default` | Button text content |
| `icon` | Custom icon content |

## Implementation in Tables

The ActionButton component is designed to work seamlessly in data tables:

```vue
<template #actions="{ item, index }">
  <div class="flex items-center space-x-2">
    <!-- Edit Button -->
    <ActionButton
      variant="edit"
      size="sm"
      shape="round"
      :icon-only="true"
      tooltip="Edit Client"
      @click="editClient(item)"
    />
    
    <!-- View Transactions Button -->
    <ActionButton
      variant="transactions"
      size="sm"
      shape="round"
      :icon-only="true"
      tooltip="View Transactions"
      @click="viewTransactions(item)"
    />
    
    <!-- Status Toggle Button -->
    <ActionButton
      :variant="item.status === 'active' ? 'danger' : 'success'"
      size="sm"
      shape="round"
      :icon-only="true"
      :tooltip="item.status === 'active' ? 'Deactivate' : 'Activate'"
      @click="toggleStatus(item)"
    />
  </div>
</template>
```

## Styling

The component uses Tailwind CSS classes and includes:
- Hover effects with scale transform
- Focus states with ring outline
- Smooth transitions
- Proper color contrast
- Responsive design

## Accessibility

- Proper ARIA attributes
- Keyboard navigation support
- Screen reader friendly
- High contrast colors
- Tooltip support for context
