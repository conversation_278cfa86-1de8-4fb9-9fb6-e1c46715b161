import { apiClient } from './apiClient'
import { createHash<PERSON><PERSON> } from '@/utils/hash'
import type { ApiResponse, PaginationParams, PaginatedResponse } from './types'

// System-related interfaces
export interface SystemUser {
  user_id: string
  username: string
  email_address: string
  msisdn: string
  role_id: number
  role_name: string
  permissions: Permission[]
  permissions_list: Permission[]
  status: number
  created_at: string
  last_login?: string
  client_id?: string
  client_name?: string
}

export interface Role {
  role_id: number
  role_name: string
  description?: string
  permissions: Permission[]
  status: number
  created_at: string
  updated_at?: string
  users_count?: number
}

export interface Permission {
  id: number
  name: string
  description?: string
  module?: string
  status: number
  created_at?: string
}

export interface CreateUserPayload {
  username: string
  email_address: string
  msisdn: string
  role_id: number
  permissions: number[]
  client_id?: string
  password?: string
}

export interface UpdateUserPayload {
  user_id: string
  username?: string
  email_address?: string
  msisdn?: string
  role_id?: number
  permissions?: number[]
  status?: number
}

export interface CreateRolePayload {
  role_name: string
  description?: string
  permissions: number[]
}

export interface UpdateRolePayload {
  role_id: number
  role_name?: string
  description?: string
  permissions?: number[]
  status?: number
}

export interface UserFilters {
  role_id?: number
  status?: number
  client_id?: string
  search?: string
}

export interface RoleFilters {
  status?: number
  search?: string
}

/**
 * System API service for users, roles, and permissions
 */
export const systemApi = {
  /**
   * Get system users with pagination and filters
   */
  async getUsers(
    params: PaginationParams & UserFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<SystemUser>>> {
    try {
      const payload = {
        page: params.page || 1,
        per_page: params.limit || 10,
        role_id: params.role_id || '',
        status: params.status || '',
        client_id: params.client_id || '',
        search: params.search || '',
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      // Convert to query string for GET request
      const queryString = new URLSearchParams(
        Object.entries(payload).filter(([_, value]) => value !== '').map(([key, value]) => [key, String(value)])
      ).toString()
      
      // const response = await apiClient.get(`merchant/v1/users?${queryString}`, { 
        const response = await apiClient.get(`merchant/v1/users?${queryString}`, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.per_page
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching users:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Create new system user
   */
  async createUser(userData: CreateUserPayload): Promise<ApiResponse> {
    try {
      const payload = {
        username: userData.username,
        email_address: userData.email_address,
        msisdn: userData.msisdn,
        role_id: userData.role_id,
        permissions: userData.permissions,
        client_id: userData.client_id || '',
        password: userData.password ? btoa(userData.password) : ''
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/user_create', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error creating user:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to create user',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to create user',
        code: '500'
      }
    }
  },

  /**
   * Update system user
   */
  async updateUser(userData: UpdateUserPayload): Promise<ApiResponse> {
    try {
      const payload = {
        user_id: userData.user_id,
        username: userData.username,
        email_address: userData.email_address,
        msisdn: userData.msisdn,
        role_id: userData.role_id,
        permissions: userData.permissions,
        status: userData.status
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/user_update', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating user:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update user',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to update user',
        code: '500'
      }
    }
  },

  /**
   * Delete system user
   */
  async deleteUser(userId: string): Promise<ApiResponse> {
    try {
      const payload = { user_id: userId }
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/user_delete', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error deleting user:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to delete user',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to delete user',
        code: '500'
      }
    }
  },

  /**
   * Get system roles with pagination and filters
   */
  async getRoles(
    params: PaginationParams & RoleFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<Role>>> {
    try {
      const payload = {
        page: params.page || 1,
        per_page: params.limit || 10,
        status: params.status || '',
        search: params.search || '',
        ...params
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/user_roles', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data?.data?.data || [],
          total_count: responseData.data?.data?.total || 0,
          current_page: responseData.data?.data?.current_page || 1,
          per_page: responseData.data?.data?.per_page || payload.per_page
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching roles:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Create new role
   */
  async createRole(roleData: CreateRolePayload): Promise<ApiResponse> {
    try {
      const payload = {
        role_name: roleData.role_name,
        description: roleData.description || '',
        permissions: roleData.permissions
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/role/create', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error creating role:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to create role',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to create role',
        code: '500'
      }
    }
  },

  /**
   * Update role
   */
  async updateRole(roleData: UpdateRolePayload): Promise<ApiResponse> {
    try {
      const payload = {
        role_id: roleData.role_id,
        role_name: roleData.role_name,
        description: roleData.description,
        permissions: roleData.permissions,
        status: roleData.status
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/role/update', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating role:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update role',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to update role',
        code: '500'
      }
    }
  },

  /**
   * Delete role
   */
  async deleteRole(roleId: number): Promise<ApiResponse> {
    try {
      const payload = { role_id: roleId }
      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/role/delete', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error deleting role:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to delete role',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to delete role',
        code: '500'
      }
    }
  },

  /**
   * Get system permissions
   */
  async getPermissions(
    params: PaginationParams = {}
  ): Promise<ApiResponse<PaginatedResponse<Permission>>> {
    try {
      const payload = {
        page: params.page || 1,
        per_page: params.limit || 100
      }

      const hash = createHashKey(payload)

      // Convert to query string for GET request
      const queryString = new URLSearchParams(payload as any).toString()

      const response = await apiClient.get(`merchant/v1/permission/view?${queryString}`, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      // Handle response structure from API
      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data?.data || [],
          total_count: responseData.data?.total || 0,
          current_page: responseData.data?.current_page || 1,
          per_page: responseData.data?.per_page || payload.per_page
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching permissions:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 100
          },
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 100
        },
        code: '500'
      }
    }
  },

  /**
   * Assign client to user
   */
  async assignClient(userId: string, clientId: string): Promise<ApiResponse> {
    try {
      const payload = {
        user_id: userId,
        client_id: clientId
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/user_assign_client', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error assigning client:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to assign client',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to assign client',
        code: '500'
      }
    }
  },

  /**
   * Resend OTP to user
   */
  async resendOTP(userId: string): Promise<ApiResponse> {
    try {
      const payload = { user_id: userId }
      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/user_resend_otp', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error resending OTP:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to resend OTP',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Failed to resend OTP',
        code: '500'
      }
    }
  }
}
