import { apiClient } from './apiClient'
import { createHash<PERSON>ey } from '@/utils/hash'
import type { ApiResponse, PaginationParams, PaginatedResponse } from './types'

// Bill Payment interfaces
export interface BillPayment {
  id: string
  bill_name: string
  utility_name: string
  utility_desc: string
  utility_icon: string
  status: number
  created_at: string
  updated_at?: string
}

export interface BillPaymentTransaction {
  id: string
  reference_number: string
  receipt_number: string
  customer: string
  narration: string
  amount: number
  discount: number
  currency_code: string
  status: number
  created: string
  utility_id: string
  loan_number?: string
  client_id?: string
  client_phone?: string
}

export interface CreateBillPaymentPayload {
  utility_name: string
  utility_desc: string
  utility_icon?: string
  user_name?: string
  user_pass?: string
  api_key?: string
}

export interface BillPaymentFilters {
  start?: string
  end?: string
  utility_id?: string
  utility_name?: string
  status?: string
  reference_number?: string
  receipt_number?: string
  client_phone?: string
  loan_number?: string
  client_id?: string
}

/**
 * Bill Payments API service
 */
export const billPaymentsApi = {
  /**
   * Get bill payment utilities with pagination and filters
   */
  async getBillPayments(
    params: PaginationParams & BillPaymentFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<BillPayment>>> {
    try {
      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 1,
        page: params.page || 1,
        start: params.start || '',
        end: params.end || '',
        utility_id: params.utility_id || '',
        utility_name: params.utility_name || '',
        sort: '',
        export: '',
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('bill_payment/v1/view/utilities', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching bill payments:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get bill payment transactions with pagination and filters
   */
  async getBillPaymentTransactions(
    params: PaginationParams & BillPaymentFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<BillPaymentTransaction>>> {
    try {
      const payload = {
        offset: params.offset || 1,
        limit: params.limit || 10,
        start: params.start || '',
        end: params.end || '',
        sort: '',
        export: '',
        utility_id: params.utility_id || '',
        loan_number: params.loan_number || '',
        client_id: params.client_id || '',
        status: params.status || '',
        reference_number: params.reference_number || '',
        receipt_number: params.receipt_number || '',
        client_phone: params.client_phone || '',
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('bill_payment/v1/view/trxn', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching bill payment transactions:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Create new bill payment utility
   */
  async createBillPayment(billPaymentData: CreateBillPaymentPayload): Promise<ApiResponse> {
    try {
      const payload = {
        utility_name: billPaymentData.utility_name,
        utility_desc: billPaymentData.utility_desc,
        utility_icon: billPaymentData.utility_icon || '',
        user_name: billPaymentData.user_name || '',
        user_pass: billPaymentData.user_pass || '',
        api_key: billPaymentData.api_key || ''
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('bill_payment/v1/create', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error creating bill payment:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to create bill payment',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to create bill payment',
        code: '500'
      }
    }
  },

  /**
   * Update bill payment utility
   */
  async updateBillPayment(
    billPaymentId: string,
    billPaymentData: Partial<CreateBillPaymentPayload>
  ): Promise<ApiResponse> {
    try {
      const payload = {
        id: billPaymentId,
        ...billPaymentData
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('bill_payment/v1/update', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating bill payment:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update bill payment',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to update bill payment',
        code: '500'
      }
    }
  },

  /**
   * Delete bill payment utility
   */
  async deleteBillPayment(billPaymentId: string): Promise<ApiResponse> {
    try {
      const payload = { id: billPaymentId }
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('bill_payment/v1/delete', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error deleting bill payment:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to delete bill payment',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to delete bill payment',
        code: '500'
      }
    }
  },

  /**
   * Get withdrawals with pagination and filters
   */
  async getWithdrawals(
    params: PaginationParams & BillPaymentFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    try {
      const payload = {
        offset: params.offset || 1,
        limit: params.limit || 10,
        start: params.start || '',
        end: params.end || '',
        sort: '',
        export: '',
        amount: '',
        reference_number: params.reference_number || '',
        withdrawal_status: params.status || '',
        receipt_number: params.receipt_number || '',
        loan_number: params.loan_number || '',
        client_id: params.client_id || '',
        client_phone: params.client_phone || '',
        ...params
      }

      // Create hash for the request
      const hash = createHashKey(payload)

      const response = await apiClient.post('bill_payment/v1/view/withdrawals', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      // Handle response structure from API
      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching withdrawals:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get transactions with pagination and filters
   */
  async getTransactions(
    params: PaginationParams & {
      start?: string
      end?: string
      source?: string
      amount?: string
      client_id?: string
      loan_number?: string
      client_phone?: string
      client_email?: string
      channel_name?: string
      reference_type_id?: string
      trxn_reference_id?: string
    } = {}
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    try {
      const payload = {
        offset: params.offset || 1,
        limit: params.limit || 10,
        sort: '',
        export: '',
        start: params.start || '',
        end: params.end || '',
        source: params.source || '',
        amount: params.amount || '',
        client_id: params.client_id || '',
        loan_number: params.loan_number || '',
        client_phone: params.client_phone || '',
        client_email: params.client_email || '',
        channel_name: params.channel_name || '',
        reference_type_id: params.reference_type_id || '',
        trxn_reference_id: params.trxn_reference_id || '',
        ...params
      }

      // Create hash for the request
      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/transactions', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      // Handle response structure from API
      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching transactions:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  }
}
