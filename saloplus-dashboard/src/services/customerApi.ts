import { apiClient } from './api'
import { createHash<PERSON><PERSON> } from './utils'
import type { ApiResponse } from './types'

export interface CustomerSearchParams {
  loan_number: string
  limit?: number
  offset?: number
}

export interface LoanRequestParams {
  loan_number: string
  limit?: number
  offset?: number
  loan_request_number?: string
}

export interface LoanRepaymentParams {
  loan_number: string
  limit?: number
  offset?: number
  request_number?: string
}

export interface LimitRequestParams {
  loan_number: string
  limit?: number
  offset?: number
  reference_id?: string
}

export interface TransactionParams {
  loan_number: string
  limit?: number
  offset?: number
  trxn_id?: string
}

export const customerApi = {
  /**
   * Search for a customer by loan number/phone
   */
  async searchCustomer(params: CustomerSearchParams): Promise<ApiResponse> {
    try {
      const payload = {
        loan_number: params.loan_number,
        limit: params.limit || 10,
        offset: params.offset || 1
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/search/loan_accounts', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error searching customer:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to search customer',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get loan requests for a customer
   */
  async getLoanRequests(params: LoanRequestParams): Promise<ApiResponse> {
    try {
      const payload = {
        loan_number: params.loan_number,
        limit: params.limit || 10,
        offset: params.offset || 1,
        loan_request_number: params.loan_request_number || ''
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/loan_request', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching loan requests:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch loan requests',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get loan repayments for a customer
   */
  async getLoanRepayments(params: LoanRepaymentParams): Promise<ApiResponse> {
    try {
      const payload = {
        loan_number: params.loan_number,
        limit: params.limit || 10,
        offset: params.offset || 1,
        request_number: params.request_number || ''
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/loan_repayments', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching loan repayments:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch loan repayments',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get limit requests for a customer
   */
  async getLimitRequests(params: LimitRequestParams): Promise<ApiResponse> {
    try {
      const payload = {
        loan_number: params.loan_number,
        limit: params.limit || 10,
        offset: params.offset || 1,
        reference_id: params.reference_id || ''
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/loan_limits', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching limit requests:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch limit requests',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get transactions for a customer
   */
  async getTransactions(params: TransactionParams): Promise<ApiResponse> {
    try {
      const payload = {
        loan_number: params.loan_number,
        limit: params.limit || 10,
        offset: params.offset || 1,
        trxn_id: params.trxn_id || ''
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/transactions', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching transactions:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch transactions',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Toggle customer bulk marketing status
   */
  async toggleBulkStatus(loanNumber: string, enable: boolean): Promise<ApiResponse> {
    try {
      const payload = {
        loan_number: loanNumber,
        marketing_status: enable ? '1' : '0'
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/update/marketing_status', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error toggling bulk status:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update bulk status',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Block/Unblock customer
   */
  async toggleBlockStatus(loanNumber: string, block: boolean, reason?: string): Promise<ApiResponse> {
    try {
      const payload = {
        loan_number: loanNumber,
        block_status: block ? '1' : '0',
        reason: reason || ''
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/update/block_status', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error toggling block status:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update block status',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  }
}
