/**
 * Common API types and interfaces
 */

// Base API Response Interface
export interface ApiResponse<T = any> {
  status: number
  message: T
  code?: string
}

// Pagination Interface
export interface PaginationParams {
  page?: number
  limit?: number
  offset?: number
  search?: string
  sortField?: string
  sortDirection?: 'asc' | 'desc'
}

// Pagination Response
export interface PaginatedResponse<T> {
  data: T[]
  total_count: number
  current_page: number
  per_page: number
  last_page?: number
  from?: number
  to?: number
}

// User Types
export interface User {
  id: number
  username: string
  email?: string
  role_id: string
  permissions: Permission[]
  token: string
  mc?: number
  clients?: Client[]
  created_at?: string
  updated_at?: string
}

export interface Permission {
  id: number
  name: string
  description?: string
  module?: string
}

export interface Role {
  id: number
  name: string
  description?: string
  permissions: Permission[]
}

export interface Client {
  client_id: string
  client_name: string
  client_account: string
  client_status?: number
  created_at?: string
}

// Organization Types
export interface Organization {
  client_id: string
  client_account: string
  client_name: string
  client_phone: string
  client_email: string
  client_address?: string
  client_status: number
  currency_code: string
  total_loan_assets: number
  can_issue_loans: string
  can_issue_loans_desc: string
  service_fee: number
  open_date: string
  close_date: string
  b2c_paybill: string
  c2b_paybill: string
  created: string
  updated?: string
}

// Client Types (updated to match new API response structure)
export interface ClientEntity {
  total_count: string
  client_id: string
  client_account: string
  client_name: string
  service_fee: string
  open_date: string
  close_date: string
  total_loan_assets: string
  currency_code: string
  can_issue_loans: string
  can_issue_loans_desc: string
  client_email: string
  client_address: string
  client_phone: string
  b2b_paybill: string
  sender_id: string
  client_status: string
  activated_on?: string | null
  deactivated_on?: string | null
  created: string
  b2c_paybill: string
  c2b_paybill: string
}

// Merchant Types (similar to Organization but for merchant management)
export interface Merchant {
  client_id: string
  client_account: string
  client_name: string
  client_phone: string
  client_email: string
  client_address?: string
  client_status: number
  currency_code: string
  total_loan_assets: number
  can_issue_loans: string
  can_issue_loans_desc: string
  service_fee: number
  open_date: string
  close_date: string
  b2c_paybill: string
  c2b_paybill: string
  created: string
  updated?: string
}

// Loan Types
export interface LoanRequest {
  id: string
  loan_number: string
  client_account: string
  customer_phone: string
  customer_name: string
  loan_amount: number
  loan_period: number
  interest_rate: number
  status: string
  created_at: string
  updated_at?: string
}

export interface LoanProduct {
  client_product_id: string
  product_name: string
  repayment_period: number
  interest_rate: number
  late_fine_interest_rate: number
  minimum_loan_amount: number
  maximum_loan_amount: number
  currency_code: string
  status: number
  client_id: string
  created_at?: string
  updated_at?: string
}

export interface LoanAccount {
  id: string
  account_number: string
  client_account: string
  customer_phone: string
  customer_name: string
  loan_balance: number
  total_paid: number
  status: string
  created_at: string
}

export interface LoanRepayment {
  repayment_id: string
  loan_number: string
  request_number: string
  amount: number
  payment_method: string
  reference: string
  status: number
  payment_date: string
  created_at: string
  customer_phone?: string
  customer_name?: string
}

export interface LoanLimit {
  id: string
  reference_id: string
  customer_phone: string
  customer_name: string
  requested_limit: number
  approved_limit: number
  status: number
  created_at: string
}

export interface LoanProduct {
  id: string
  product_name: string
  client_account: string
  min_amount: number
  max_amount: number
  interest_rate: number
  min_period: number
  max_period: number
  status: number
  created_at: string
}

// Transaction Types
export interface Transaction {
  id: string
  transaction_id: string
  client_account: string
  customer_phone: string
  transaction_type: string
  amount: number
  balance_before: number
  balance_after: number
  description: string
  status: string
  created_at: string
}

// System User Types
export interface SystemUser {
  id: number
  username: string
  email: string
  role_id: number
  role_name: string
  client_account?: string
  status: number
  last_login?: string
  created_at: string
}

// Customer Types
export interface Customer {
  id: string
  phone: string
  name: string
  email?: string
  id_number?: string
  client_account: string
  status: number
  loan_limit: number
  current_balance: number
  created_at: string
}

// Dashboard Stats Types
export interface DashboardStats {
  total_organizations: number
  active_organizations: number
  total_customers: number
  total_loans: number
  total_loan_amount: number
  pending_requests: number
  active_loans: number
  overdue_loans: number
}

// Error Types
export interface ApiError {
  status: number
  message: string
  code?: string
  details?: any
}

// File Upload Types
export interface FileUploadResponse {
  file_url: string
  file_name: string
  file_size: number
  file_type: string
}

// Bulk SMS Types
export interface BulkSMSRequest {
  client_account: string
  message: string
  recipients: string[]
  sender_id?: string
}

export interface BulkSMSResponse {
  message_id: string
  status: string
  total_recipients: number
  successful_sends: number
  failed_sends: number
}

// Report Types
export interface ReportRequest {
  client_account?: string
  start_date: string
  end_date: string
  report_type: string
  format?: 'pdf' | 'excel' | 'csv'
}

export interface ReportResponse {
  report_url: string
  report_name: string
  generated_at: string
  expires_at: string
}

// Configuration Types
export interface SystemConfig {
  key: string
  value: string
  description?: string
  type: 'string' | 'number' | 'boolean' | 'json'
  client_account?: string
}

// Notification Types
export interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  created_at: string
}

// Search Types
export interface SearchParams {
  query: string
  type?: 'customer' | 'organization' | 'transaction' | 'loan'
  filters?: Record<string, any>
}

export interface SearchResult<T = any> {
  type: string
  data: T
  score: number
}

// Export all types
export type {
  // Re-export for convenience
  ApiResponse as Response,
  PaginationParams as Pagination,
  PaginatedResponse as PaginatedData
}
