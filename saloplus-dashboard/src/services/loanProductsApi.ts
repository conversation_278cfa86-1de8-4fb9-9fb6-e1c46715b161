import { apiClient } from './apiClient'
import { createHash<PERSON><PERSON> } from '@/utils/hash'
import type { ApiResponse, PaginationParams, PaginatedResponse } from './types'

// Types
export interface LoanProduct {
  client_product_id: string
  product_name: string
  repayment_period: number
  interest_rate: number
  late_fine_interest_rate: number
  minimum_loan_amount: number
  maximum_loan_amount: number
  currency_code: string
  status: number
  client_id: string
  created_at?: string
  updated_at?: string
}

/**
 * Loan Products API service
 */
export const loanProductsApi = {
  /**
   * Get loan products list with pagination
   */
  async getLoanProducts(params: PaginationParams & { client_id?: string } = {}): Promise<ApiResponse<PaginatedResponse<LoanProduct>>> {
    try {
      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 0,
        page: params.page || 1,
        client_id: params.client_id,
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/loan_products', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from old app
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching loan products:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Add new loan product
   */
  async addLoanProduct(productData: Partial<LoanProduct>): Promise<ApiResponse> {
    try {
      const payload = {
        product_name: productData.product_name,
        repayment_period: productData.repayment_period,
        interest_rate: productData.interest_rate,
        late_fine_interest_rate: productData.late_fine_interest_rate,
        minimum_loan_amount: productData.minimum_loan_amount,
        maximum_loan_amount: productData.maximum_loan_amount,
        client_id: productData.client_id
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/create_loan_product', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error adding loan product:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to add loan product',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Update loan product
   */
  async updateLoanProduct(productData: Partial<LoanProduct>): Promise<ApiResponse> {
    try {
      const payload = {
        client_product_id: productData.client_product_id,
        product_name: productData.product_name,
        repayment_period: productData.repayment_period,
        interest_rate: productData.interest_rate,
        late_fine_interest_rate: productData.late_fine_interest_rate,
        minimum_loan_amount: productData.minimum_loan_amount,
        maximum_loan_amount: productData.maximum_loan_amount,
        status: productData.status
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/update_loan_product', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating loan product:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update loan product',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Delete loan product
   */
  async deleteLoanProduct(productId: string): Promise<ApiResponse> {
    try {
      const payload = {
        client_product_id: productId
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/delete_loan_product', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error deleting loan product:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to delete loan product',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get loan product by ID
   */
  async getLoanProduct(productId: string): Promise<ApiResponse<LoanProduct>> {
    try {
      const payload = {
        client_product_id: productId
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/loan_product', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching loan product:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch loan product',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  }
}
