import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios'
import { getApp<PERSON><PERSON>, getA<PERSON><PERSON><PERSON>, getWebA<PERSON><PERSON><PERSON> } from '@/utils/hash'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_BASE_API || 'https://salo.app.codev.life/'

/**
 * Create axios instance with default configuration
 */
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
})

/**
 * Request interceptor to add authentication headers
 */
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // Get authentication data from localStorage
    const token = localStorage.getItem('token')
    const selectedClientId = localStorage.getItem('selectedClientId')
    const clientMode = localStorage.getItem('clientMode')

    // Add standard headers
    config.headers = config.headers || {}
    config.headers['X-App-Key'] = getAppKey()
    config.headers['X-Authorization'] = getAuthKey()
    // Removed X-Web-Auth header to resolve CORS issues

    // Add token if available
    if (token) {
      config.headers['X-Access-Token'] = token
    }

    // Add client context if available
    if (selectedClientId) {
      config.headers['X-Client-ID'] = selectedClientId
    }

    if (clientMode) {
      config.headers['X-Client-Mode'] = clientMode
    }

    // Enhanced logging for debugging network issues
    if (import.meta.env.DEV) {
      console.group(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`)
      console.log('Base URL:', config.baseURL)
      console.log('Full URL:', `${config.baseURL}${config.url}`)
      console.log('Headers:', config.headers)
      console.log('Data:', config.data)
      console.groupEnd()
    }

    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

/**
 * Response interceptor for error handling and token management
 */
apiClient.interceptors.response.use(
  (response) => {
    // Enhanced logging for debugging network issues
    if (import.meta.env.DEV) {
      console.group(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`)
      console.log('Status:', response.status)
      console.log('Headers:', response.headers)
      console.log('Data:', response.data)
      console.groupEnd()
    }

    return response
  },
  (error) => {
    // Enhanced error logging for debugging network issues
    if (import.meta.env.DEV) {
      console.group(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`)
      console.error('Status:', error.response?.status)
      console.error('Error Message:', error.message)
      console.error('Response Data:', error.response?.data)
      console.error('Request Config:', error.config)
      console.groupEnd()
    }
    
    // Handle different error scenarios
    if (error.response) {
      const status = error.response.status
      
      switch (status) {
        case 401:
          // Unauthorized - clear auth data and redirect to login
          handleUnauthorized()
          break
          
        case 403:
          // Forbidden - user doesn't have permission
          console.warn('Access forbidden - insufficient permissions')
          break
          
        case 422:
          // Validation error
          console.warn('Validation error:', error.response.data)
          break
          
        case 429:
          // Rate limiting
          console.warn('Rate limit exceeded')
          break
          
        case 500:
          // Server error
          console.error('Server error:', error.response.data)
          break
          
        case 503:
          // Service unavailable
          console.error('Service unavailable')
          break
          
        default:
          console.error('API error:', status, error.response.data)
      }
    } else if (error.request) {
      // Network error
      console.error('Network error - no response received:', error.request)
    } else {
      // Request setup error
      console.error('Request setup error:', error.message)
    }
    
    return Promise.reject(error)
  }
)

/**
 * Handle unauthorized access
 */
const handleUnauthorized = async () => {
  console.warn('Unauthorized access detected. Logging out...')

  try {
    // Try to use auth store for proper logout
    const { useAuthStore } = await import('@/stores/auth')
    const authStore = useAuthStore()
    await authStore.logout()
  } catch (authError) {
    console.error('Failed to logout via auth store:', authError)

    // Fallback: Clear all auth-related data manually
    const authKeys = [
      'token',
      'user',
      'permissions',
      'role',
      'selectedClientId',
      'clientList',
      'clientMode'
    ]

    authKeys.forEach(key => localStorage.removeItem(key))

    // Redirect to login page
    if (typeof window !== 'undefined') {
      // Check if we're not already on the login page to avoid infinite redirects
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login'
      }
    }
  }
}

/**
 * Create a new API client instance with custom configuration
 */
export const createApiClient = (config: Partial<AxiosRequestConfig> = {}): AxiosInstance => {
  return axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
    ...config
  })
}

/**
 * API client for file uploads
 */
export const fileApiClient: AxiosInstance = createApiClient({
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  timeout: 60000 // Longer timeout for file uploads
})

// Add the same interceptors to file upload client
fileApiClient.interceptors.request.use(apiClient.interceptors.request.handlers[0].fulfilled)
fileApiClient.interceptors.response.use(
  apiClient.interceptors.response.handlers[0].fulfilled,
  apiClient.interceptors.response.handlers[0].rejected
)

/**
 * Health check endpoint
 */
export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await apiClient.get('/health')
    return response.status === 200
  } catch (error) {
    console.error('Health check failed:', error)
    return false
  }
}

/**
 * Get API base URL
 */
export const getApiBaseUrl = (): string => {
  return API_BASE_URL
}

/**
 * Check if API is available
 */
export const isApiAvailable = async (): Promise<boolean> => {
  try {
    const response = await fetch(API_BASE_URL, { 
      method: 'HEAD',
      mode: 'no-cors'
    })
    return true
  } catch (error) {
    console.error('API availability check failed:', error)
    return false
  }
}

export default apiClient
