import { apiClient } from './apiClient'
import { createHash<PERSON><PERSON> } from '@/utils/hash'
import type { ApiResponse, PaginationParams, PaginatedResponse, LoanRepayment, LoanLimit } from './types'

// Loan-related interfaces
export interface LoanRequest {
  req_number: string
  mobile: string
  name: string
  merchant_name: string
  product_name: string
  interest_charged: number
  requested_amount: number
  approved_amount: number
  status: number
  created_at: string
  client_ac: string
}

export interface LoanLimit {
  reference_id: string
  client_ac: string
  current_limit: number
  requested_limit: number
  status: number
  created_at: string
  merchant_name: string
  employee_number: string
}

export interface LoanAccount {
  user_id: string
  loan_number: string
  first_name: string
  last_name: string
  email_address: string
  msisdn: string
  network: string
  nationality: string
  national_id: string
  gender: string
  dob: string
  max_approved_loan_amount: number
  actual_balance: number
  loan_balance: number
  status: number
  black_list_state: number
  created: string
}

export interface LoanRepayment {
  repayment_id: string
  loan_number: string
  request_number: string
  amount: number
  payment_date: string
  status: number
  payment_method: string
  reference: string
}

export interface LoanProduct {
  client_product_id: string
  product_name: string
  repayment_period: number
  interest_rate: number
  late_fine_interest_rate: number
  minimum_loan_amount: number
  maximum_loan_amount: number
  currency_code: string
  status: number
  created_at?: string
}

// Filter interfaces
export interface LoanRequestFilters {
  client_id?: string
  status?: string
  loan_number?: string
  reference_id?: string
  phone_number?: string
  start_date?: string
  end_date?: string
  amount?: string
}

export interface LoanLimitFilters {
  client_id?: string
  status?: string
  reference_id?: string
  start_date?: string
  end_date?: string
}

export interface LoanAccountFilters {
  client_id?: string
  status?: string
  loan_number?: string
  msisdn?: string
  national_id?: string
  start_date?: string
  end_date?: string
}

export interface LoanRepaymentFilters {
  client_id?: string
  loan_number?: string
  request_number?: string
  start_date?: string
  end_date?: string
  payment_method?: string
}

/**
 * Loan API service
 */
export const loanApi = {
  /**
   * Get loan requests with pagination and filters
   */
  async getLoanRequests(
    params: PaginationParams & LoanRequestFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<LoanRequest>>> {
    try {
      // Get default client_id from localStorage if not provided
      const defaultClientId = localStorage.getItem('selectedClientId')
      
      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 1,
        page: params.page || 1,
        client_id: params.client_id || defaultClientId,
        status: params.status || '',
        loan_number: params.loan_number || '',
        reference_id: params.reference_id || '',
        phone_number: params.phone_number || '',
        start_date: params.start_date || '',
        end_date: params.end_date || '',
        amount: params.amount || '',
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/loan_request', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching loan requests:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Approve or reject a loan request
   */
  async approveLoanRequest(
    requestId: string,
    status: number,
    approvedAmount: number,
    statusDescription: string
  ): Promise<ApiResponse> {
    try {
      const payload = {
        id: requestId,
        status: status,
        approved_amount: approvedAmount,
        status_description: statusDescription
      }

      // Create hash for the request
      const hash = createHashKey(payload)

      const response = await apiClient.post(`merchant/v1/approve_loan/${requestId}`, payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      return {
        status: response.data.data.code === 200 ? 200 : response.data.data.code,
        message: response.data.data.message || 'Loan request processed successfully',
        code: response.data.data.code.toString()
      }
    } catch (error: any) {
      console.error('Error processing loan request:', error)

      if (error.response?.status === 401) {
        throw new Error('Unauthorized access')
      }

      return {
        status: error.response?.status || 500,
        message: error.response?.data?.data?.message || 'Failed to process loan request',
        code: (error.response?.status || 500).toString()
      }
    }
  },

  /**
   * Resend TAN for loan request
   */
  async resendTAN(requestNumber: string): Promise<ApiResponse> {
    try {
      const payload = {
        request_number: requestNumber
      }

      // Create hash for the request
      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/resend_tan', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      return {
        status: response.data.data.code === 200 ? 200 : response.data.data.code,
        message: response.data.data.message || 'TAN sent successfully',
        code: response.data.data.code.toString()
      }
    } catch (error: any) {
      console.error('Error resending TAN:', error)

      return {
        status: error.response?.status || 500,
        message: error.response?.data?.data?.message || 'Failed to resend TAN',
        code: (error.response?.status || 500).toString()
      }
    }
  },

  /**
   * Approve or reject a loan limit request
   */
  async approveLoanLimit(
    referenceId: string,
    status: number,
    narration: string
  ): Promise<ApiResponse> {
    try {
      const payload = {
        reference_id: referenceId,
        status: status,
        narration: narration
      }

      // Create hash for the request
      const hash = createHashKey(payload)

      const response = await apiClient.post(`merchant/v1/approve_limits/${referenceId}`, payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      return {
        status: response.data.data.code === 200 ? 200 : response.data.data.code,
        message: response.data.data.message || 'Loan limit processed successfully',
        code: response.data.data.code.toString()
      }
    } catch (error: any) {
      console.error('Error processing loan limit:', error)

      if (error.response?.status === 401) {
        throw new Error('Unauthorized access')
      }

      return {
        status: error.response?.status || 500,
        message: error.response?.data?.data?.message || 'Failed to process loan limit',
        code: (error.response?.status || 500).toString()
      }
    }
  },

  /**
   * Get loan limits with pagination and filters
   */
  async getLoanLimits(
    params: PaginationParams & LoanLimitFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<LoanLimit>>> {
    try {
      // Get default client_id from localStorage if not provided
      const defaultClientId = localStorage.getItem('selectedClientId')
      
      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 1,
        page: params.page || 1,
        client_id: params.client_id || defaultClientId,
        status: params.status || '',
        reference_id: params.reference_id || '',
        start_date: params.start_date || '',
        end_date: params.end_date || '',
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/loan_limits', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching loan limits:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get loan accounts with pagination and filters
   */
  async getLoanAccounts(
    params: PaginationParams & LoanAccountFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<LoanAccount>>> {
    try {
      // Get default client_id from localStorage if not provided
      const defaultClientId = localStorage.getItem('selectedClientId')
      
      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 1,
        page: params.page || 1,
        client_id: params.client_id || defaultClientId,
        status: params.status || '',
        loan_number: params.loan_number || '',
        msisdn: params.msisdn || '',
        national_id: params.national_id || '',
        start_date: params.start_date || '',
        end_date: params.end_date || '',
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/loan_accounts', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching loan accounts:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get loan repayments with pagination and filters
   */
  async getLoanRepayments(
    params: PaginationParams & LoanRepaymentFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<LoanRepayment>>> {
    try {
      // Get default client_id from localStorage if not provided
      const defaultClientId = localStorage.getItem('selectedClientId')

      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 1,
        page: params.page || 1,
        client_id: params.client_id || defaultClientId,
        loan_number: params.loan_number || '',
        request_number: params.request_number || '',
        start_date: params.start_date || '',
        end_date: params.end_date || '',
        payment_method: params.payment_method || '',
        ...params
      }

      // Create hash for the request
      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/loan_repayments', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      // Handle response structure from API
      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching loan repayments:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get loan products with pagination and filters
   */
  async getLoanProducts(
    params: PaginationParams & { client_id?: string } = {}
  ): Promise<ApiResponse<PaginatedResponse<LoanProduct>>> {
    try {
      // Get default client_id from localStorage if not provided
      const defaultClientId = localStorage.getItem('selectedClientId')

      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 1,
        page: params.page || 1,
        client_id: params.client_id || defaultClientId,
        ...params
      }

      // Create hash for the request
      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/loan_products', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      // Handle response structure from API
      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching loan products:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get check-off reports with pagination and filters
   */
  async getCheckOffReports(
    params: PaginationParams & { client_id?: string; type_id?: string; start_date?: string; end_date?: string } = {}
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    try {
      // Get default client_id from localStorage if not provided
      const defaultClientId = localStorage.getItem('selectedClientId')

      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 1,
        page: params.page || 1,
        client_id: params.client_id || defaultClientId,
        type_id: params.type_id || '',
        start: params.start_date || '',
        end: params.end_date || '',
        ...params
      }

      // Create hash for the request
      const hash = createHashKey(payload)

      const response = await apiClient.post('checkoff/v1/report', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      // Handle response structure from API
      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching check-off reports:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  }
}
