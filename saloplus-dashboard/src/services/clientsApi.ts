import { apiClient } from './apiClient'
import { createHashKey } from '@/utils/hash'
import type { ApiResponse, PaginationParams, ClientEntity, PaginatedResponse } from './types'

/**
 * Clients API service
 */
export const clientsApi = {
  /**
   * Get clients list with pagination
   */
  async getClients(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<ClientEntity>>> {
    try {
      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 0,
        page: params.page || 1,
        status: "1",
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/companies', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data

      // Handle nested data structure - the actual data is in responseData.data.data
      const actualData = responseData.data?.data || responseData.data || []
      const actualTotalCount = responseData.data?.total_count || actualData.length || 0

      // Transform the response to match our expected format
      const transformedData = {
        data: actualData,
        total_count: parseInt(actualTotalCount.toString()) || 0,
        current_page: responseData.current_page || 1,
        per_page: responseData.per_page || 10,
        last_page: responseData.last_page || 1,
        from: responseData.from || 0,
        to: responseData.to || 0
      }

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: transformedData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Clients API error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 422 || status === 500) {
          return {
            status: status,
            message: { data: [], total_count: 0, current_page: 1, per_page: 10 },
            code: status.toString()
          }
        } else if (data.data) {
          return {
            status: data.data.code || status,
            message: { data: [], total_count: 0, current_page: 1, per_page: 10 },
            code: (data.data.code || status).toString()
          }
        }
      }
      
      return {
        status: 500,
        message: { data: [], total_count: 0, current_page: 1, per_page: 10 },
        code: '500'
      }
    }
  },



  /**
   * Get a single client by ID
   */
  async getClient(clientId: string): Promise<ApiResponse<ClientEntity>> {
    try {
      const payload = {
        client_id: clientId
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/client', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching client:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch client',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: {} as ClientEntity,
        code: '500'
      }
    }
  },

  /**
   * Add a new client
   */
  async addClient(clientData: Partial<ClientEntity>): Promise<ApiResponse> {
    try {
      const payload = {
        client_name: clientData.client_name,
        client_email: clientData.client_email,
        client_phone: clientData.client_phone,
        client_address: clientData.client_address,
        can_issue_loans: clientData.can_issue_loans,
        service_fee: clientData.service_fee,
        currency_code: clientData.currency_code,
        open_date: clientData.open_date,
        close_date: clientData.close_date,
        b2c_paybill: clientData.b2c_paybill,
        c2b_paybill: clientData.c2b_paybill,
        sender_id: clientData.sender_id
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/create_client', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error adding client:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to add client',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Update an existing client
   */
  async updateClient(clientData: Partial<ClientEntity>): Promise<ApiResponse> {
    try {
      const payload = {
        client_id: clientData.client_id,
        client_name: clientData.client_name,
        client_email: clientData.client_email,
        client_phone: clientData.client_phone,
        client_address: clientData.client_address,
        can_issue_loans: clientData.can_issue_loans,
        service_fee: clientData.service_fee,
        currency_code: clientData.currency_code,
        open_date: clientData.open_date,
        close_date: clientData.close_date,
        b2c_paybill: clientData.b2c_paybill,
        c2b_paybill: clientData.c2b_paybill,
        sender_id: clientData.sender_id
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/update_client', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating client:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update client',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Activate a client
   */
  async activateClient(clientId: string): Promise<ApiResponse> {
    try {
      const payload = {
        client_id: clientId,
        action: 'activate'
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/client_status', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error activating client:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to activate client',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Deactivate a client
   */
  async deactivateClient(clientId: string): Promise<ApiResponse> {
    try {
      const payload = {
        client_id: clientId,
        action: 'deactivate'
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/client_status', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error deactivating client:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to deactivate client',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Delete a client
   */
  async deleteClient(clientId: string): Promise<ApiResponse> {
    try {
      const payload = {
        client_id: clientId
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/delete_client', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error deleting client:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to delete client',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get client details for editing
   */
  async getClientForEdit(clientId: string): Promise<ApiResponse<ClientEntity>> {
    try {
      const payload = {
        client_id: clientId
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/client_details', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching client for edit:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch client details',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: {} as ClientEntity,
        code: '500'
      }
    }
  },

  /**
   * Get loan accounts for a client
   */
  async getClientLoanAccounts(params: {
    client_id: string
    limit?: number
    offset?: number
    loan_number?: string
  }): Promise<ApiResponse> {
    try {
      const payload = {
        client_id: params.client_id,
        limit: params.limit || 10,
        offset: params.offset || 1,
        loan_number: params.loan_number || ''
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/loan_accounts', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching client loan accounts:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch loan accounts',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get loan requests for a client
   */
  async getClientLoanRequests(params: {
    client_id: string
    limit?: number
    offset?: number
    loan_request_number?: string
    status?: string
  }): Promise<ApiResponse> {
    try {
      const payload = {
        client_id: params.client_id,
        limit: params.limit || 10,
        offset: params.offset || 1,
        loan_request_number: params.loan_request_number || '',
        status: params.status || ''
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/loan_request', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching client loan requests:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch loan requests',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get loan repayments for a client
   */
  async getClientLoanRepayments(params: {
    client_id: string
    limit?: number
    offset?: number
    request_number?: string
  }): Promise<ApiResponse> {
    try {
      const payload = {
        client_id: params.client_id,
        limit: params.limit || 10,
        offset: params.offset || 1,
        request_number: params.request_number || ''
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/loan_repayments', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching client loan repayments:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch loan repayments',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get limit requests for a client
   */
  async getClientLimitRequests(params: {
    client_id: string
    limit?: number
    offset?: number
    reference_id?: string
  }): Promise<ApiResponse> {
    try {
      const payload = {
        client_id: params.client_id,
        limit: params.limit || 10,
        offset: params.offset || 1,
        reference_id: params.reference_id || ''
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('merchant/v1/view/loan_limits', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching client limit requests:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch limit requests',
          code: (data.data?.code || status).toString()
        }
      }

      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  }
}
