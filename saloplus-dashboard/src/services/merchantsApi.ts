import { apiClient } from './apiClient'
import { createHashKey } from '@/utils/hash'
import type { ApiResponse, PaginationParams, Merchant, PaginatedResponse } from './types'

/**
 * Merchants API service
 */
export const merchantsApi = {
  /**
   * Get merchants list with pagination
   */
  async getMerchants(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Merchant>>> {
    try {
      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 0,
        page: params.page || 1,
        status: "1",
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/companies', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      // Transform the response to match our expected format
      const transformedData = {
        data: responseData.data || [],
        total_count: responseData.total_count || 0,
        current_page: responseData.current_page || 1,
        per_page: responseData.per_page || 10,
        last_page: responseData.last_page || 1,
        from: responseData.from || 0,
        to: responseData.to || 0
      }
      
      return {
        status: responseData.code,
        message: transformedData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Merchants API error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 422 || status === 500) {
          return {
            status: status,
            message: { data: [], total_count: 0, current_page: 1, per_page: 10 },
            code: status.toString()
          }
        } else if (data.data) {
          return {
            status: data.data.code || status,
            message: { data: [], total_count: 0, current_page: 1, per_page: 10 },
            code: (data.data.code || status).toString()
          }
        }
      }
      
      return {
        status: 500,
        message: { data: [], total_count: 0, current_page: 1, per_page: 10 },
        code: '500'
      }
    }
  },

  /**
   * Add new merchant
   */
  async addMerchant(merchantData: Partial<Merchant>): Promise<ApiResponse> {
    try {
      const payload = {
        company_name: merchantData.client_name,
        company_phone: merchantData.client_phone,
        company_email: merchantData.client_email,
        company_address: merchantData.client_address,
        contact_person: merchantData.client_name,
        dial_code: "254"
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/create_account', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Add merchant error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: status,
          message: data.statusDescription || 'Error adding merchant',
          code: status.toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Update merchant status
   */
  async updateMerchant(merchantData: Partial<Merchant>): Promise<ApiResponse> {
    try {
      const payload = {
        client_account: merchantData.client_account,
        status: merchantData.client_status
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/activate', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Update merchant error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: status,
          message: data.statusDescription || 'Error updating merchant',
          code: status.toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get merchant details
   */
  async getMerchantDetails(clientAccount: string): Promise<ApiResponse<Merchant>> {
    try {
      const payload = {
        client_account: clientAccount
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/company_details', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get merchant details error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: status,
          message: data.statusDescription || 'Error fetching merchant details',
          code: status.toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  }
}
