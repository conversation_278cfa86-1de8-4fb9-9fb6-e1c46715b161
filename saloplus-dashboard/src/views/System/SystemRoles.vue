<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">System Roles</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage system roles and their permissions
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <button
          @click="getItems"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
        <router-link
          :to="{ name: 'add-role' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          Add Role
        </router-link>
      </div>
    </div>
    <!-- Roles Data Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="px-6 py-4">
        <div v-if="isLoading" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-3">#</th>
          <th class="py-3">Role Name</th>
          <th class="text-center py-3">Status</th>
          <th class="py-3">Date Created</th>
          <th class="py-3">Action</th>
          <th></th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in items" :key="item.role_id">
          <td class="py-3 w-1 pr-8">{{ index + 1 }}</td>
          <td class="py-3 font-medium">{{ item.role_name }}</td>
          <td class="text-center  py-3">
            <button class="inline-block px-4 py-2 rounded-md text-white" :class="getStatusBg(item.status)">
              {{ parseInt(item.status) === 1 ? 'Active' : parseInt(item.status) === 3 ? 'Deactivated' : 'Inactive' }}
            </button>
          </td>
          <td class="py-3">{{ formatDate(item.created_at) }}</td>
          <td class="py-3 text-center relative w-24">
            <button class="px-3 py-1 border rounded-md" @click="editRole(item)">Edit Role</button>
          </td>
        </tr>
        </tbody>
      </table>

      <div v-show="total>limit" class="flex w-full text-xs items-center mb-2">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to
          {{ limit * offset < total ? limit * offset : total }} of {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3"
                    v-show="Math.ceil(total/limit) > 1 && offset > 1"
                    @click="gotToPage(offset-1)">
              &larr;
            </button>

            <button class="p-2 px-4"
                    :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">
              1
            </button>

            <button class="p-2 px-4"
                    :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">
              2
            </button>

            <button class="p-2 px-4"
                    :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2"
                    @click="gotToPage(3)">
              3
            </button>

            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>

    </div>
  </div>

</template>

<script>
import { systemApi } from '@/services/systemApi'

export default {
  data() {
    return {
      isOpen: false,
      isLoading: false,
      items: [],
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      permissions: [],
    }
  },

  mounted() {
    this.getItems()
  },

  methods: {
    //
    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.getItems()
    },

    //
    async editRole(userData) {
      let app = this
      // Store role data for editing
      app.$router.push({name: 'edit-role', params: {id: userData.role_id}})
    },

    //
    async getItems() {
      let vm = this
      vm.isLoading = true;
      let payload = {page: vm.offset, per_page: vm.limit}

      try {
        let response = await systemApi.getRoles(payload)
        if (response.status === 200) {
          vm.items = response.message?.data || response.message || []
          vm.total = vm.items.length
        }
      } catch (error) {
        console.error('Error fetching roles:', error)
        vm.items = []
        vm.total = 0
      }

      vm.isLoading = false;
    },

    //
    getRoleBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-amber-400'
        case 4:
          return 'bg-teal-600'
        case 5:
          return 'bg-sky-600'
        default:
          return 'bg-purple-600'
      }
    },

    //
    getStatusBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-red-600'
        default:
          return 'bg-purple-600'
      }
    },

    //
    async permissionList(list) {
      this.permissions = list
      this.isOpen = true
    },

    //
    formatDate(dateString) {
      if (!dateString) return 'N/A';
      try {
        const date = new Date(dateString);
        return date.toLocaleString();
      } catch (error) {
        return 'N/A';
      }
    }
  }
}
</script>
