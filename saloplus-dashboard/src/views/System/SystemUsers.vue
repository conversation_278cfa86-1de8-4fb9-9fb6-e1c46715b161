<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">System Users</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage system users and their access permissions
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <button
          @click="setUsers"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
        <router-link
          v-if="authStore.isSuperUser"
          :to="{ name: 'add-user' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          Add System User
        </router-link>
      </div>
    </div>

    <!-- Users Data Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="px-6 py-4">
        <div v-if="isLoading" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <table class="w-full mb-4 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">User</th>
          <th class="py-2">Client Acc</th>
          <th class="py-2">Username</th>
          <th class="py-2">Role</th>
          <th class="py-2">Logins Attempts</th>
          <th class="py-2">Last Login</th>
          <th class="text-center py-2">Status</th>
          <th class="text-center">Actions</th>
          <th></th>
        </tr>
        </thead>

        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in data" :key="item.id">
          <td class="py-3 font-medium">{{ explodeByUnderscore(item.user_name) }}</td>

          <td class="py-3">
            <span>{{ item.client_name }}</span>
            <br>
            <span>{{ item.account_number }}</span>
          </td>

          <td class="py-3">
            <span>{{ item.email_address }}</span>
            <br>
            <span>+{{ item.msisdn }}</span>
          </td>

          <td class="py-3">{{ getRoleName(item.role_id) }}</td>

          <td class="py-3">
            <span style="color: #1bdc00">Successful:{{ item.cumulative_success_login }}</span>
            <br>
            <span style="color: #dd0000">Failed:{{ item.cumlative_failed_attempts }}</span>
          </td>

          <td class="py-3">{{ formatLastLoggedOn(item.last_logged_on) }}</td>

          <td class="py-3 text-center">
            <button v-if="parseInt(item.status) === 1" class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: green">
              Active
            </button>
            <button v-else-if="parseInt(item.status) === 3"
                    class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: red">
              Deactivated
            </button>
            <button v-else
                    class="inline-block px-3 py-1 rounded-md text-white"
                    style="background-color: purple">
              Inactive
            </button>
          </td>

          <td class="py-2 text-center relative w-24">
            <div class="relative inline-block">
              <button class="px-3 py-1 flex items-center space-x-1"
                      @click="toggleDropdown(index)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 250px; text-align: left;">
                <ul class="py-2 ">

                  <li v-if="parseInt(item.status) === 1"
                      @click.prevent="editRow(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200"> Edit User
                      <b>{{ explodeByUnderscore(item.user_name) }}</b></a>
                  </li>

                  <li @click="resend(parseInt(item.user_id))">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Send OTP to
                      <b>{{ explodeByUnderscore(item.user_name) }}</b></a>
                  </li>

                </ul>
              </div>

            </div>
          </td>
        </tr>
        </tbody>
      </table>

      <!--Pagination-->
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to
          {{ limit * offset < total ? limit * offset : total }} of {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1 && offset > 1" @click="gotToPage(offset-1)">
              &larr;
            </button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===3}" v-show="Math.ceil(total/limit) > 2"
                    @click="gotToPage(3)">3
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>

    </div>
  </div>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'
import { systemApi } from '@/services/systemApi'

export default {
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      organisations: [],
      data: [],
      roles: [],

      //
      fullPage: true,
      total: 0,
      offset: 1,
      limit: 100,
      showDropdown: [],

      isOpen: false,
      isLoading: false,
      //

      moreParams: {
        start: "",
        end: "",
        limit: 100,
        page: 1,
        status: "",
        client_id: "",
      },
      time3: "",
      id: null,
      status: null,
    }
  },

  setup() {
    const authStore = useAuthStore()
    return {
      authStore
    }
  },

  watch: {
    searchClient(newVal, oldVal) {
      if (newVal !== oldVal && newVal !== "") {
        this.filterOrganizations();
      }
    }
  },
  async mounted() {
    await this.setRoles()

    if (!this.authStore.isSuperUser) {
      this.moreParams.client_id = this.$route.params.client_id
    }

    await this.setUsers()
  },

  methods: {
    //
    formatLastLoggedOn(lastLoggedOn) {
      if (!lastLoggedOn) {
        return 'Never logged in';
      }
      try {
        const date = new Date(lastLoggedOn);
        return date.toLocaleString();
      } catch (error) {
        return 'Never logged in';
      }
    },

    explodeByUnderscore(str) {
      if (!str) return '';

      const names = str.split('_');
      const firstName = names[0] || '';
      const secondName = names[1] || '';
      const thirdName = names[2] || '';

      return `${this.capitalizeFirstLetter(firstName)} ${this.capitalizeFirstLetter(secondName)} ${this.capitalizeFirstLetter(thirdName)}`;
    },

    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },

    //
    gotToPage(page) {
      let vm = this
      vm.moreParams.offset = page
      vm.offset = page
      vm.setUsers()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    async resend(user_id) {
      let app = this
      const payload = {
        user_id: user_id,
      }

      app.closeDropdown()

      // Simple confirmation for now - can be replaced with SweetAlert later
      if (confirm('Are you sure you want to resend OTP to this user?')) {
        try {
          app.isLoading = true
          const response = await systemApi.resendOTP(payload)
          if (response.status === 200) {
            alert('OTP sent successfully!')
          } else {
            alert('Error sending OTP: ' + response.message)
          }
        } catch (error) {
          console.error('Error sending OTP:', error)
          alert('Error sending OTP')
        } finally {
          app.isLoading = false
        }
      }
    },

    async editRow(row) {
      this.closeDropdown()
      // Store user data for editing
      this.$router.push({name: 'edit-user', params: {id: row.user_id}})
    },

    async setUsers() {
      this.isLoading = true
      try {
        let response = await systemApi.getUsers(this.moreParams)
        this.data = response.message?.data || []
        this.total = parseInt(response.message?.total_count || 0)

        this.showDropdown = []
        for (let i = 0; i < this.data.length; i++) {
          this.showDropdown.push(false)
        }
      } catch (error) {
        console.error('Error fetching users:', error)
        this.data = []
        this.total = 0
      }
      this.isLoading = false
    },

    // Fetch System roles
    async setRoles() {
      let app = this
      try {
        let response = await systemApi.getRoles({limit: 100})
        if (response.status === 200) {
          app.roles = []
          const rolesData = response.message?.data || response.message || []
          rolesData.forEach(function (item) {
            let role = {text: item.role_name, value: item.role_id}
            app.roles.push(role)
          })
        }
      } catch (error) {
        console.error('Error fetching roles:', error)
      }
    },

    // get role name from role id
    getRoleName(role_id) {
      let role = this.roles.find(role => role.value === role_id)
      return role ? role.text : 'N/A'
    }
  }
}
</script>
