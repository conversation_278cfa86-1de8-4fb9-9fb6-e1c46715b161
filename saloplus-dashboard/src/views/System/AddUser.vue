<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Add System User</h1>
        <p class="mt-1 text-sm text-gray-500">
          Create a new system user account
        </p>
      </div>
      <router-link
        :to="{ name: 'system-roles' }"
        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
        </svg>
        Back to System
      </router-link>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">

      <div v-if="!authStore.isSuperUser" class="relative mb-6">
        <label class="text-xs font-medium mb-1 block">Filter By Organisation <strong
            v-show="clientName">({{ clientName }})</strong></label>
        <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
               @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
        <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
            v-if="searchDropdown">
          <li v-for="item in organisations" class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
              @click="setClientId(item)">{{ item.text }}
          </li>
        </ul>
      </div>

      <!--Full Name, Phone, Email -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">First Names</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.first_name" placeholder="John">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Middle Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.middle_name" placeholder="Smith">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Last Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.last_name" placeholder="Doe">
        </div>
      </div>

      <!-- Phone, Email -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Phone Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="tel"
                 v-model="form.msisdn" placeholder="07xx xxx xxx">
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Email Address</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="email"
                 v-model="form.email_address" placeholder="<EMAIL>">
        </div>
      </div>

      <!--Nationality, Identify Type, ID number -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Nationality</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.nationality">
            <option value="" disabled selected>Select Country</option> <!-- Placeholder option -->
            <option v-for="nationality in nationalities" :value="nationality.value">
              {{ nationality.text }}
            </option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Identify Type</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.identifier_type">
            <option value="" disabled selected>Select ID Type</option> <!-- Placeholder option -->
            <option v-for="type in identifier_types" :value="type.value">
              {{ type.text }}
            </option>
          </select>
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">ID Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number"
                 v-model="form.national_id" placeholder="12345678">
        </div>
      </div>

      <!--Organisation Dropdown -->
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Assign Role</label>
        <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.role_id">
          <option value="" disabled selected>Choose one Role</option> <!-- Placeholder option -->
          <option v-for="role in roles" :value="role.value">
            {{ role.text }}
          </option>
        </select>
      </div>

      <!--Buttons-->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'system-roles'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20" @click="createUser"
                id="addSystemUser">
          <div v-show="loading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white inline-block mr-2"></div>
          Submit
        </button>
      </div>

    </div>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'
import { systemApi } from '@/services/systemApi'
import { clientsApi } from '@/services/clientsApi'

export default {
  data() {
    return {
      isLoading: false,
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      loading: false,
      organisations: [],
      roles: [],
      form: {
        dial_code: "254",
        msisdn: '',
        email_address: '',
        first_name: '',
        middle_name: '',
        last_name: '',
        role_id: '',
        national_id: '',
        nationality: 'KENYA',
        identifier_type: 'NATIONAL_ID',
        account_number: ''
      },
      nationalities: [
        {text: 'KENYA', value: 'KENYA'}
      ],
      identifier_types: [
        {text: 'NATIONAL ID', value: 'NATIONAL_ID'},
        {text: 'HUDUMA ID', value: 'HUDUMA_ID'},
        {text: 'PASSPORT', value: 'PASSPORT'},
        {text: 'ALIEN ID', value: 'ALIEN_ID'}
      ],
    }
  },

  setup() {
    const authStore = useAuthStore()
    return {
      authStore
    }
  },

  watch: {
    client_id(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.onOrganisationSelected(newVal)
      }
    },
  },

  async mounted() {
    // Call Organisations
    if (!this.authStore.isSuperUser) {
      this.form.account_number = this.authStore.user?.client_account || ''
    }

    await this.setOrganisations()
    await this.setRoles()
  },

  methods: {
    setClientId(item) {
      this.clientName = item.text
      this.form.account_number = item.value

      this.searchDropdown = false
      this.searchClient = ""
      this.searchDropdownPlaceholder = this.clientName
      console.log("filterOrganizations id", this.form.account_number)
    },

    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },

    // Fetch and set Organisations to UI
    async setOrganisations() {
      let app = this
      app.isLoading = true;

      try {
        let response = await clientsApi.getClients({limit: 100})
        if (response.status === 200) {
          response.message.data.forEach(function (item) {
            let _organisations = {text: item.client_name, value: item.client_account}
            app.organisations.push(_organisations)
          })
        } else {
          app.organisations = []
        }
      } catch (error) {
        console.error('Error fetching organisations:', error)
        app.organisations = []
      }

      app.isLoading = false
    },

    // Fetch System roles
    async setRoles() {
      let app = this
      try {
        let response = await systemApi.getRoles({limit: 100})
        if (response.status === 200) {
          const rolesData = response.message?.data || response.message || []
          rolesData.forEach(function (item) {
            let role = {text: item.role_name, value: item.role_id}
            app.roles.push(role)
          })
        }
      } catch (error) {
        console.error('Error fetching roles:', error)
      }
    },

    async createUser() {
      let app = this

      const payload = this.form

      if (confirm('Are you sure you want to add this user?')) {
        try {
          app.loading = true
          const response = await systemApi.createUser(payload)
          if (response.status === 200) {
            alert('User added successfully!')
            await this.$router.push({name: 'system-users'})
          } else {
            alert('Error adding user: ' + response.message)
          }
        } catch (error) {
          console.error('Error adding user:', error)
          alert('Error adding user')
        } finally {
          app.loading = false
        }
      }
    },
  }
}
</script>
