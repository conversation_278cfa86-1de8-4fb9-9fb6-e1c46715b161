<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Merchants</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage and view all registered merchants
        </p>
      </div>
      <button
        @click="showAddModal = true"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
      >
        <PlusIcon class="h-4 w-4 mr-2" />
        Add Merchant
      </button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Active Merchants -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <BuildingOfficeIcon class="w-5 h-5 text-green-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Active Merchants</p>
            <p class="text-2xl font-bold text-gray-900">{{ activeCount }}</p>
          </div>
        </div>
      </div>

      <!-- Inactive Merchants -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <BuildingOfficeIcon class="w-5 h-5 text-red-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Inactive Merchants</p>
            <p class="text-2xl font-bold text-gray-900">{{ inactiveCount }}</p>
          </div>
        </div>
      </div>

      <!-- Can Issue Loans -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <BuildingOfficeIcon class="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Can Issue Loans</p>
            <p class="text-2xl font-bold text-gray-900">{{ canIssueLoanCount }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filter Cards -->
    <FilterCards
      :filters="filters"
      filter-type="merchants"
      :status-options="statusOptions"
      @update:filters="updateFilters"
      @apply="applyFilters"
      @clear="clearFilters"
    />

    <!-- Merchants Data Table -->
    <DataTable
      :data="merchants"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Merchants"
      row-key="client_id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <button
          @click="refreshData"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
      </template>

      <!-- Custom cell content -->
      <template #cell-client_status="{ value }">
        <span
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          :class="{
            'bg-green-100 text-green-800': value === 1,
            'bg-red-100 text-red-800': value === 0
          }"
        >
          {{ value === 1 ? 'Active' : 'Inactive' }}
        </span>
      </template>

      <template #cell-total_loan_assets="{ value }">
        <span class="font-medium text-gray-900">
          {{ formatCurrency(value) }}
        </span>
      </template>

      <template #cell-open_date="{ value, item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ formatTime(value) }}</div>
          <div class="text-gray-500">to {{ formatTime(item.close_date) }}</div>
        </div>
      </template>

      <template #cell-b2c_paybill="{ value, item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">B2C: {{ value }}</div>
          <div class="text-gray-500">C2B: {{ item.c2b_paybill }}</div>
        </div>
      </template>

      <template #cell-created="{ value }">
        <span class="text-sm text-gray-500">
          {{ formatDate(value) }}
        </span>
      </template>

      <!-- Row Actions -->
      <template #actions="{ item, index }">
        <div class="relative">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700"
          >
            Actions
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>

          <!-- Dropdown Menu -->
          <Transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-if="showDropdown[index]"
              class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5"
            >
              <div class="py-1">
                <button
                  @click="editMerchant(item)"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Edit Merchant
                </button>
                <button
                  @click="toggleStatus(item)"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  {{ item.client_status === 1 ? 'Deactivate' : 'Activate' }}
                </button>
                <button
                  @click="viewTransactions(item)"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  View Transactions
                </button>
                <button
                  @click="viewLoanProducts(item)"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Loan Products
                </button>
                <button
                  @click="viewSystemUsers(item)"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  System Users
                </button>
              </div>
            </div>
          </Transition>
        </div>
      </template>
    </DataTable>

    <!-- Add Merchant Modal -->
    <Transition
      enter-active-class="transition ease-out duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition ease-in duration-200"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div v-if="showAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Add New Merchant</h3>
            <form @submit.prevent="addMerchant">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Merchant Name</label>
                  <input
                    v-model="newMerchant.client_name"
                    type="text"
                    required
                    class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Phone</label>
                  <input
                    v-model="newMerchant.client_phone"
                    type="tel"
                    required
                    class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    v-model="newMerchant.client_email"
                    type="email"
                    required
                    class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Address</label>
                  <textarea
                    v-model="newMerchant.client_address"
                    rows="3"
                    class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  ></textarea>
                </div>
              </div>
              <div class="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  @click="showAddModal = false"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  :disabled="loading"
                  class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {{ loading ? 'Adding...' : 'Add Merchant' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, reactive } from 'vue'
import { PlusIcon, BuildingOfficeIcon } from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import FilterCards from '@/components/FilterCards.vue'
import { merchantsApi } from '@/services/merchantsApi'
import { useBackofficeActions } from '@/composables/useBackofficeActions'
import type { Merchant, PaginationParams } from '@/services/types'

// Use backoffice actions composable
const {
  showDropdown,
  loading,
  currentPage,
  pageSize,
  totalRecords,
  searchQuery,
  formatCurrency,
  formatDate,
  formatTime,
  toggleDropdown,
  closeDropdown,
  goToPage,
  editRow,
  viewRelatedData,
  handleSearch: handleSearchAction
} = useBackofficeActions()

// Reactive data
const merchants = ref<Merchant[]>([])
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)

// Filter state
const filters = reactive({
  status: '',
  client_id: '',
  phone_number: '',
  customer_name: ''
})

// Status options for filter
const statusOptions = [
  { value: '1', label: 'Active' },
  { value: '0', label: 'Inactive' }
]

// New merchant form data
const newMerchant = ref<Partial<Merchant>>({
  client_name: '',
  client_phone: '',
  client_email: '',
  client_address: ''
})

// Table headers configuration
const tableHeaders = {
  client_account: 'Account',
  client_name: 'Merchant Name',
  client_phone: 'Contact Info',
  total_loan_assets: 'Loan Assets',
  open_date: 'Operating Hours',
  b2c_paybill: 'PayBill Numbers',
  client_status: 'Status',
  created: 'Created Date'
}

// Computed properties
const activeCount = computed(() =>
  merchants.value.filter(merchant => merchant.client_status === 1).length
)

const inactiveCount = computed(() =>
  merchants.value.filter(merchant => merchant.client_status === 0).length
)

const canIssueLoanCount = computed(() =>
  merchants.value.filter(merchant => merchant.can_issue_loans === '1').length
)

// Methods
const fetchMerchants = async (params: PaginationParams = {}) => {
  loading.value = true
  try {
    const response = await merchantsApi.getMerchants({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      sortField: sortField.value,
      sortDirection: sortDirection.value,
      ...params
    })

    if (response.status === 200) {
      merchants.value = response.message.data || []
      totalRecords.value = response.message.total_count || 0
    } else {
      console.error('Error fetching merchants:', response.message)
      merchants.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching merchants:', error)
    merchants.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

// Filter methods
const updateFilters = (newFilters: Record<string, any>) => {
  Object.assign(filters, newFilters)
}

const applyFilters = (appliedFilters: Record<string, any>) => {
  Object.assign(filters, appliedFilters)
  currentPage.value = 1
  fetchMerchants()
}

const clearFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = ''
  })
  currentPage.value = 1
  fetchMerchants()
}

const handlePageChange = (page: number) => {
  goToPage(page, fetchMerchants)
}

const handleSearch = (query: string) => {
  handleSearchAction(query, fetchMerchants)
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  currentPage.value = 1
  fetchMerchants()
}

const handleRowClick = (item: Merchant) => {
  console.log('Row clicked:', item)
  // You can add navigation logic here
}

const refreshData = () => {
  fetchMerchants()
}

// Use the composable's toggleDropdown method directly

const addMerchant = async () => {
  if (!newMerchant.value.client_name || !newMerchant.value.client_phone || !newMerchant.value.client_email) {
    return
  }

  loading.value = true
  try {
    const response = await merchantsApi.addMerchant(newMerchant.value)

    if (response.status === 200) {
      showAddModal.value = false
      newMerchant.value = {
        client_name: '',
        client_phone: '',
        client_email: '',
        client_address: ''
      }
      fetchMerchants()
    } else {
      console.error('Error adding merchant:', response.message)
    }
  } catch (error) {
    console.error('Error adding merchant:', error)
  } finally {
    loading.value = false
  }
}

const editMerchant = async (merchant: Merchant) => {
  await editRow(merchant, 'merchants-edit')
}

const toggleStatus = async (merchant: Merchant) => {
  const newStatus = merchant.client_status === 1 ? 0 : 1

  try {
    const response = await merchantsApi.updateMerchant({
      client_account: merchant.client_account,
      client_status: newStatus
    })

    if (response.status === 200) {
      merchant.client_status = newStatus
    } else {
      console.error('Error updating merchant status:', response.message)
    }
  } catch (error) {
    console.error('Error updating merchant status:', error)
  }

  closeDropdown()
}

const viewTransactions = (merchant: Merchant) => {
  // Navigate to transactions for this merchant
  console.log('View transactions for:', merchant.client_name)
  showDropdown[merchants.value.indexOf(merchant)] = false
}

const viewLoanProducts = (merchant: Merchant) => {
  // Navigate to loan products for this merchant
  console.log('View loan products for:', merchant.client_name)
  showDropdown[merchants.value.indexOf(merchant)] = false
}

const viewSystemUsers = (merchant: Merchant) => {
  // Navigate to system users for this merchant
  console.log('View system users for:', merchant.client_name)
  showDropdown[merchants.value.indexOf(merchant)] = false
}



// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    Object.keys(showDropdown).forEach(key => {
      showDropdown[parseInt(key)] = false
    })
  }
}

onMounted(() => {
  fetchMerchants()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
