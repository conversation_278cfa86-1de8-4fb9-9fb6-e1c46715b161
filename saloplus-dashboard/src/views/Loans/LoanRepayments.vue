<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Loan Repayments</h1>
          <p class="text-gray-600 mt-1">Track and manage loan repayments from customers</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="refreshData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Filter Cards -->
    <FilterCards
      :filters="filters"
      filter-type="repayments"
      :status-options="statusOptions"
      @update:filters="updateFilters"
      @apply="applyFilters"
      @clear="clearFilters"
    />

    <!-- Loan Repayments Data Table -->
    <DataTable
      :data="loanRepayments"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Loan Repayments"
      row-key="repayment_id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button
            @click="exportData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </template>

      <!-- Custom columns -->
      <template #cell-loan_number="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ row.loan_number }}</div>
          <div class="text-xs text-gray-500">Req: {{ row.request_number }}</div>
        </div>
      </template>

      <template #cell-amount="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ formatCurrency(row.amount) }}
        </div>
      </template>

      <template #cell-payment_method="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ row.payment_method }}</div>
          <div class="text-xs text-gray-500">{{ row.reference }}</div>
        </div>
      </template>

      <template #cell-status="{ row }">
        <span :class="getStatusClass(row.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
          {{ getStatusText(row.status) }}
        </span>
      </template>

      <template #cell-payment_date="{ row }">
        <div class="text-sm text-gray-900">
          {{ formatDate(row.payment_date) }}
        </div>
      </template>

      <!-- Actions dropdown -->
      <template #actions="{ row, index }">
        <div class="relative">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EllipsisVerticalIcon class="w-4 h-4" />
          </button>

          <div
            v-if="showDropdown[index]"
            class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          >
            <div class="py-1">
              <button
                @click="viewDetails(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                View Details
              </button>
              <button
                @click="downloadReceipt(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"
              >
                Download Receipt
              </button>
              <button
                v-if="row.status === 2"
                @click="verifyPayment(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"
              >
                Verify Payment
              </button>
            </div>
          </div>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowPathIcon,
  ArrowDownTrayIcon,
  EllipsisVerticalIcon
} from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import FilterCards from '@/components/FilterCards.vue'
import { loanApi, type LoanRepayment } from '@/services/loanApi'
import { useAuthStore } from '@/stores/auth'
import { useBackofficeActions } from '@/composables/useBackofficeActions'

// Router and auth store
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Use backoffice actions composable
const {
  showDropdown,
  loading,
  currentPage,
  pageSize,
  totalRecords,
  searchQuery,
  toggleDropdown,
  closeDropdown,
  goToPage,
  editRow,
  viewRelatedData,
  handleSearch: handleSearchAction
} = useBackofficeActions()

// Reactive data
const loanRepayments = ref<LoanRepayment[]>([])
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')

// Filters
const filters = ref({
  client_id: authStore.selectedClientId || '',
  loan_number: (route.query.loan_number as string) || '',
  request_number: '',
  start_date: '',
  end_date: '',
  payment_method: ''
})

// Status options for filters
const statusOptions = [
  { value: '1', label: 'Completed' },
  { value: '2', label: 'Pending' },
  { value: '3', label: 'Failed' },
  { value: '4', label: 'Reversed' }
]

// Table headers
const tableHeaders = [
  { key: 'loan_number', label: 'Loan/Request', sortable: true },
  { key: 'amount', label: 'Amount', sortable: true },
  { key: 'payment_method', label: 'Payment Method', sortable: false },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'payment_date', label: 'Payment Date', sortable: true }
]

// Methods
const fetchLoanRepayments = async () => {
  loading.value = true
  try {
    const response = await loanApi.getLoanRepayments({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      ...filters.value
    })

    if (response.status === 200) {
      loanRepayments.value = response.message?.data || []
      totalRecords.value = response.message?.total_count || 0
    } else {
      loanRepayments.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching loan repayments:', error)
    loanRepayments.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchLoanRepayments()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  fetchLoanRepayments()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  fetchLoanRepayments()
}

const handleRowClick = (row: LoanRepayment) => {
  viewDetails(row)
}

// Filter methods
const updateFilters = (newFilters: Record<string, any>) => {
  Object.assign(filters.value, newFilters)
}

const refreshData = () => {
  fetchLoanRepayments()
}

const applyFilters = (newFilters: Record<string, any>) => {
  filters.value = { ...newFilters }
  currentPage.value = 1
  fetchLoanRepayments()
}

const clearFilters = () => {
  filters.value = {
    client_id: authStore.selectedClientId || '',
    loan_number: (route.query.loan_number as string) || '',
    request_number: '',
    start_date: '',
    end_date: '',
    payment_method: ''
  }
  currentPage.value = 1
  fetchLoanRepayments()
}



const viewDetails = (repayment: LoanRepayment) => {
  console.log('View details for repayment:', repayment.repayment_id)
  // TODO: Navigate to repayment details page or open modal
}

const downloadReceipt = (repayment: LoanRepayment) => {
  console.log('Download receipt for repayment:', repayment.repayment_id)
  // TODO: Implement receipt download
}

const verifyPayment = async (repayment: LoanRepayment) => {
  console.log('Verify payment:', repayment.repayment_id)
  // TODO: Implement payment verification
}

const exportData = () => {
  console.log('Export loan repayments data')
  // TODO: Implement export functionality
}

// Utility functions
const formatCurrency = (amount: number | string) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(num)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'Completed',
    2: 'Pending',
    3: 'Failed',
    4: 'Reversed'
  }
  return statusMap[status] || 'Unknown'
}

const getStatusClass = (status: number) => {
  const statusClasses: Record<number, string> = {
    1: 'bg-green-100 text-green-800',
    2: 'bg-yellow-100 text-yellow-800',
    3: 'bg-red-100 text-red-800',
    4: 'bg-gray-100 text-gray-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// Initialize data
onMounted(() => {
  fetchLoanRepayments()
})
</script>
