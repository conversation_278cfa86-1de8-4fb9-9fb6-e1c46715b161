<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Loan Products</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage loan products and configurations for {{ selectedClient?.client_name || 'All Organizations' }}
        </p>
      </div>
      <button
        v-if="canAddProducts"
        @click="showAddModal = true"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
      >
        <PlusIcon class="h-4 w-4 mr-2" />
        Add Loan Product
      </button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CreditCardIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                <dd class="text-lg font-medium text-gray-900">{{ totalRecords }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                <div class="h-3 w-3 bg-green-500 rounded-full"></div>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Active Products</dt>
                <dd class="text-lg font-medium text-gray-900">{{ activeCount }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CurrencyDollarIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Avg Interest Rate</dt>
                <dd class="text-lg font-medium text-gray-900">{{ averageInterestRate }}%</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CalendarIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Avg Period</dt>
                <dd class="text-lg font-medium text-gray-900">{{ averagePeriod }} months</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loan Products Data Table -->
    <DataTable
      :data="loanProducts"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Loan Products"
      row-key="client_product_id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <button
          @click="refreshData"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
      </template>

      <!-- Custom Cell: Product Name -->
      <template #cell-product_name="{ value }">
        <div class="font-medium text-blue-600">{{ value }}</div>
      </template>

      <!-- Custom Cell: Period -->
      <template #cell-repayment_period="{ value }">
        <div class="text-sm text-gray-900">{{ value }} months</div>
      </template>

      <!-- Custom Cell: Interest Rate -->
      <template #cell-interest_rate="{ value }">
        <div class="text-sm font-medium text-green-600">{{ value }}%</div>
      </template>

      <!-- Custom Cell: Late Fine Interest -->
      <template #cell-late_fine_interest_rate="{ value }">
        <div class="text-sm font-medium text-red-600">{{ (parseFloat(value) * 100).toFixed(2) }}%</div>
      </template>

      <!-- Custom Cell: Loan Amount Range -->
      <template #cell-minimum_loan_amount="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">
            {{ item.currency_code }} {{ formatCurrency(item.minimum_loan_amount) }} - {{ formatCurrency(item.maximum_loan_amount) }}
          </div>
          <div class="text-xs text-gray-500">Min - Max Amount</div>
        </div>
      </template>

      <!-- Custom Cell: Status -->
      <template #cell-status="{ value }">
        <span :class="[
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          value === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        ]">
          {{ value === 1 ? 'Active' : 'Inactive' }}
        </span>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <div class="relative" v-if="canEditProducts">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
            </svg>
          </button>

          <!-- Dropdown Menu -->
          <div
            v-if="showDropdown[index]"
            class="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10"
          >
            <div class="py-1">
              <button
                @click="editProduct(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                Edit Product
              </button>
              <button
                @click="viewProductDetails(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                View Details
              </button>
              <button
                v-if="item.status === 1"
                @click="deactivateProduct(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"
              >
                Deactivate
              </button>
              <button
                v-else
                @click="activateProduct(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"
              >
                Activate
              </button>
            </div>
          </div>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { PlusIcon, CreditCardIcon, CurrencyDollarIcon, CalendarIcon } from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import { loanApi } from '@/services/loanApi'
import { useAuthStore } from '@/stores/auth'

// Types
interface LoanProduct {
  client_product_id: string
  product_name: string
  repayment_period: number
  interest_rate: number
  late_fine_interest_rate: number
  minimum_loan_amount: number
  maximum_loan_amount: number
  currency_code: string
  status: number
  created_at?: string
}

interface PaginationParams {
  page?: number
  limit?: number
  offset?: number
  search?: string
  sortField?: string
  sortDirection?: 'asc' | 'desc'
}

// Auth store
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const loanProducts = ref<LoanProduct[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)
const showDropdown = reactive<Record<number, boolean>>({})

// New product form data
const newProduct = ref<Partial<LoanProduct>>({
  product_name: '',
  repayment_period: 12,
  interest_rate: 0,
  late_fine_interest_rate: 0,
  minimum_loan_amount: 0,
  maximum_loan_amount: 0
})

// Table headers configuration
const tableHeaders = {
  product_name: 'Product Name',
  repayment_period: 'Period',
  interest_rate: 'Interest Rate',
  late_fine_interest_rate: 'Late Fine Rate',
  minimum_loan_amount: 'Loan Amount Range',
  status: 'Status'
}

// Computed properties
const selectedClient = computed(() => authStore.selectedClient)
const canAddProducts = computed(() => authStore.hasPermission('loan_products_create'))
const canEditProducts = computed(() => authStore.hasPermission('loan_products_edit'))

const activeCount = computed(() =>
  loanProducts.value.filter(product => product.status === 1).length
)

const averageInterestRate = computed(() => {
  if (loanProducts.value.length === 0) return 0
  const total = loanProducts.value.reduce((sum, product) => sum + product.interest_rate, 0)
  return (total / loanProducts.value.length).toFixed(2)
})

const averagePeriod = computed(() => {
  if (loanProducts.value.length === 0) return 0
  const total = loanProducts.value.reduce((sum, product) => sum + product.repayment_period, 0)
  return Math.round(total / loanProducts.value.length)
})

// Methods
const fetchLoanProducts = async (params: PaginationParams = {}) => {
  loading.value = true
  try {
    const response = await loanApi.getLoanProducts({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      client_id: authStore.selectedClientId,
      ...params
    })

    if (response.status === 200) {
      loanProducts.value = response.message?.data || []
      totalRecords.value = response.message?.total_count || 0
    } else {
      loanProducts.value = []
      totalRecords.value = 0
    }

  } catch (error) {
    console.error('Error fetching loan products:', error)
    loanProducts.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchLoanProducts()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  fetchLoanProducts()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  currentPage.value = 1
  fetchLoanProducts()
}

const handleRowClick = (item: LoanProduct) => {
  console.log('Row clicked:', item)
}

const refreshData = () => {
  fetchLoanProducts()
}

const toggleDropdown = (index: number) => {
  // Close all other dropdowns
  Object.keys(showDropdown).forEach(key => {
    if (parseInt(key) !== index) {
      showDropdown[parseInt(key)] = false
    }
  })

  // Toggle current dropdown
  showDropdown[index] = !showDropdown[index]
}

const editProduct = (product: LoanProduct) => {
  console.log('Edit product:', product.product_name)
  // TODO: Navigate to edit page or open edit modal
  showDropdown[loanProducts.value.indexOf(product)] = false
}

const viewProductDetails = (product: LoanProduct) => {
  console.log('View product details:', product.product_name)
  showDropdown[loanProducts.value.indexOf(product)] = false
}

const activateProduct = async (product: LoanProduct) => {
  try {
    loading.value = true
    // TODO: API call to activate product
    console.log('Activating product:', product.product_name)
    await fetchLoanProducts()
  } catch (error) {
    console.error('Error activating product:', error)
  } finally {
    loading.value = false
    showDropdown[loanProducts.value.indexOf(product)] = false
  }
}

const deactivateProduct = async (product: LoanProduct) => {
  try {
    loading.value = true
    // TODO: API call to deactivate product
    console.log('Deactivating product:', product.product_name)
    await fetchLoanProducts()
  } catch (error) {
    console.error('Error deactivating product:', error)
  } finally {
    loading.value = false
    showDropdown[loanProducts.value.indexOf(product)] = false
  }
}

const addProduct = async () => {
  try {
    loading.value = true
    // TODO: API call to add product
    console.log('Adding product:', newProduct.value)

    showAddModal.value = false
    newProduct.value = {
      product_name: '',
      repayment_period: 12,
      interest_rate: 0,
      late_fine_interest_rate: 0,
      minimum_loan_amount: 0,
      maximum_loan_amount: 0
    }
    await fetchLoanProducts()
  } catch (error) {
    console.error('Error adding product:', error)
  } finally {
    loading.value = false
  }
}

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Lifecycle
onMounted(() => {
  fetchLoanProducts()
})
</script>
