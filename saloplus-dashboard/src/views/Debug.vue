<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="space-y-6">
      <!-- Page Header -->
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Network Debug Page</h1>
        <p class="mt-2 text-gray-600">
          Debug API calls and network issues for organizations/clients migration
        </p>
      </div>

      <!-- Network Debugger Component -->
      <NetworkDebugger />

      <!-- Additional Debug Information -->
      <div class="bg-white p-6 rounded-lg shadow border">
        <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Environment Variables -->
          <div>
            <h3 class="font-medium mb-2">Environment Variables</h3>
            <div class="bg-gray-50 p-3 rounded text-sm space-y-1">
              <div><strong>VITE_BASE_API:</strong> {{ envVars.VITE_BASE_API }}</div>
              <div><strong>VITE_APP_KEY:</strong> {{ envVars.VITE_APP_KEY ? '***' + envVars.VITE_APP_KEY.slice(-4) : 'Not set' }}</div>
              <div><strong>VITE_AUTH_KEY:</strong> {{ envVars.VITE_AUTH_KEY ? '***' + envVars.VITE_AUTH_KEY.slice(-4) : 'Not set' }}</div>
              <div><strong>VITE_DEV_MODE:</strong> {{ envVars.VITE_DEV_MODE }}</div>
            </div>
          </div>

          <!-- Local Storage -->
          <div>
            <h3 class="font-medium mb-2">Local Storage</h3>
            <div class="bg-gray-50 p-3 rounded text-sm space-y-1">
              <div><strong>Token:</strong> {{ localStorageData.token ? 'Present' : 'Missing' }}</div>
              <div><strong>User:</strong> {{ localStorageData.user ? 'Present' : 'Missing' }}</div>
              <div><strong>Selected Client ID:</strong> {{ localStorageData.selectedClientId || 'Not set' }}</div>
              <div><strong>Client Mode:</strong> {{ localStorageData.clientMode || 'Not set' }}</div>
            </div>
          </div>
        </div>

        <!-- Browser Information -->
        <div class="mt-6">
          <h3 class="font-medium mb-2">Browser Information</h3>
          <div class="bg-gray-50 p-3 rounded text-sm space-y-1">
            <div><strong>User Agent:</strong> {{ navigator.userAgent }}</div>
            <div><strong>Current URL:</strong> {{ window.location.href }}</div>
            <div><strong>Protocol:</strong> {{ window.location.protocol }}</div>
          </div>
        </div>

        <!-- Network Test Results -->
        <div class="mt-6">
          <h3 class="font-medium mb-2">Network Test Results</h3>
          <div class="space-y-2">
            <button 
              @click="testBasicConnectivity"
              :disabled="testing"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              Test Basic Connectivity
            </button>
            <button 
              @click="testCORS"
              :disabled="testing"
              class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
            >
              Test CORS
            </button>
            <button
              @click="testAuthentication"
              :disabled="testing"
              class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
            >
              Test Authentication
            </button>
            <button
              @click="testOrganizationsFromAuth"
              :disabled="testing"
              class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
            >
              Test Orgs (Auth File)
            </button>
          </div>
          
          <div v-if="testResults.length > 0" class="mt-4">
            <h4 class="font-medium mb-2">Test Results:</h4>
            <div class="bg-gray-50 p-3 rounded max-h-40 overflow-y-auto">
              <div v-for="(result, index) in testResults" :key="index" class="text-sm mb-2">
                <span :class="result.success ? 'text-green-600' : 'text-red-600'">
                  {{ result.timestamp }}: {{ result.message }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Instructions -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 class="font-medium text-blue-900 mb-2">Debugging Instructions</h3>
        <ol class="list-decimal list-inside text-sm text-blue-800 space-y-1">
          <li>Open browser DevTools (F12 or right-click → Inspect)</li>
          <li>Go to the Network tab in DevTools</li>
          <li>Make sure "Preserve log" is checked</li>
          <li>Click the test buttons above to trigger API calls</li>
          <li>Look for requests to {{ envVars.VITE_BASE_API }}</li>
          <li>Check the Console tab for detailed logs</li>
          <li>If no network requests appear, there might be a JavaScript error preventing the calls</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import NetworkDebugger from '@/components/NetworkDebugger.vue'
import { apiClient } from '@/services/apiClient'
import { authApi } from '@/services/authApi'

const testing = ref(false)
const testResults = ref<Array<{timestamp: string, message: string, success: boolean}>>([])

const envVars = reactive({
  VITE_BASE_API: import.meta.env.VITE_BASE_API,
  VITE_APP_KEY: import.meta.env.VITE_APP_KEY,
  VITE_AUTH_KEY: import.meta.env.VITE_AUTH_KEY,
  VITE_DEV_MODE: import.meta.env.VITE_DEV_MODE
})

const localStorageData = reactive({
  token: localStorage.getItem('token'),
  user: localStorage.getItem('user'),
  selectedClientId: localStorage.getItem('selectedClientId'),
  clientMode: localStorage.getItem('clientMode')
})

const addTestResult = (message: string, success: boolean) => {
  testResults.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message,
    success
  })
}

const testBasicConnectivity = async () => {
  testing.value = true
  try {
    console.log('🧪 Testing basic connectivity...')
    const response = await fetch(envVars.VITE_BASE_API, { 
      method: 'HEAD',
      mode: 'no-cors'
    })
    addTestResult('Basic connectivity test completed', true)
  } catch (error) {
    console.error('Basic connectivity test failed:', error)
    addTestResult(`Basic connectivity failed: ${error}`, false)
  } finally {
    testing.value = false
  }
}

const testCORS = async () => {
  testing.value = true
  try {
    console.log('🧪 Testing CORS...')
    const response = await fetch(`${envVars.VITE_BASE_API}health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    addTestResult(`CORS test: ${response.status} ${response.statusText}`, response.ok)
  } catch (error) {
    console.error('CORS test failed:', error)
    addTestResult(`CORS test failed: ${error}`, false)
  } finally {
    testing.value = false
  }
}

const testAuthentication = async () => {
  testing.value = true
  try {
    console.log('🧪 Testing authentication...')
    const response = await apiClient.post('merchant/v1/view/companies', {
      limit: 1,
      offset: 0,
      page: 1,
      status: "1"
    })
    addTestResult(`Authentication test: ${response.status} ${response.statusText}`, response.status === 200)
  } catch (error: any) {
    console.error('Authentication test failed:', error)
    const status = error.response?.status || 'Unknown'
    addTestResult(`Authentication test failed: ${status} - ${error.message}`, false)
  } finally {
    testing.value = false
  }
}

const testOrganizationsFromAuth = async () => {
  testing.value = true
  try {
    console.log('🧪 Testing Organizations from Auth file...')
    const response = await authApi.getOrganizationsDebug({ limit: 5 })
    addTestResult(`Organizations (Auth File): Success - ${response.message.data?.length || 0} records`, response.status === 200)
  } catch (error: any) {
    console.error('Organizations (Auth File) test failed:', error)
    const status = error.response?.status || 'Unknown'
    addTestResult(`Organizations (Auth File) failed: ${status} - ${error.message}`, false)
  } finally {
    testing.value = false
  }
}

onMounted(() => {
  console.log('🔧 Debug page mounted')
  console.log('Environment:', envVars)
  console.log('Local Storage:', localStorageData)
})
</script>
