<template>
  <div class="space-y-6">
    <!-- Global Success Message -->
    <div v-if="successMessage" class="p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
      {{ successMessage }}
    </div>

    <!-- Global Error Message -->
    <div v-if="errorMessage" class="p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
      {{ errorMessage }}
    </div>

    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Clients</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage and view all registered clients
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <!-- Search Toggle Button -->
        <button
          @click="showSearch = !showSearch"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <MagnifyingGlassIcon class="h-4 w-4 mr-2" />
          Search
        </button>

        <!-- Filter Toggle Button -->
        <button
          @click="showFilters = !showFilters"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <FunnelIcon class="h-4 w-4 mr-2" />
          Filters
        </button>

        <!-- Add Client Button -->
        <router-link
          :to="{ name: 'clients-add' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <PlusIcon class="h-4 w-4 mr-2" />
          Add Client
        </router-link>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Active Clients -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <BuildingOfficeIcon class="w-5 h-5 text-green-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Active Clients</p>
            <p class="text-2xl font-bold text-gray-900">{{ activeCount }}</p>
          </div>
        </div>
      </div>

      <!-- Inactive Clients -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <BuildingOfficeIcon class="w-5 h-5 text-red-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Inactive Clients</p>
            <p class="text-2xl font-bold text-gray-900">{{ inactiveCount }}</p>
          </div>
        </div>
      </div>

      <!-- Can Issue Loans -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <BuildingOfficeIcon class="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Can Issue Loans</p>
            <p class="text-2xl font-bold text-gray-900">{{ canIssueLoanCount }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Search Section -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 -translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 -translate-y-2"
    >
      <div v-if="showSearch" class="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div class="flex items-center space-x-4">
          <div class="flex-1">
            <div class="relative">
              <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search clients by name, account, email, or phone..."
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                @input="debouncedSearch"
              />
            </div>
          </div>
          <button
            v-if="searchQuery"
            @click="clearSearch"
            class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <XMarkIcon class="h-4 w-4 mr-2" />
            Clear
          </button>
        </div>
      </div>
    </Transition>

    <!-- Collapsible Filter Section -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 -translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 -translate-y-2"
    >
      <div v-if="showFilters" class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Filters</h3>
            <button
              @click="clearFilters"
              class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <XMarkIcon class="h-4 w-4 mr-2" />
              Clear All
            </button>
          </div>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Status Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                v-model="filters.status"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                @change="applyFilters"
              >
                <option value="">All Statuses</option>
                <option value="1">Active</option>
                <option value="0">Inactive</option>
              </select>
            </div>

            <!-- Account Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Account</label>
              <input
                v-model="filters.account"
                type="text"
                placeholder="Search by account..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                @input="debouncedFilter"
              />
            </div>

            <!-- Client Name Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Client Name</label>
              <input
                v-model="filters.clientName"
                type="text"
                placeholder="Search by name..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                @input="debouncedFilter"
              />
            </div>

            <!-- Contact Info Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Contact Info</label>
              <input
                v-model="filters.contactInfo"
                type="text"
                placeholder="Email or phone..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                @input="debouncedFilter"
              />
            </div>

            <!-- Currency Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
              <select
                v-model="filters.currency"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                @change="applyFilters"
              >
                <option value="">All Currencies</option>
                <option value="KES">KES</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
              </select>
            </div>

            <!-- Can Issue Loans Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Can Issue Loans</label>
              <select
                v-model="filters.canIssueLoans"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                @change="applyFilters"
              >
                <option value="">All</option>
                <option value="1">Yes</option>
                <option value="0">No</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </Transition>



    <!-- Clients Data Table -->
    <DataTable
      :data="clients"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Clients"
      row-key="client_id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <button
          @click="refreshData"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
      </template>

      <!-- Custom cell content -->
      <template #cell-client_status="{ value }">
        <span
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          :class="{
            'bg-green-100 text-green-800': value === '1',
            'bg-red-100 text-red-800': value === '0'
          }"
        >
          {{ value === '1' ? 'Active' : 'Inactive' }}
        </span>
      </template>

      <template #cell-total_loan_assets="{ value }">
        <span class="font-medium text-gray-900">
          {{ formatCurrency(value) }}
        </span>
      </template>

      <template #cell-open_date="{ value, item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ value }}{{ getDateSuffix(value) }}</div>
          <div class="text-gray-500">to {{ item.close_date }}{{ getDateSuffix(item.close_date) }}</div>
        </div>
      </template>

      <template #cell-b2c_paybill="{ value, item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">B2C: {{ value }}</div>
          <div class="text-gray-500">C2B: {{ item.c2b_paybill }}</div>
        </div>
      </template>

      <template #cell-created="{ value }">
        <span class="text-sm text-gray-500">
          {{ formatDateTime(value) }}
        </span>
      </template>

      <!-- Row Actions -->
      <template #actions="{ item, index }">
        <div class="relative">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Actions
            <EllipsisVerticalIcon class="h-4 w-4 ml-2" />
          </button>

          <!-- Dropdown Menu -->
          <Transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-if="showDropdown[index]"
              class="absolute right-0 z-10 mt-2 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5"
            >
              <div class="py-1">
                <!-- Edit Client -->
                <button
                  @click="editClient(item)"
                  class="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <PencilIcon class="h-4 w-4 mr-3" />
                  Edit Client
                </button>

                <!-- View Details -->
                <button
                  @click="viewClientDetails(item)"
                  class="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <EyeIcon class="h-4 w-4 mr-3" />
                  View Details
                </button>

              
              </div>
            </div>
          </Transition>
        </div>
      </template>
    </DataTable>

    <!-- Add Client Modal -->
    <Transition
      enter-active-class="transition ease-out duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition ease-in duration-200"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div v-if="showAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Add New Client</h3>

            <!-- Success Message -->
            <div v-if="successMessage" class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              {{ successMessage }}
            </div>

            <!-- Error Message -->
            <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {{ errorMessage }}
            </div>

            <form @submit.prevent="addClient">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Client Name *</label>
                  <input
                    v-model="newClient.client_name"
                    type="text"
                    :class="[
                      'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                      formErrors.client_name ? 'border-red-300' : 'border-gray-300'
                    ]"
                  />
                  <p v-if="formErrors.client_name" class="mt-1 text-sm text-red-600">{{ formErrors.client_name }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Phone Number *</label>
                  <input
                    v-model="newClient.client_phone"
                    type="tel"
                    :class="[
                      'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                      formErrors.client_phone ? 'border-red-300' : 'border-gray-300'
                    ]"
                  />
                  <p v-if="formErrors.client_phone" class="mt-1 text-sm text-red-600">{{ formErrors.client_phone }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Email Address *</label>
                  <input
                    v-model="newClient.client_email"
                    type="email"
                    :class="[
                      'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                      formErrors.client_email ? 'border-red-300' : 'border-gray-300'
                    ]"
                  />
                  <p v-if="formErrors.client_email" class="mt-1 text-sm text-red-600">{{ formErrors.client_email }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Address</label>
                  <textarea
                    v-model="newClient.client_address"
                    rows="3"
                    class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  ></textarea>
                </div>
              </div>
              <div class="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  @click="cancelAdd"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  :disabled="loading"
                  class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {{ loading ? 'Adding...' : 'Add Client' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Transition>

    <!-- Edit Client Modal -->
    <Transition
      enter-active-class="transition ease-out duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition ease-in duration-200"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Client</h3>

            <!-- Success Message -->
            <div v-if="successMessage" class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              {{ successMessage }}
            </div>

            <!-- Error Message -->
            <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {{ errorMessage }}
            </div>

            <form @submit.prevent="updateClient">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Client Account</label>
                  <input
                    v-model="editClientForm.client_account"
                    type="text"
                    disabled
                    class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 text-gray-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Client Name *</label>
                  <input
                    v-model="editClientForm.client_name"
                    type="text"
                    :class="[
                      'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                      formErrors.client_name ? 'border-red-300' : 'border-gray-300'
                    ]"
                  />
                  <p v-if="formErrors.client_name" class="mt-1 text-sm text-red-600">{{ formErrors.client_name }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Phone Number *</label>
                  <input
                    v-model="editClientForm.client_phone"
                    type="tel"
                    :class="[
                      'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                      formErrors.client_phone ? 'border-red-300' : 'border-gray-300'
                    ]"
                  />
                  <p v-if="formErrors.client_phone" class="mt-1 text-sm text-red-600">{{ formErrors.client_phone }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Email Address *</label>
                  <input
                    v-model="editClientForm.client_email"
                    type="email"
                    :class="[
                      'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                      formErrors.client_email ? 'border-red-300' : 'border-gray-300'
                    ]"
                  />
                  <p v-if="formErrors.client_email" class="mt-1 text-sm text-red-600">{{ formErrors.client_email }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Address</label>
                  <textarea
                    v-model="editClientForm.client_address"
                    rows="3"
                    class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  ></textarea>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Status</label>
                  <select
                    v-model="editClientForm.client_status"
                    class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                  </select>
                </div>
              </div>
              <div class="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  @click="cancelEdit"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  :disabled="loading"
                  class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {{ loading ? 'Updating...' : 'Update Client' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusIcon,
  BuildingOfficeIcon,
  CheckIcon,
  XMarkIcon,
  EllipsisVerticalIcon,
  BanknotesIcon,
  UserGroupIcon,
  EyeIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon
} from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import { clientsApi } from '@/services/clientsApi'
import type { ClientEntity, PaginationParams } from '@/services/types'

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)
const clients = ref<ClientEntity[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)
const showEditModal = ref(false)
const showDropdown = reactive<Record<number, boolean>>({})
const showSearch = ref(false)
const showFilters = ref(false)

// Filter state
const filters = reactive({
  status: '',
  account: '',
  clientName: '',
  contactInfo: '',
  currency: '',
  canIssueLoans: '',
  serviceFeeMin: '',
  serviceFeeMax: '',
  dateFrom: '',
  dateTo: ''
})

// New client form data
const newClient = ref<Partial<ClientEntity>>({
  client_name: '',
  client_phone: '',
  client_email: '',
  client_address: ''
})

// Edit client form data
const editingClient = ref<ClientEntity | null>(null)
const editClientForm = ref({
  client_id: '',
  client_account: '',
  client_name: '',
  client_phone: '',
  client_email: '',
  client_address: '',
  client_status: '1'
})

// Form validation and messages
const formErrors = ref<Record<string, string>>({})
const successMessage = ref('')
const errorMessage = ref('')

// Table headers configuration
const tableHeaders = {
  client_account: 'Account',
  client_name: 'Client Name',
  client_phone: 'Contact Info',
  total_loan_assets: 'Loan Assets',
  open_date: 'Operating Hours',
  b2c_paybill: 'PayBill Numbers',
  client_status: 'Status',
  created: 'Created Date'
}

// Computed properties
const activeCount = computed(() => {
  if (!Array.isArray(clients.value)) return 0
  return clients.value.filter(client => client.client_status === '1').length
})

const inactiveCount = computed(() => {
  if (!Array.isArray(clients.value)) return 0
  return clients.value.filter(client => client.client_status === '0').length
})

const canIssueLoanCount = computed(() => {
  if (!Array.isArray(clients.value)) return 0
  return clients.value.filter(client => client.can_issue_loans === '1').length
})

// Methods
const fetchClients = async (params: PaginationParams = {}) => {
  loading.value = true

  try {
    const requestParams = {
      page: currentPage.value,
      limit: pageSize.value,
      offset: (currentPage.value - 1) * pageSize.value,
      search: searchQuery.value,
      sortField: sortField.value,
      sortDirection: sortDirection.value,
      ...params
    }

    const response = await clientsApi.getClients(requestParams)
    console.log("response", JSON.stringify(response))

    if (response.status === 200) {
      // Handle the API response structure
      const responseData = response.message
      clients.value = Array.isArray(responseData.data) ? responseData.data : []
      totalRecords.value = parseInt(responseData.total_count.toString()) || 0
      currentPage.value = responseData.current_page || 1

      // Store clients in localStorage for access in ClientsView
      localStorage.setItem('clients', JSON.stringify(clients.value))
    } else {
      console.error('Failed to fetch clients:', response.message)
      clients.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching clients:', error)
    clients.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchClients()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  fetchClients()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  currentPage.value = 1
  fetchClients()
}

const handleRowClick = (item: ClientEntity) => {
  console.log('Row clicked:', item)
  // You can add navigation logic here
}

const refreshData = () => {
  fetchClients()
}

const toggleDropdown = (index: number) => {
  // Close all other dropdowns
  Object.keys(showDropdown).forEach(key => {
    if (parseInt(key) !== index) {
      showDropdown[parseInt(key)] = false
    }
  })
  showDropdown[index] = !showDropdown[index]
}

// Form validation
const validateAddForm = () => {
  formErrors.value = {}

  if (!newClient.value.client_name?.trim()) {
    formErrors.value.client_name = 'Client name is required'
  }

  if (!newClient.value.client_phone?.trim()) {
    formErrors.value.client_phone = 'Phone number is required'
  } else if (!/^[0-9+\-\s()]+$/.test(newClient.value.client_phone)) {
    formErrors.value.client_phone = 'Please enter a valid phone number'
  }

  if (!newClient.value.client_email?.trim()) {
    formErrors.value.client_email = 'Email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newClient.value.client_email)) {
    formErrors.value.client_email = 'Please enter a valid email address'
  }

  return Object.keys(formErrors.value).length === 0
}

const validateEditForm = () => {
  formErrors.value = {}

  if (!editClientForm.value.client_name?.trim()) {
    formErrors.value.client_name = 'Client name is required'
  }

  if (!editClientForm.value.client_phone?.trim()) {
    formErrors.value.client_phone = 'Phone number is required'
  } else if (!/^[0-9+\-\s()]+$/.test(editClientForm.value.client_phone)) {
    formErrors.value.client_phone = 'Please enter a valid phone number'
  }

  if (!editClientForm.value.client_email?.trim()) {
    formErrors.value.client_email = 'Email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editClientForm.value.client_email)) {
    formErrors.value.client_email = 'Please enter a valid email address'
  }

  return Object.keys(formErrors.value).length === 0
}

const addClient = async () => {
  errorMessage.value = ''
  successMessage.value = ''

  if (!validateAddForm()) {
    return
  }

  loading.value = true
  try {
    // Format phone number before sending
    const clientData = {
      ...newClient.value,
      client_phone: formatPhoneNumber(newClient.value.client_phone || '')
    }

    const response = await clientsApi.addClient(clientData)

    if (response.status === 200) {
      successMessage.value = 'Client added successfully!'
      showAddModal.value = false
      newClient.value = {
        client_name: '',
        client_phone: '',
        client_email: '',
        client_address: ''
      }
      formErrors.value = {}
      fetchClients()

      // Clear success message after 5 seconds
      setTimeout(() => {
        successMessage.value = ''
      }, 5000)
    } else {
      errorMessage.value = response.message || 'Failed to add client'
    }
  } catch (error: any) {
    errorMessage.value = error.message || 'An error occurred while adding the client'
  } finally {
    loading.value = false
  }
}

const editClient = (client: ClientEntity) => {
  editingClient.value = client
  editClientForm.value = {
    client_id: client.client_id,
    client_account: client.client_account,
    client_name: client.client_name,
    client_phone: client.client_phone,
    client_email: client.client_email,
    client_address: client.client_address || '',
    client_status: client.client_status
  }
  formErrors.value = {}
  errorMessage.value = ''
  successMessage.value = ''
  showEditModal.value = true
  showDropdown[clients.value.indexOf(client)] = false
}

const updateClient = async () => {
  errorMessage.value = ''
  successMessage.value = ''

  if (!validateEditForm()) {
    return
  }

  loading.value = true
  try {
    // Format phone number before sending
    const clientData = {
      ...editClientForm.value,
      client_phone: formatPhoneNumber(editClientForm.value.client_phone || '')
    }

    const response = await clientsApi.updateClient(clientData)

    if (response.status === 200) {
      successMessage.value = 'Client updated successfully!'
      showEditModal.value = false

      // Update the client in the local array
      const index = clients.value.findIndex(c => c.client_id === editClientForm.value.client_id)
      if (index !== -1) {
        clients.value[index] = { ...clients.value[index], ...clientData }
      }

      formErrors.value = {}
      editingClient.value = null

      // Clear success message after 5 seconds
      setTimeout(() => {
        successMessage.value = ''
      }, 5000)
    } else {
      errorMessage.value = response.message || 'Failed to update client'
    }
  } catch (error: any) {
    errorMessage.value = error.message || 'An error occurred while updating the client'
  } finally {
    loading.value = false
  }
}

const cancelEdit = () => {
  showEditModal.value = false
  editingClient.value = null
  formErrors.value = {}
  errorMessage.value = ''
  successMessage.value = ''
}

const cancelAdd = () => {
  showAddModal.value = false
  newClient.value = {
    client_name: '',
    client_phone: '',
    client_email: '',
    client_address: ''
  }
  formErrors.value = {}
  errorMessage.value = ''
  successMessage.value = ''
}

const toggleStatus = async (client: ClientEntity) => {
  const isCurrentlyActive = client.client_status === '1'

  try {
    let response
    if (isCurrentlyActive) {
      response = await clientsApi.deactivateClient(client.client_id)
    } else {
      response = await clientsApi.activateClient(client.client_id)
    }

    if (response.status === 200) {
      client.client_status = isCurrentlyActive ? '0' : '1'
      successMessage.value = `Client ${isCurrentlyActive ? 'deactivated' : 'activated'} successfully!`
      setTimeout(() => {
        successMessage.value = ''
      }, 3000)
    } else {
      errorMessage.value = response.message || 'Error updating client status'
      setTimeout(() => {
        errorMessage.value = ''
      }, 3000)
    }
  } catch (error: any) {
    errorMessage.value = error.message || 'Error updating client status'
    setTimeout(() => {
      errorMessage.value = ''
    }, 3000)
  }

  showDropdown[clients.value.indexOf(client)] = false
}

const viewTransactions = (client: ClientEntity) => {
  // Navigate to transactions for this client
  console.log('View transactions for:', client.client_name)
  showDropdown[clients.value.indexOf(client)] = false
}

const viewLoanProducts = (client: ClientEntity) => {
  // Navigate to loan products for this client
  console.log('View loan products for:', client.client_name)
  showDropdown[clients.value.indexOf(client)] = false
}

const viewSystemUsers = (client: ClientEntity) => {
  // Navigate to system users for this client
  console.log('View system users for:', client.client_name)
  showDropdown[clients.value.indexOf(client)] = false
}

const viewClientDetails = (client: ClientEntity) => {
  // Navigate to client details view
  console.log('View details for:', client.client_name)
  showDropdown[clients.value.indexOf(client)] = false
  // Navigate to the client view page using router
  router.push({ name: 'clients-view', params: { id: client.client_id } })
}

const deleteClient = async (client: ClientEntity) => {
  const clientIndex = clients.value.indexOf(client)
  showDropdown[clientIndex] = false

  // Show confirmation dialog
  if (!confirm(`Are you sure you want to delete ${client.client_name}? This action cannot be undone.`)) {
    return
  }

  try {
    const response = await clientsApi.deleteClient(client.client_id)

    if (response.status === 200) {
      successMessage.value = 'Client deleted successfully!'
      fetchClients() // Refresh the list
      setTimeout(() => {
        successMessage.value = ''
      }, 3000)
    } else {
      errorMessage.value = response.message || 'Failed to delete client'
      setTimeout(() => {
        errorMessage.value = ''
      }, 3000)
    }
  } catch (error: any) {
    errorMessage.value = error.message || 'Error deleting client'
    setTimeout(() => {
      errorMessage.value = ''
    }, 3000)
  }
}

// Search and Filter functions
let searchTimeout: number

const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    handleSearch(searchQuery.value)
  }, 300)
}

const clearSearch = () => {
  searchQuery.value = ''
  handleSearch('')
}

let filterTimeout: number

const debouncedFilter = () => {
  clearTimeout(filterTimeout)
  filterTimeout = setTimeout(() => {
    applyFilters()
  }, 300)
}

const applyFilters = () => {
  // Apply filters to the current search
  fetchClients()
}

const clearFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = ''
  })
  fetchClients()
}

// Utility functions
const formatCurrency = (amount: string | number) => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES'
  }).format(numAmount || 0)
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-KE', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getDateSuffix = (day: string): string => {
  const dayNum = parseInt(day)
  if (dayNum >= 11 && dayNum <= 13) {
    return 'th'
  }
  switch (dayNum % 10) {
    case 1: return 'st'
    case 2: return 'nd'
    case 3: return 'rd'
    default: return 'th'
  }
}

const formatPhoneNumber = (phone: string) => {
  // Remove any non-digit characters
  const cleaned = phone.replace(/\D/g, '')

  // Add Kenya country code if not present
  if (cleaned.length === 9 && cleaned.startsWith('7')) {
    return `254${cleaned}`
  } else if (cleaned.length === 10 && cleaned.startsWith('07')) {
    return `254${cleaned.substring(1)}`
  } else if (cleaned.length === 12 && cleaned.startsWith('254')) {
    return cleaned
  }

  return phone // Return original if format is unclear
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    Object.keys(showDropdown).forEach(key => {
      showDropdown[parseInt(key)] = false
    })
  }
}

onMounted(() => {
  fetchClients()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
