<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Bulk SMS</h1>
        <p class="mt-1 text-sm text-gray-500">
          Send bulk SMS messages to organization customers
        </p>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="successMessage" class="p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
      {{ successMessage }}
    </div>
    
    <!-- Error Message -->
    <div v-if="errorMessage" class="p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
      {{ errorMessage }}
    </div>

    <!-- Organization Selection -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Select Organization</h3>
      </div>
      
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Organization</label>
            <select
              v-model="selectedOrganization"
              class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select an organization</option>
              <option 
                v-for="org in organizations" 
                :key="org.client_id" 
                :value="org"
              >
                {{ org.client_name }} ({{ org.client_account }})
              </option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- SMS Composition -->
    <div v-if="selectedOrganization" class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Compose SMS</h3>
      </div>
      
      <form @submit.prevent="sendBulkSMS" class="p-6 space-y-6">
        <!-- Message Type -->
        <div>
          <label class="block text-sm font-medium text-gray-700">Message Type</label>
          <select
            v-model="smsForm.message_type"
            class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="promotional">Promotional</option>
            <option value="transactional">Transactional</option>
            <option value="reminder">Reminder</option>
            <option value="announcement">Announcement</option>
          </select>
        </div>

        <!-- Target Audience -->
        <div>
          <label class="block text-sm font-medium text-gray-700">Target Audience</label>
          <select
            v-model="smsForm.target_audience"
            class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Customers</option>
            <option value="active">Active Customers Only</option>
            <option value="inactive">Inactive Customers</option>
            <option value="loan_holders">Loan Holders</option>
            <option value="high_value">High Value Customers</option>
          </select>
        </div>

        <!-- Message Content -->
        <div>
          <label class="block text-sm font-medium text-gray-700">Message Content</label>
          <textarea
            v-model="smsForm.message"
            rows="6"
            maxlength="160"
            :class="[
              'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
              formErrors.message ? 'border-red-300' : 'border-gray-300'
            ]"
            placeholder="Enter your SMS message here..."
          ></textarea>
          <div class="flex justify-between mt-1">
            <p v-if="formErrors.message" class="text-sm text-red-600">{{ formErrors.message }}</p>
            <p class="text-sm text-gray-500">{{ smsForm.message.length }}/160 characters</p>
          </div>
        </div>

        <!-- Sender ID -->
        <div>
          <label class="block text-sm font-medium text-gray-700">Sender ID</label>
          <input
            v-model="smsForm.sender_id"
            type="text"
            maxlength="11"
            :class="[
              'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
              formErrors.sender_id ? 'border-red-300' : 'border-gray-300'
            ]"
            placeholder="SALOPLUS"
          />
          <p v-if="formErrors.sender_id" class="mt-1 text-sm text-red-600">{{ formErrors.sender_id }}</p>
          <p class="mt-1 text-sm text-gray-500">Maximum 11 characters, alphanumeric only</p>
        </div>

        <!-- Schedule Options -->
        <div>
          <label class="block text-sm font-medium text-gray-700">Delivery Options</label>
          <div class="mt-2 space-y-2">
            <div class="flex items-center">
              <input
                id="send_now"
                v-model="smsForm.delivery_option"
                type="radio"
                value="now"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <label for="send_now" class="ml-2 block text-sm text-gray-900">
                Send Now
              </label>
            </div>
            <div class="flex items-center">
              <input
                id="schedule"
                v-model="smsForm.delivery_option"
                type="radio"
                value="schedule"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <label for="schedule" class="ml-2 block text-sm text-gray-900">
                Schedule for Later
              </label>
            </div>
          </div>
        </div>

        <!-- Schedule Date/Time -->
        <div v-if="smsForm.delivery_option === 'schedule'">
          <label class="block text-sm font-medium text-gray-700">Schedule Date & Time</label>
          <input
            v-model="smsForm.scheduled_time"
            type="datetime-local"
            :min="minDateTime"
            class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <!-- Preview -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-sm font-medium text-gray-900 mb-2">Preview</h4>
          <div class="bg-white border rounded-lg p-3">
            <div class="text-xs text-gray-500 mb-1">From: {{ smsForm.sender_id || 'SALOPLUS' }}</div>
            <div class="text-sm text-gray-900">{{ smsForm.message || 'Your message will appear here...' }}</div>
          </div>
        </div>

        <!-- Estimated Cost -->
        <div class="bg-blue-50 rounded-lg p-4">
          <h4 class="text-sm font-medium text-blue-900 mb-2">Estimated Cost</h4>
          <div class="text-sm text-blue-800">
            <p>Recipients: {{ estimatedRecipients }} customers</p>
            <p>Cost per SMS: KES 1.00</p>
            <p class="font-medium">Total Estimated Cost: KES {{ estimatedRecipients }}</p>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="resetForm"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            Reset
          </button>
          <button
            type="submit"
            :disabled="loading || !smsForm.message.trim()"
            class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {{ loading ? 'Sending...' : (smsForm.delivery_option === 'schedule' ? 'Schedule SMS' : 'Send SMS') }}
          </button>
        </div>
      </form>
    </div>

    <!-- SMS History -->
    <div v-if="selectedOrganization" class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Recent SMS History</h3>
      </div>
      
      <div class="p-6">
        <div class="text-sm text-gray-500">
          SMS history will be displayed here...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { organizationsApi } from '@/services/organizationsApi'
import type { Organization } from '@/services/types'

// Reactive data
const loading = ref(false)
const successMessage = ref('')
const errorMessage = ref('')
const organizations = ref<Organization[]>([])
const selectedOrganization = ref<Organization | null>(null)

// SMS form
const smsForm = reactive({
  message_type: 'promotional',
  target_audience: 'all',
  message: '',
  sender_id: 'SALOPLUS',
  delivery_option: 'now',
  scheduled_time: ''
})

// Form validation
const formErrors = ref<Record<string, string>>({})

// Computed properties
const estimatedRecipients = computed(() => {
  // This would be calculated based on the selected organization and target audience
  return selectedOrganization.value ? 100 : 0
})

const minDateTime = computed(() => {
  const now = new Date()
  now.setMinutes(now.getMinutes() + 30) // Minimum 30 minutes from now
  return now.toISOString().slice(0, 16)
})

// Load organizations
const loadOrganizations = async () => {
  try {
    const response = await organizationsApi.getOrganizations({ limit: 100 })
    if (response.status === 200) {
      organizations.value = response.message.data?.filter(org => org.client_status === 1) || []
    }
  } catch (error) {
    console.error('Error loading organizations:', error)
  }
}

// Validate form
const validateForm = () => {
  formErrors.value = {}
  
  if (!smsForm.message.trim()) {
    formErrors.value.message = 'Message is required'
  } else if (smsForm.message.length > 160) {
    formErrors.value.message = 'Message cannot exceed 160 characters'
  }
  
  if (!smsForm.sender_id.trim()) {
    formErrors.value.sender_id = 'Sender ID is required'
  } else if (!/^[A-Za-z0-9]+$/.test(smsForm.sender_id)) {
    formErrors.value.sender_id = 'Sender ID must be alphanumeric only'
  } else if (smsForm.sender_id.length > 11) {
    formErrors.value.sender_id = 'Sender ID cannot exceed 11 characters'
  }
  
  return Object.keys(formErrors.value).length === 0
}

// Send bulk SMS
const sendBulkSMS = async () => {
  if (!selectedOrganization.value) return
  
  errorMessage.value = ''
  successMessage.value = ''
  
  if (!validateForm()) {
    return
  }

  loading.value = true
  
  try {
    // This would call a bulk SMS API endpoint
    // For now, we'll simulate the API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    successMessage.value = `SMS ${smsForm.delivery_option === 'schedule' ? 'scheduled' : 'sent'} successfully to ${estimatedRecipients.value} recipients!`
    
    // Reset form after successful send
    if (smsForm.delivery_option === 'now') {
      resetForm()
    }
    
    setTimeout(() => { successMessage.value = '' }, 5000)
  } catch (error: any) {
    errorMessage.value = error.message || 'An error occurred while sending SMS'
  } finally {
    loading.value = false
  }
}

// Reset form
const resetForm = () => {
  smsForm.message = ''
  smsForm.message_type = 'promotional'
  smsForm.target_audience = 'all'
  smsForm.sender_id = 'SALOPLUS'
  smsForm.delivery_option = 'now'
  smsForm.scheduled_time = ''
  formErrors.value = {}
}

onMounted(() => {
  loadOrganizations()
})
</script>
