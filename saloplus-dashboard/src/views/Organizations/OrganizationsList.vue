<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Organizations</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage and view all registered organizations
        </p>
      </div>
      <router-link
        :to="{ name: 'organisations-add' }"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
      >
        <PlusIcon class="h-4 w-4 mr-2" />
        Add Organization
      </router-link>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <BuildingOfficeIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Organizations</dt>
                <dd class="text-lg font-medium text-gray-900">{{ totalRecords }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                <div class="h-3 w-3 bg-green-500 rounded-full"></div>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Active</dt>
                <dd class="text-lg font-medium text-gray-900">{{ activeCount }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 bg-red-100 rounded-full flex items-center justify-center">
                <div class="h-3 w-3 bg-red-500 rounded-full"></div>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Inactive</dt>
                <dd class="text-lg font-medium text-gray-900">{{ inactiveCount }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center">
                <div class="h-3 w-3 bg-blue-500 rounded-full"></div>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Can Issue Loans</dt>
                <dd class="text-lg font-medium text-gray-900">{{ canIssueLoanCount }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Network Debugger (Development Only) -->
    <NetworkDebugger v-if="isDev" />

    <!-- Organizations Data Table -->
    <DataTable
      :data="organizations"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Organizations"
      row-key="client_id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <button
          @click="refreshData"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
      </template>

      <!-- Custom Cell: Client Account -->
      <template #cell-client_account="{ value }">
        <div class="font-medium text-gray-900">{{ value }}</div>
      </template>

      <!-- Custom Cell: Client Name -->
      <template #cell-client_name="{ value }">
        <div class="font-medium text-blue-600">{{ value }}</div>
      </template>

      <!-- Custom Cell: Contact Info -->
      <template #cell-client_phone="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">+{{ item.client_phone }}</div>
          <div class="text-gray-500">{{ item.client_email }}</div>
        </div>
      </template>

      <!-- Custom Cell: Loan Assets -->
      <template #cell-total_loan_assets="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">
            {{ item.currency_code }} {{ formatCurrency(item.total_loan_assets) }}
          </div>
          <div class="text-xs" :class="item.can_issue_loans === '1' ? 'text-green-600' : 'text-orange-600'">
            {{ item.can_issue_loans_desc }} loans
          </div>
        </div>
      </template>

      <!-- Custom Cell: Operating Hours -->
      <template #cell-open_date="{ item }">
        <div class="text-sm text-gray-900">
          {{ item.open_date }}{{ getDateSuffix(item.open_date) }} - {{ item.close_date }}{{ getDateSuffix(item.close_date) }}
        </div>
      </template>

      <!-- Custom Cell: PayBills -->
      <template #cell-b2c_paybill="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">B2C: {{ item.b2c_paybill }}</div>
          <div class="text-gray-500">C2B: {{ item.c2b_paybill }}</div>
        </div>
      </template>

      <!-- Custom Cell: Status -->
      <template #cell-client_status="{ value }">
        <span :class="[
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          getStatusClass(value)
        ]">
          {{ getStatusText(value) }}
        </span>
      </template>

      <!-- Custom Cell: Created Date -->
      <template #cell-created="{ value }">
        <div class="text-sm text-gray-900">
          {{ formatDateTime(value) }}
        </div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <div class="relative">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
            </svg>
          </button>

          <!-- Dropdown Menu -->
          <div
            v-if="showDropdown[index]"
            class="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10"
          >
            <div class="py-1">
              <button
                v-if="item.client_status !== 1"
                @click="activateOrganization(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"
              >
                Activate {{ item.client_name }}
              </button>
              <button
                v-else
                @click="deactivateOrganization(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"
              >
                Deactivate {{ item.client_name }}
              </button>
              <button
                @click="viewLoanAccounts(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                View Loan Accounts
              </button>
              <button
                @click="viewLoanRequests(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                View Loan Requests
              </button>
              <button
                @click="viewLoanLimits(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                View Loan Limits
              </button>
              <button
                @click="viewTransactions(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                View Transactions
              </button>
              <button
                @click="viewLoanProducts(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                View Loan Products
              </button>
              <button
                @click="viewSystemUsers(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                View System Users
              </button>
              <button
                v-if="item.client_status === 1"
                @click="sendBulkSMS(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                Send Bulk SMS
              </button>
            </div>
          </div>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, reactive } from 'vue'
import { PlusIcon, BuildingOfficeIcon } from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import NetworkDebugger from '@/components/NetworkDebugger.vue'
import { organizationsApi } from '@/services/organizationsApi'
import type { Organization, PaginationParams } from '@/services/types'

// Reactive data
const loading = ref(false)
const organizations = ref<Organization[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)
const showDropdown = reactive<Record<number, boolean>>({})

// Table headers configuration
const tableHeaders = {
  client_account: 'Account',
  client_name: 'Organization Name',
  client_phone: 'Contact Info',
  total_loan_assets: 'Loan Assets',
  open_date: 'Operating Hours',
  b2c_paybill: 'PayBill Numbers',
  client_status: 'Status',
  created: 'Created Date'
}

// Computed properties
const activeCount = computed(() =>
  organizations.value.filter(org => org.client_status === 1).length
)

const inactiveCount = computed(() =>
  organizations.value.filter(org => org.client_status === 0).length
)

const canIssueLoanCount = computed(() =>
  organizations.value.filter(org => org.can_issue_loans === '1').length
)

// Development mode check
const isDev = import.meta.env.DEV

// Methods
const fetchOrganizations = async (params: PaginationParams = {}) => {
  loading.value = true

  try {
    const requestParams = {
      page: currentPage.value,
      limit: pageSize.value,
      offset: (currentPage.value - 1) * pageSize.value,
      search: searchQuery.value,
      sortField: sortField.value,
      sortDirection: sortDirection.value,
      ...params
    }

    const response = await organizationsApi.getOrganizations(requestParams)

    if (response.status === 200) {
      organizations.value = response.message.data || []
      totalRecords.value = response.message.total_count || 0
      currentPage.value = response.message.current_page || 1
    } else {
      console.error('Failed to fetch organizations:', response.message)
      organizations.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching organizations:', error)
    organizations.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchOrganizations()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  fetchOrganizations()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  currentPage.value = 1
  fetchOrganizations()
}

const handleRowClick = (item: Organization) => {
  console.log('Row clicked:', item)
  // You can add navigation logic here
}

const refreshData = () => {
  fetchOrganizations()
}

const toggleDropdown = (index: number) => {
  // Close all other dropdowns
  Object.keys(showDropdown).forEach(key => {
    if (parseInt(key) !== index) {
      showDropdown[parseInt(key)] = false
    }
  })

  // Toggle current dropdown
  showDropdown[index] = !showDropdown[index]
}

const activateOrganization = async (organization: Organization) => {
  try {
    loading.value = true
    const response = await organizationsApi.updateOrganization({
      client_account: organization.client_account,
      client_status: 1
    })

    if (response.status === 200) {
      await fetchOrganizations()
      alert('Organization activated successfully')
    } else {
      alert('Failed to activate organization: ' + response.message)
    }
  } catch (error) {
    console.error('Error activating organization:', error)
    alert('Error activating organization')
  } finally {
    loading.value = false
    showDropdown[organizations.value.indexOf(organization)] = false
  }
}

const deactivateOrganization = async (organization: Organization) => {
  try {
    loading.value = true
    const response = await organizationsApi.updateOrganization({
      client_account: organization.client_account,
      client_status: 0
    })

    if (response.status === 200) {
      await fetchOrganizations()
      alert('Organization deactivated successfully')
    } else {
      alert('Failed to deactivate organization: ' + response.message)
    }
  } catch (error) {
    console.error('Error deactivating organization:', error)
    alert('Error deactivating organization')
  } finally {
    loading.value = false
    showDropdown[organizations.value.indexOf(organization)] = false
  }
}

// Navigation methods
const viewLoanAccounts = (organization: Organization) => {
  // Navigate to loan accounts for this organization
  console.log('View loan accounts for:', organization.client_name)
  showDropdown[organizations.value.indexOf(organization)] = false
}

const viewLoanRequests = (organization: Organization) => {
  // Navigate to loan requests for this organization
  console.log('View loan requests for:', organization.client_name)
  showDropdown[organizations.value.indexOf(organization)] = false
}

const viewLoanLimits = (organization: Organization) => {
  // Navigate to loan limits for this organization
  console.log('View loan limits for:', organization.client_name)
  showDropdown[organizations.value.indexOf(organization)] = false
}

const viewTransactions = (organization: Organization) => {
  // Navigate to transactions for this organization
  console.log('View transactions for:', organization.client_name)
  showDropdown[organizations.value.indexOf(organization)] = false
}

const viewLoanProducts = (organization: Organization) => {
  // Navigate to loan products for this organization
  console.log('View loan products for:', organization.client_name)
  showDropdown[organizations.value.indexOf(organization)] = false
}

const viewSystemUsers = (organization: Organization) => {
  // Navigate to system users for this organization
  console.log('View system users for:', organization.client_name)
  showDropdown[organizations.value.indexOf(organization)] = false
}

const sendBulkSMS = (organization: Organization) => {
  // Navigate to bulk SMS for this organization
  console.log('Send bulk SMS for:', organization.client_name)
  showDropdown[organizations.value.indexOf(organization)] = false
}

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

const getDateSuffix = (hour: string): string => {
  const hourNum = parseInt(hour)
  if (hourNum === 0) return ' AM'
  if (hourNum < 12) return ' AM'
  if (hourNum === 12) return ' PM'
  return ' PM'
}

const getStatusClass = (status: number): string => {
  switch (status) {
    case 1:
      return 'bg-green-100 text-green-800'
    case 0:
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: number): string => {
  switch (status) {
    case 1:
      return 'Active'
    case 0:
      return 'Inactive'
    default:
      return 'Unknown'
  }
}

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
}

// Close dropdown when clicking outside
const closeDropdowns = () => {
  Object.keys(showDropdown).forEach(key => {
    showDropdown[parseInt(key)] = false
  })
}

// Lifecycle hooks
onMounted(() => {
  fetchOrganizations()

  // Add click listener to close dropdowns when clicking outside
  document.addEventListener('click', closeDropdowns)
})



// Cleanup
onUnmounted(() => {
  document.removeEventListener('click', closeDropdowns)
})
</script>
