<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Add Organization</h1>
        <p class="mt-1 text-sm text-gray-500">
          Create a new organization in the system
        </p>
      </div>
      <router-link
        :to="{ name: 'organisations' }"
        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        ← Back to Organizations
      </router-link>
    </div>

    <!-- Success Message -->
    <div v-if="successMessage" class="p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
      {{ successMessage }}
    </div>
    
    <!-- Error Message -->
    <div v-if="errorMessage" class="p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
      {{ errorMessage }}
    </div>

    <!-- Add Organization Form -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Organization Details</h3>
      </div>
      
      <form @submit.prevent="addOrganization" class="p-6 space-y-6">
        <!-- Company Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700">Company Name *</label>
          <input
            v-model="form.company_name"
            type="text"
            required
            :class="[
              'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
              formErrors.company_name ? 'border-red-300' : 'border-gray-300'
            ]"
            placeholder="MKenya LTD"
          />
          <p v-if="formErrors.company_name" class="mt-1 text-sm text-red-600">{{ formErrors.company_name }}</p>
        </div>

        <!-- Company Email and Phone -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Company Email *</label>
            <input
              v-model="form.company_email"
              type="email"
              required
              :class="[
                'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                formErrors.company_email ? 'border-red-300' : 'border-gray-300'
              ]"
              placeholder="<EMAIL>"
            />
            <p v-if="formErrors.company_email" class="mt-1 text-sm text-red-600">{{ formErrors.company_email }}</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700">Company Phone Number *</label>
            <input
              v-model="form.company_phone"
              type="tel"
              required
              :class="[
                'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                formErrors.company_phone ? 'border-red-300' : 'border-gray-300'
              ]"
              placeholder="07xx xxx xxx"
            />
            <p v-if="formErrors.company_phone" class="mt-1 text-sm text-red-600">{{ formErrors.company_phone }}</p>
          </div>
        </div>

        <!-- Company Address and Contact Person -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Company Address *</label>
            <input
              v-model="form.company_address"
              type="text"
              required
              :class="[
                'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                formErrors.company_address ? 'border-red-300' : 'border-gray-300'
              ]"
              placeholder="**********, Nairobi"
            />
            <p v-if="formErrors.company_address" class="mt-1 text-sm text-red-600">{{ formErrors.company_address }}</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700">Contact Person (Phone No) *</label>
            <input
              v-model="form.contact_person"
              type="text"
              required
              :class="[
                'mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500',
                formErrors.contact_person ? 'border-red-300' : 'border-gray-300'
              ]"
              placeholder="07xx xxx xxx"
            />
            <p v-if="formErrors.contact_person" class="mt-1 text-sm text-red-600">{{ formErrors.contact_person }}</p>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <router-link
            :to="{ name: 'organisations' }"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            Cancel
          </router-link>
          <button
            type="submit"
            :disabled="loading"
            class="px-8 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {{ loading ? 'Adding...' : 'Add Organization' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { organizationsApi } from '@/services/organizationsApi'

const router = useRouter()

// Reactive data
const loading = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

// Form data
const form = reactive({
  dial_code: '254',
  company_name: '',
  company_phone: '',
  company_address: '',
  company_email: '',
  contact_person: ''
})

// Form validation
const formErrors = ref<Record<string, string>>({})

// Validation function
const validateForm = () => {
  formErrors.value = {}
  
  if (!form.company_name?.trim()) {
    formErrors.value.company_name = 'Company name is required'
  }
  
  if (!form.company_email?.trim()) {
    formErrors.value.company_email = 'Company email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.company_email)) {
    formErrors.value.company_email = 'Please enter a valid email address'
  }
  
  if (!form.company_phone?.trim()) {
    formErrors.value.company_phone = 'Company phone is required'
  }
  
  if (!form.company_address?.trim()) {
    formErrors.value.company_address = 'Company address is required'
  }
  
  if (!form.contact_person?.trim()) {
    formErrors.value.contact_person = 'Contact person is required'
  }
  
  return Object.keys(formErrors.value).length === 0
}

// Add organization function
const addOrganization = async () => {
  errorMessage.value = ''
  successMessage.value = ''
  
  if (!validateForm()) {
    return
  }

  loading.value = true
  
  try {
    const response = await organizationsApi.addOrganization(form)
    
    if (response.status === 200) {
      successMessage.value = 'Organization added successfully!'
      
      // Reset form
      Object.keys(form).forEach(key => {
        if (key !== 'dial_code') {
          form[key as keyof typeof form] = ''
        }
      })
      
      formErrors.value = {}
      
      // Redirect after 2 seconds
      setTimeout(() => {
        router.push({ name: 'organisations' })
      }, 2000)
    } else {
      errorMessage.value = response.message || 'Failed to add organization'
    }
  } catch (error: any) {
    errorMessage.value = error.message || 'An error occurred while adding the organization'
  } finally {
    loading.value = false
  }
}
</script>
