<template>
  <span :class="displayClasses">
    <span v-if="showSymbol && symbolPosition === 'before'" class="currency-symbol">
      {{ currencySymbol }}
    </span>
    <span class="amount">{{ formattedAmount }}</span>
    <span v-if="showSymbol && symbolPosition === 'after'" class="currency-symbol">
      {{ currencySymbol }}
    </span>
  </span>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue'

// Props
const props = defineProps<{
  amount: number | string
  currency?: string
  locale?: string
  showSymbol?: boolean
  symbolPosition?: 'before' | 'after'
  decimals?: number
  showSign?: boolean
  colorCode?: boolean
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl'
  weight?: 'normal' | 'medium' | 'semibold' | 'bold'
  align?: 'left' | 'center' | 'right'
}>()

// Currency symbols mapping
const currencySymbols = {
  KES: 'KSh',
  USD: '$',
  EUR: '€',
  GBP: '£',
  UGX: 'USh',
  TZS: 'TSh',
  RWF: 'RWF',
  ETB: 'Br'
}

// Computed properties
const numericAmount = computed(() => {
  const num = typeof props.amount === 'string' ? parseFloat(props.amount) : props.amount
  return isNaN(num) ? 0 : num
})

const currencySymbol = computed(() => {
  const currency = props.currency || 'KES'
  return currencySymbols[currency] || currency
})

const formattedAmount = computed(() => {
  const amount = numericAmount.value
  const decimals = props.decimals !== undefined ? props.decimals : 2
  const locale = props.locale || 'en-KE'
  
  try {
    // Format the number with proper locale and decimal places
    const formatted = new Intl.NumberFormat(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(Math.abs(amount))
    
    // Add sign if needed
    if (props.showSign && amount !== 0) {
      return amount > 0 ? `+${formatted}` : `-${formatted}`
    }
    
    return amount < 0 ? `-${formatted}` : formatted
  } catch (error) {
    // Fallback formatting
    return amount.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }
})

const sizeClasses = computed(() => {
  const sizeMap = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  }
  return sizeMap[props.size || 'base']
})

const weightClasses = computed(() => {
  const weightMap = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold'
  }
  return weightMap[props.weight || 'medium']
})

const alignClasses = computed(() => {
  const alignMap = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  }
  return alignMap[props.align || 'left']
})

const colorClasses = computed(() => {
  if (!props.colorCode) return 'text-gray-900'
  
  const amount = numericAmount.value
  if (amount > 0) return 'text-green-600'
  if (amount < 0) return 'text-red-600'
  return 'text-gray-500'
})

const displayClasses = computed(() => {
  return [
    'inline-flex items-baseline',
    sizeClasses.value,
    weightClasses.value,
    alignClasses.value,
    colorClasses.value
  ].join(' ')
})
</script>

<style scoped>
.currency-symbol {
  @apply mr-0.5;
}

.amount {
  @apply tabular-nums;
}

/* Ensure proper spacing for after symbol */
.currency-symbol:last-child {
  @apply ml-0.5 mr-0;
}
</style>
