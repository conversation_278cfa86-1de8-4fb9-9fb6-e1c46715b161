<template>
  <div v-if="isOpen" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="handleBackdropClick">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- Icon -->
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full" :class="iconBgClass">
          <component :is="iconComponent" class="h-6 w-6" :class="iconClass" />
        </div>
        
        <!-- Title -->
        <div class="mt-3 text-center">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            {{ title }}
          </h3>
          
          <!-- Message -->
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500">
              {{ message }}
            </p>
          </div>
          
          <!-- Actions -->
          <div class="flex justify-center space-x-3 px-4 py-3">
            <button
              @click="handleCancel"
              class="px-4 py-2 bg-white text-gray-500 border border-gray-300 rounded-md text-sm font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              {{ cancelText }}
            </button>
            <button
              @click="handleConfirm"
              :disabled="loading"
              class="px-4 py-2 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50"
              :class="confirmButtonClass"
            >
              <div v-if="loading" class="flex items-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {{ loadingText }}
              </div>
              <span v-else>{{ confirmText }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue'
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  QuestionMarkCircleIcon
} from '@heroicons/vue/24/outline'

// Props
const props = defineProps<{
  isOpen: boolean
  title?: string
  message?: string
  type?: 'warning' | 'danger' | 'success' | 'info' | 'question'
  confirmText?: string
  cancelText?: string
  loadingText?: string
  loading?: boolean
  closeOnBackdrop?: boolean
}>()

// Emits
const emit = defineEmits<{
  confirm: []
  cancel: []
  close: []
}>()

// Computed properties
const iconComponent = computed(() => {
  const iconMap = {
    warning: ExclamationTriangleIcon,
    danger: XCircleIcon,
    success: CheckCircleIcon,
    info: InformationCircleIcon,
    question: QuestionMarkCircleIcon
  }
  return iconMap[props.type || 'question']
})

const iconClass = computed(() => {
  const classMap = {
    warning: 'text-yellow-600',
    danger: 'text-red-600',
    success: 'text-green-600',
    info: 'text-blue-600',
    question: 'text-gray-600'
  }
  return classMap[props.type || 'question']
})

const iconBgClass = computed(() => {
  const classMap = {
    warning: 'bg-yellow-100',
    danger: 'bg-red-100',
    success: 'bg-green-100',
    info: 'bg-blue-100',
    question: 'bg-gray-100'
  }
  return classMap[props.type || 'question']
})

const confirmButtonClass = computed(() => {
  const classMap = {
    warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
    danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
    success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
    info: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
    question: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
  }
  return classMap[props.type || 'question']
})

// Methods
const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}

const handleBackdropClick = (event: Event) => {
  if (props.closeOnBackdrop !== false && event.target === event.currentTarget) {
    emit('close')
  }
}

// Default values
const title = computed(() => props.title || 'Confirm Action')
const message = computed(() => props.message || 'Are you sure you want to proceed?')
const confirmText = computed(() => props.confirmText || 'Confirm')
const cancelText = computed(() => props.cancelText || 'Cancel')
const loadingText = computed(() => props.loadingText || 'Processing...')
</script>
