import {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
  Cog6ToothIcon
} from '@heroicons/vue/24/outline'

export interface TableAction {
  key: string
  label: string
  icon: any
  class?: string
  condition?: (item: any) => boolean
}

export const defaultActions: Record<string, TableAction> = {
  view: {
    key: 'view',
    label: 'View Details',
    icon: EyeIcon
  },
  edit: {
    key: 'edit',
    label: 'Edit',
    icon: PencilIcon
  },
  delete: {
    key: 'delete',
    label: 'Delete',
    icon: TrashIcon,
    class: 'text-red-600 hover:bg-red-50'
  },
  approve: {
    key: 'approve',
    label: 'Approve',
    icon: CheckIcon,
    class: 'text-green-600 hover:bg-green-50'
  },
  reject: {
    key: 'reject',
    label: 'Reject',
    icon: XMarkIcon,
    class: 'text-red-600 hover:bg-red-50'
  },
  download: {
    key: 'download',
    label: 'Download',
    icon: ArrowDownTrayIcon
  },
  settings: {
    key: 'settings',
    label: 'Settings',
    icon: Cog6ToothIcon
  }
}
