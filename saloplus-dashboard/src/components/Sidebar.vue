<template>
  <!-- Sidebar -->
  <aside
    class="bg-gradient-to-b from-slate-800 to-slate-900 shadow-xl sidebar-transition flex flex-col"
    :class="[
      sidebarStore.sidebarWidth,
      {
        'fixed inset-y-0 left-0 z-50': sidebarStore.isMobile,
        'fixed inset-y-0 left-0 z-30': !sidebarStore.isMobile,
        'translate-x-0': sidebarStore.isMobile ? sidebarStore.isOpen : true,
        '-translate-x-full': sidebarStore.isMobile && !sidebarStore.isOpen
      }
    ]"
  >
    <!-- Sidebar Header -->
    <div class="relative overflow-hidden flex-shrink-0">
      <!-- Background Pattern -->
      <div class="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-600/20"></div>
      <div class="absolute inset-0 opacity-30 sidebar-pattern"></div>

      <!-- Header Content -->
      <div class="relative z-10 px-4 py-6 border-b border-white/10">
        <div class="flex items-center space-x-3">
          <!-- Logo Icon -->
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <span class="text-white font-bold text-lg">S</span>
            </div>
          </div>

          <!-- Brand Text -->
          <Transition
            enter-active-class="transition-all duration-300"
            enter-from-class="opacity-0 transform translate-x-4"
            enter-to-class="opacity-100 transform translate-x-0"
            leave-active-class="transition-all duration-300"
            leave-from-class="opacity-100 transform translate-x-0"
            leave-to-class="opacity-0 transform translate-x-4"
          >
            <div v-if="sidebarStore.isOpen || sidebarStore.isMobile" class="flex-1 min-w-0">
              <h1 class="text-xl font-bold text-white tracking-tight">
                SaloPlus
                <sup class="text-xs text-blue-300 ml-1">+</sup>
              </h1>
              <p class="text-xs text-slate-300 mt-1">Financial Management</p>
            </div>
          </Transition>
        </div>
      </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
      <!-- Dashboard -->
      <router-link
        :to="{ name: 'dashboard' }"
        class="sidebar-item group"
        :class="{ 'sidebar-item-active': $route.name === 'dashboard' }"
        @click="handleMenuClick"
      >
        <HomeIcon class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" />
        <Transition
          enter-active-class="transition-all duration-300"
          enter-from-class="opacity-0"
          enter-to-class="opacity-100"
          leave-active-class="transition-all duration-300"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
        >
          <span v-if="sidebarStore.isOpen || sidebarStore.isMobile">Dashboard</span>
        </Transition>
        
        <!-- Tooltip for collapsed state -->
        <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
             class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
          Dashboard
        </div>
      </router-link>

      <!-- Clients Section (formerly Organizations) -->
      <div class="space-y-1">
        <div v-if="sidebarStore.isOpen || sidebarStore.isMobile"
             class="px-2 py-2 text-xs font-semibold text-slate-400 uppercase tracking-wider">
          Clients & Loans
        </div>

        <!-- Clients with Submenu -->
        <div class="relative group">
          <button
            @click="sidebarStore.toggleClientsMenu()"
            class="sidebar-item w-full"
            :class="{ 'sidebar-item-active': isClientsActive }"
          >
            <BuildingOfficeIcon class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" />
            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0"
              enter-to-class="opacity-100"
              leave-active-class="transition-all duration-300"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <span v-if="sidebarStore.isOpen || sidebarStore.isMobile" class="flex-1 text-left">Clients</span>
            </Transition>
            <ChevronDownIcon
              v-if="sidebarStore.isOpen || sidebarStore.isMobile"
              class="w-4 h-4 transition-transform duration-200"
              :class="{ 'rotate-180': sidebarStore.clientsMenuOpen }"
            />
          </button>

          <Transition
            enter-active-class="transition-all duration-300 ease-out"
            enter-from-class="opacity-0 max-h-0"
            enter-to-class="opacity-100 max-h-96"
            leave-active-class="transition-all duration-300 ease-in"
            leave-from-class="opacity-100 max-h-96"
            leave-to-class="opacity-0 max-h-0"
          >
            <div v-if="(sidebarStore.isOpen || sidebarStore.isMobile) && sidebarStore.clientsMenuOpen"
                 class="overflow-hidden submenu-container">
              <router-link :to="{ name: 'clients' }" class="submenu-item" :class="{ 'submenu-item-active': ['clients', 'clients-add'].includes($route.name as string) }" @click="handleMenuClick">
                Clients List
              </router-link>
              <!-- <router-link :to="{ name: 'clients-add' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'clients-add' }" @click="handleMenuClick">
                Add Client
              </router-link> -->
              <router-link :to="{ name: 'clients-config' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'clients-config' }" @click="handleMenuClick">
                Configuration
              </router-link>
              <router-link :to="{ name: 'clients-bulk' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'clients-bulk' }" @click="handleMenuClick">
                Bulk SMS
              </router-link>
            </div>
          </Transition>

          <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
               class="absolute left-full ml-2 px-3 py-2 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            <div class="font-medium mb-1">Clients</div>
            <div class="text-xs space-y-1">
              <div>• Clients List</div>
              <div>• Add Client</div>
              <div>• Configuration</div>
              <div>• Bulk SMS</div>
            </div>
          </div>
        </div>





        <!-- Loans with Submenu -->
        <div class="relative group">
          <button
            @click="sidebarStore.toggleLoansMenu()"
            class="sidebar-item w-full"
            :class="{ 'sidebar-item-active': isLoansActive }"
          >
            <CurrencyDollarIcon class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" />
            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0"
              enter-to-class="opacity-100"
              leave-active-class="transition-all duration-300"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <span v-if="sidebarStore.isOpen || sidebarStore.isMobile" class="flex-1 text-left">Loans</span>
            </Transition>
            <ChevronDownIcon 
              v-if="sidebarStore.isOpen || sidebarStore.isMobile"
              class="w-4 h-4 transition-transform duration-200"
              :class="{ 'rotate-180': sidebarStore.loansMenuOpen }"
            />
          </button>

          <!-- Loans Submenu -->
          <Transition
            enter-active-class="transition-all duration-300 ease-out"
            enter-from-class="opacity-0 max-h-0"
            enter-to-class="opacity-100 max-h-96"
            leave-active-class="transition-all duration-300 ease-in"
            leave-from-class="opacity-100 max-h-96"
            leave-to-class="opacity-0 max-h-0"
          >
            <div v-if="(sidebarStore.isOpen || sidebarStore.isMobile) && sidebarStore.loansMenuOpen"
                 class="overflow-hidden submenu-container">
              <router-link :to="{ name: 'requests' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'requests' }" @click="handleMenuClick">
                Loan Requests
              </router-link>
              <router-link :to="{ name: 'limits' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'limits' }" @click="handleMenuClick">
                Loan Limits
              </router-link>
              <router-link :to="{ name: 'check-off' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'check-off' }" @click="handleMenuClick">
                Check-off
              </router-link>
              <router-link :to="{ name: 'loan-accounts' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'loan-accounts' }" @click="handleMenuClick">
                Loan Accounts
              </router-link>
              <router-link :to="{ name: 'loan-products' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'loan-products' }" @click="handleMenuClick">
                Loan Products
              </router-link>
              <router-link :to="{ name: 'loan-repayments' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'loan-repayments' }" @click="handleMenuClick">
                Loan Repayments
              </router-link>
              <router-link :to="{ name: 'merchants' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'merchants' }" @click="handleMenuClick">
                Merchants List
              </router-link>
              <router-link :to="{ name: 'merchants-config' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'merchants-config' }" @click="handleMenuClick">
                Merchants Config
              </router-link>
              <router-link :to="{ name: 'merchants-bulk' }" class="submenu-item" :class="{ 'submenu-item-active': $route.name === 'merchants-bulk' }" @click="handleMenuClick">
                Merchants Bulk SMS
              </router-link>
            </div>
          </Transition>

          <!-- Tooltip for collapsed state -->
          <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
               class="absolute left-full ml-2 px-3 py-2 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            <div class="font-medium mb-1">Loans</div>
            <div class="text-xs space-y-1">
              <div>• Loan Requests</div>
              <div>• Loan Limits</div>
              <div>• Check-off</div>
              <div>• Loan Accounts</div>
              <div>• Loan Products</div>
              <div>• Loan Repayments</div>
              <div>• Merchants List</div>
              <div>• Merchants Config</div>
              <div>• Merchants Bulk SMS</div>
            </div>
          </div>
        </div>

        <!-- Transactions -->
        <div class="relative group">
          <router-link
            :to="{ name: 'transactions' }"
            class="sidebar-item"
            :class="{ 'sidebar-item-active': $route.name === 'transactions' }"
            @click="handleMenuClick"
          >
            <svg class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"/>
            </svg>
            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0"
              enter-to-class="opacity-100"
              leave-active-class="transition-all duration-300"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <span v-if="sidebarStore.isOpen || sidebarStore.isMobile">Transactions</span>
            </Transition>
          </router-link>

          <!-- Tooltip for collapsed state -->
          <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
               class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            Transactions
          </div>
        </div>

        <!-- Customer Search -->
        <div class="relative group">
          <router-link
            :to="{ name: 'customers' }"
            class="sidebar-item"
            :class="{ 'sidebar-item-active': $route.name === 'customers' }"
            @click="handleMenuClick"
          >
            <svg class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 21l-4.35-4.35m-3.257-.293a7.5 7.5 0 111.084-1.09m0 0L21 21m-9-16.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11z"/>
            </svg>
            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0"
              enter-to-class="opacity-100"
              leave-active-class="transition-all duration-300"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <span v-if="sidebarStore.isOpen || sidebarStore.isMobile">Customer Search</span>
            </Transition>
          </router-link>

          <!-- Tooltip for collapsed state -->
          <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
               class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            Customer Search
          </div>
        </div>

        <!-- System with Submenu -->
        <div class="relative group">
          <button
            @click="sidebarStore.toggleSystemMenu()"
            class="sidebar-item w-full"
            :class="{ 'sidebar-item-active': isSystemActive }"
          >
            <svg class="w-5 h-5" :class="{ 'mr-3': sidebarStore.isOpen || sidebarStore.isMobile }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"/>
            </svg>
            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0"
              enter-to-class="opacity-100"
              leave-active-class="transition-all duration-300"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <span v-if="sidebarStore.isOpen || sidebarStore.isMobile" class="flex-1 text-left">System</span>
            </Transition>
            <ChevronDownIcon
              v-if="sidebarStore.isOpen || sidebarStore.isMobile"
              class="w-4 h-4 transition-transform duration-200"
              :class="{ 'rotate-180': sidebarStore.systemMenuOpen }"
            />
          </button>

          <!-- System Submenu -->
          <Transition
            enter-active-class="transition-all duration-300 ease-out"
            enter-from-class="opacity-0 max-h-0"
            enter-to-class="opacity-100 max-h-96"
            leave-active-class="transition-all duration-300 ease-in"
            leave-from-class="opacity-100 max-h-96"
            leave-to-class="opacity-0 max-h-0"
          >
            <div v-if="(sidebarStore.isOpen || sidebarStore.isMobile) && sidebarStore.systemMenuOpen"
                 class="overflow-hidden submenu-container">
              <!-- Temporarily commented out due to template parsing issue -->
              <router-link :to="{ name: 'system-users' }" class="submenu-item" :class="{ 'submenu-item-active': ['system-users', 'add-user', 'edit-user'].includes($route.name as string) }" @click="handleMenuClick">
                Users
              </router-link> 

              <router-link :to="{ name: 'system-roles' }" class="submenu-item" :class="{ 'submenu-item-active': ['system-roles', 'add-role', 'edit-role'].includes($route.name as string) }" @click="handleMenuClick">
                Roles
              </router-link>
              
              <router-link :to="{ name: 'system-permissions' }" class="submenu-item" :class="{ 'submenu-item-active': ['system-permissions', 'add-permission', 'edit-permission'].includes($route.name as string) }" @click="handleMenuClick">
                Permissions
              </router-link>
            </div>
          </Transition>

          <!-- Tooltip for collapsed state -->
          <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
               class="absolute left-full ml-2 px-3 py-2 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            <div class="font-medium mb-1">System</div>
            <div class="text-xs space-y-1">
              <div>• Users</div>
              <div>• Roles</div>
              <div>• Permissions</div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Logout Button -->
    <div class="flex-shrink-0 p-3 border-t border-white/10">
      <div class="relative group">
        <button
          @click="handleLogout"
          :disabled="isLoggingOut"
          class="sidebar-item w-full text-red-300 hover:text-red-200 hover:bg-red-500/20 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ArrowRightOnRectangleIcon
            class="w-5 h-5"
            :class="{
              'mr-3': sidebarStore.isOpen || sidebarStore.isMobile,
              'animate-spin': isLoggingOut
            }"
          />
          <Transition
            enter-active-class="transition-all duration-300"
            enter-from-class="opacity-0"
            enter-to-class="opacity-100"
            leave-active-class="transition-all duration-300"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
          >
            <span v-if="sidebarStore.isOpen || sidebarStore.isMobile">
              {{ isLoggingOut ? 'Signing Out...' : 'Sign Out' }}
            </span>
          </Transition>
        </button>

        <!-- Tooltip for collapsed state -->
        <div v-if="!sidebarStore.isMobile && !sidebarStore.isOpen"
             class="absolute left-full ml-2 bottom-0 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
          Sign Out
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSidebarStore } from '@/stores/sidebar'
import { useAuthStore } from '@/stores/auth'
import {
  HomeIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  ChevronDownIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline'

const route = useRoute()
const router = useRouter()
const sidebarStore = useSidebarStore()
const authStore = useAuthStore()

// Logout state
const isLoggingOut = ref(false)

// Computed properties for active states
const isClientsActive = computed(() => {
  return ['clients', 'clients-add', 'clients-edit', 'clients-config', 'clients-bulk'].includes(route.name as string)
})



const isLoansActive = computed(() => {
  return ['requests', 'limits', 'check-off', 'loan-accounts', 'loan-products', 'loan-repayments', 'merchants', 'merchants-config', 'merchants-bulk'].includes(route.name as string)
})

const isSystemActive = computed(() => {
  return ['system-roles', 'add-role', 'edit-role', 'system-permissions'].includes(route.name as string)
})

// Handle menu click - close sidebar on mobile
const handleMenuClick = () => {
  if (sidebarStore.isMobile && sidebarStore.isOpen) {
    sidebarStore.close()
  }
}

// Handle logout
const handleLogout = async () => {
  if (isLoggingOut.value) return

  try {
    isLoggingOut.value = true
    await authStore.logout()
    // Navigation will be handled by the auth store
  } catch (error) {
    console.error('Logout error:', error)
    // Force navigation to login even if logout fails
    router.push({ name: 'login' })
  } finally {
    isLoggingOut.value = false
  }
}

// Auto-expand menus based on current route
const handleRouteChange = () => {
  sidebarStore.autoExpandMenus(route.name as string)
}

// Handle window resize for mobile detection
const handleResize = () => {
  const isMobile = window.innerWidth < 768
  sidebarStore.setMobile(isMobile)
}

onMounted(() => {
  handleRouteChange()
  handleResize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// Watch for route changes
import { watch } from 'vue'
watch(() => route.name, handleRouteChange)
</script>
