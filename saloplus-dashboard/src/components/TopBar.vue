<template>
  <header class="bg-white border-b border-gray-200 shadow-sm">
    <div class="flex items-center justify-between h-16 px-6">
      <!-- Left side: Hamburger menu and page title -->
      <div class="flex items-center space-x-4">
        <!-- Hamburger <PERSON><PERSON> -->
        <button
          @click="sidebarStore.toggle()"
          class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Toggle sidebar"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>

        <!-- Page Title -->
        <div class="flex items-center space-x-2">
          <h1 class="text-xl font-semibold text-gray-900">{{ pageTitle }}</h1>
          <div v-if="breadcrumbs.length > 1" class="flex items-center space-x-2 text-sm text-gray-500">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
            <span>{{ breadcrumbs.join(' / ') }}</span>
          </div>
        </div>
      </div>

      <!-- Right side: Search, notifications, and user menu -->
      <div class="flex items-center space-x-4">
        <!-- Search Bar -->
        <div class="relative hidden md:block">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search..."
            class="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <!-- Notifications -->
        <button class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 relative">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z"/>
          </svg>
          <!-- Notification badge -->
          <span class="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400"></span>
        </button>

        <!-- User Menu -->
        <div class="relative">
          <button
            @click="showUserMenu = !showUserMenu"
            class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span class="text-white text-sm font-medium">{{ userInitials }}</span>
            </div>
            <div class="hidden md:block text-left">
              <div class="text-sm font-medium text-gray-900">{{ userName }}</div>
              <div class="text-xs text-gray-500">{{ companyName || userRole }}</div>
            </div>
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>

          <!-- User Dropdown Menu -->
          <Transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-if="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
            >
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
              <hr class="my-1">
              <button
                @click="handleLogout"
                :disabled="isLoggingOut"
                class="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ isLoggingOut ? 'Signing out...' : 'Sign out' }}
              </button>
            </div>
          </Transition>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSidebarStore } from '@/stores/sidebar'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const sidebarStore = useSidebarStore()
const authStore = useAuthStore()
const showUserMenu = ref(false)
const isLoggingOut = ref(false)

// Page titles mapping
const pageTitles: Record<string, string> = {
  'dashboard': 'Dashboard',
  'organisations': 'Organizations',
  'organisations-config': 'Organization Configuration',
  'organisations-bulk': 'Organization Bulk SMS',
  'clients': 'Clients',
  'clients-config': 'Client Configuration',
  'clients-bulk': 'Client Bulk SMS',
  'merchants': 'Merchants',
  'merchants-config': 'Merchant Configuration',
  'merchants-bulk': 'Merchant Bulk SMS',
  'requests': 'Loan Requests',
  'limits': 'Loan Limits',
  'check-off': 'Check-off',
  'loan-accounts': 'Loan Accounts',
  'loan-products': 'Loan Products',
  'loan-repayments': 'Loan Repayments',
  'transactions': 'Transactions',
  'withdrawals': 'Withdrawals',
  'bill-payments': 'Bill Payments',
  'bill-payments-add': 'Add Bill Payment',
  'customers': 'Customer Search',
  'system-roles': 'System Roles',
  'system-permissions': 'System Permissions'
}

const pageTitle = computed(() => {
  return pageTitles[route.name as string] || 'SaloPlus Dashboard'
})

// User information from auth store
const userName = computed(() => {
  // Priority: un (display name) > username > name > fallback
  const rawName = authStore.user?.un || authStore.user?.username || authStore.user?.name || 'User'
  // Convert underscores to spaces and capitalize words for better display
  return rawName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
})

const userInitials = computed(() => {
  const name = userName.value
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
})

const userRole = computed(() => {
  return authStore.role || 'User'
})

const companyName = computed(() => {
  return authStore.user?.cn || ''
})

const breadcrumbs = computed(() => {
  const routeName = route.name as string
  const breadcrumbMap: Record<string, string[]> = {
    'organisations-config': ['Organizations', 'Configuration'],
    'organisations-bulk': ['Organizations', 'Bulk SMS'],
    'clients-config': ['Clients', 'Configuration'],
    'clients-bulk': ['Clients', 'Bulk SMS'],
    'merchants-config': ['Merchants', 'Configuration'],
    'merchants-bulk': ['Merchants', 'Bulk SMS'],
    'requests': ['Loans', 'Requests'],
    'limits': ['Loans', 'Limits'],
    'check-off': ['Loans', 'Check-off'],
    'loan-accounts': ['Loans', 'Accounts'],
    'loan-products': ['Loans', 'Products'],
    'loan-repayments': ['Loans', 'Repayments'],
    'bill-payments-add': ['Bill Payments', 'Add Payment'],
    'system-roles': ['System', 'Roles'],
    'system-permissions': ['System', 'Permissions']
  }

  return breadcrumbMap[routeName] || []
})

// Handle logout
const handleLogout = async () => {
  if (isLoggingOut.value) return

  try {
    isLoggingOut.value = true
    showUserMenu.value = false // Close the dropdown
    await authStore.logout()
    // Navigation will be handled by the auth store
  } catch (error) {
    console.error('Logout error:', error)
    // Force navigation to login even if logout fails
    router.push({ name: 'login' })
  } finally {
    isLoggingOut.value = false
  }
}

// Close user menu when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
