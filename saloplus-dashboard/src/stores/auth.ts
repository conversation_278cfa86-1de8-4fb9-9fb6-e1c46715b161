import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
// Router will be imported dynamically to avoid circular dependency
import { authApi } from '@/services/authApi'

// Types
interface User {
  id?: number
  username?: string
  email?: string
  role_id: string
  permissions: Permission[]
  token: string
  mc?: number
  clients?: Client[]
  // New fields from API response
  un: string // User name (display name)
  cn: string // Company name
  cid: string // Company ID
  expires: string // Token expiration
}

interface Permission {
  id: string | number // Can be string from API
  name: string
  description?: string
}

interface Client {
  client_id: string
  client_name: string
  client_account: string
}

interface LoginCredentials {
  username: string
  password: string
  dial_code: string
}

interface LoginWithCodeCredentials extends LoginCredentials {
  verification_code: string
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const permissions = ref<number[]>([])
  const role = ref<number | null>(null)
  const isSuperRole = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Client management
  const selectedClientId = ref<string | null>(localStorage.getItem('selectedClientId'))
  const clientList = ref<Client[]>([])
  const clientMode = ref<string | null>(localStorage.getItem('clientMode'))
  
  // Super roles configuration
  const superRoles = [1, 8]

  // Computed
  const isAuthenticated = computed(() => !!token.value)
  const hasPermission = computed(() => (permissionId: number) =>
    permissions.value.includes(permissionId)
  )
  const isSuperUser = computed(() =>
    role.value ? superRoles.includes(role.value) : false
  )

  // Enhanced permission checking methods
  const hasPermissionByName = (permissionName: string): boolean => {
    if (isSuperUser.value) return true
    // This would need to be enhanced with actual permission name mapping
    // For now, return true for super users and false for others
    return false
  }

  const hasAnyPermission = (permissionIds: number[]): boolean => {
    if (isSuperUser.value) return true
    return permissionIds.some(permissionId => permissions.value.includes(permissionId))
  }

  const hasAllPermissions = (permissionIds: number[]): boolean => {
    if (isSuperUser.value) return true
    return permissionIds.every(permissionId => permissions.value.includes(permissionId))
  }

  const hasRole = (roleId: number): boolean => {
    return role.value === roleId
  }

  const hasAnyRole = (roleIds: number[]): boolean => {
    return role.value ? roleIds.includes(role.value) : false
  }

  // Module-based access checking
  const hasModuleAccess = (modulePermissions: number[]): boolean => {
    if (isSuperUser.value) return true
    return hasAnyPermission(modulePermissions)
  }

  // Actions
  const login = async (credentials: LoginCredentials) => {
    isLoading.value = true
    error.value = null

    try {
      // Encode password
      const payload = {
        ...credentials,
        password: btoa(credentials.password)
      }

      const response = await authApi.login(payload)

      if (response.status === 200) {
        const userData = response.message.data

        // Set user data with proper mapping
        user.value = {
          ...userData,
          username: userData.un, // Map 'un' to 'username' for compatibility
          id: parseInt(userData.cid) // Map company ID to user ID
        }
        token.value = userData.token
        role.value = parseInt(atob(userData.role_id))

        // Extract and set permissions
        const permissionIds = extractPermissionIds(userData.permissions)
        permissions.value = permissionIds

        // Check if super role
        isSuperRole.value = superRoles.includes(role.value)

        // Store in localStorage
        localStorage.setItem('token', userData.token)
        localStorage.setItem('user', JSON.stringify(user.value))
        localStorage.setItem('permissions', JSON.stringify(permissionIds))
        localStorage.setItem('role', role.value.toString())
        
        return { success: true, requiresCode: false, data: userData }
      } else if (response.status === 205 || response.status === 201 || response.status === 410) {
        // Requires verification code
        return { success: false, requiresCode: true, message: response.message }
      } else {
        error.value = response.message
        return { success: false, requiresCode: false, message: response.message }
      }
    } catch (err: any) {
      error.value = err.message || 'Login failed'
      return { success: false, requiresCode: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const loginWithCode = async (credentials: LoginWithCodeCredentials) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await authApi.loginWithCode(credentials)

      if (response.status === 200) {
        const userData = response.message.data

        // Set user data with proper mapping
        user.value = {
          ...userData,
          username: userData.un, // Map 'un' to 'username' for compatibility
          id: parseInt(userData.cid) // Map company ID to user ID
        }
        token.value = userData.token
        role.value = parseInt(atob(userData.role_id))

        // Extract and set permissions
        const permissionIds = extractPermissionIds(userData.permissions)
        permissions.value = permissionIds

        // Check if super role
        isSuperRole.value = superRoles.includes(role.value)

        // Handle multi-client scenario
        if (userData.mc === 1 && userData.clients) {
          clientList.value = userData.clients
          localStorage.setItem('clientList', JSON.stringify(userData.clients))
          return { success: true, requiresClientSelection: true, clients: userData.clients }
        }
        
        // Store in localStorage
        localStorage.setItem('token', userData.token)
        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('permissions', JSON.stringify(permissionIds))
        localStorage.setItem('role', role.value.toString())
        
        return { success: true, requiresClientSelection: false, data: userData }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } catch (err: any) {
      error.value = err.message || 'Login with code failed'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const selectClient = async (clientId: string) => {
    selectedClientId.value = clientId
    localStorage.setItem('selectedClientId', clientId)
    
    const client = clientList.value.find(c => c.client_id === clientId)
    if (client) {
      clientMode.value = client.client_account
      localStorage.setItem('clientMode', client.client_account)
    }
  }

  const forgotPassword = async (username: string) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await authApi.forgotPassword({ username, dial_code: '254' })
      
      if (response.status === 200) {
        return { success: true, message: response.message }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } catch (err: any) {
      error.value = err.message || 'Password reset failed'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    // Clear state
    user.value = null
    token.value = null
    permissions.value = []
    role.value = null
    isSuperRole.value = false
    selectedClientId.value = null
    clientList.value = []
    clientMode.value = null
    error.value = null

    // Clear localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('permissions')
    localStorage.removeItem('role')
    localStorage.removeItem('selectedClientId')
    localStorage.removeItem('clientList')
    localStorage.removeItem('clientMode')

    // Redirect to login using dynamic import
    const { router } = await import('@/router')
    router.push({ name: 'login' })
  }

  const initializeAuth = () => {
    // Restore auth state from localStorage
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')
    const storedPermissions = localStorage.getItem('permissions')
    const storedRole = localStorage.getItem('role')
    const storedClientList = localStorage.getItem('clientList')

    if (storedToken && storedUser) {
      token.value = storedToken
      user.value = JSON.parse(storedUser)
      
      if (storedPermissions) {
        permissions.value = JSON.parse(storedPermissions)
      }
      
      if (storedRole) {
        role.value = parseInt(storedRole)
        isSuperRole.value = superRoles.includes(role.value)
      }
      
      if (storedClientList) {
        clientList.value = JSON.parse(storedClientList)
      }
    }
  }

  // Helper functions
  const extractPermissionIds = (permissionsData: Permission[]): number[] => {
    return permissionsData.map(permission => {
      // Convert string IDs to numbers if needed
      return typeof permission.id === 'string' ? parseInt(permission.id) : permission.id
    })
  }

  const clearError = () => {
    error.value = null
  }

  // Initialize auth state when store is created
  initializeAuth()

  return {
    // State
    user,
    token,
    permissions,
    role,
    isSuperRole,
    isLoading,
    error,
    selectedClientId,
    clientList,
    clientMode,
    superRoles,
    
    // Computed
    isAuthenticated,
    hasPermission,
    isSuperUser,

    // Enhanced permission methods
    hasPermissionByName,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    hasModuleAccess,
    
    // Actions
    login,
    loginWithCode,
    selectClient,
    forgotPassword,
    logout,
    initializeAuth,
    clearError
  }
}, {
  persist: {
    key: 'auth-store',
    storage: localStorage,
    paths: ['token', 'user', 'permissions', 'role', 'selectedClientId', 'clientMode']
  }
})
