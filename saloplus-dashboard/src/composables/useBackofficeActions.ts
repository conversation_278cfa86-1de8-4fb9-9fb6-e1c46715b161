import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

/**
 * Composable for common backoffice actions and utilities
 * Carries over functionality from saloplus_backoffice project
 */
export function useBackofficeActions() {
  const router = useRouter()
  
  // Dropdown state management
  const showDropdown = reactive<Record<number, boolean>>({})
  
  // Loading states
  const loading = ref(false)
  
  // Pagination state
  const currentPage = ref(1)
  const pageSize = ref(10)
  const totalRecords = ref(0)
  const offset = ref(0)
  
  // Search and filter state
  const searchQuery = ref('')
  const searchDropdown = ref(false)
  const searchDropdownPlaceholder = ref('Select Client')
  const searchClient = ref('')
  
  // More parameters for API calls
  const moreParams = reactive({
    client_id: '',
    offset: 0,
    limit: 10,
    status: '',
    search: ''
  })

  /**
   * Format currency with commas
   * @param number - Number to format
   * @returns Formatted currency string
   */
  const formatCurrency = (number: number | string): string => {
    if (!number) return '0'
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
  }

  /**
   * Capitalize first letter of a string
   * @param value - String to capitalize
   * @returns Capitalized string
   */
  const capitalizeFirstLetter = (value: string): string => {
    if (!value) return ''
    return value.charAt(0).toUpperCase() + value.slice(1)
  }

  /**
   * Toggle dropdown visibility for a specific index
   * @param index - Index of the dropdown to toggle
   */
  const toggleDropdown = (index: number): void => {
    // Close all other dropdowns first
    Object.keys(showDropdown).forEach(key => {
      const keyNum = parseInt(key)
      showDropdown[keyNum] = keyNum === index ? !showDropdown[keyNum] : false
    })
  }

  /**
   * Close all dropdowns
   */
  const closeDropdown = (): void => {
    Object.keys(showDropdown).forEach(key => {
      showDropdown[parseInt(key)] = false
    })
  }

  /**
   * Handle pagination - go to specific page
   * @param page - Page number to navigate to
   * @param callback - Optional callback function to execute after page change
   */
  const goToPage = (page: number, callback?: () => void): void => {
    currentPage.value = page
    offset.value = page
    moreParams.offset = page
    if (callback) {
      callback()
    }
  }

  /**
   * Set client ID and update search dropdown
   * @param item - Client item with value and text properties
   * @param callback - Optional callback function to execute after setting client
   */
  const setClientId = (item: { value: string; text: string }, callback?: () => void): void => {
    moreParams.client_id = item.value
    searchDropdownPlaceholder.value = item.text
    searchDropdown.value = false
    searchClient.value = ""
    if (callback) {
      callback()
    }
  }

  /**
   * Toggle search dropdown visibility
   */
  const toggleSearchDropdown = (): void => {
    searchDropdown.value = !searchDropdown.value
  }

  /**
   * Filter data by status
   * @param data - Array of data to filter
   * @param selectedStatus - Status to filter by
   * @param statusField - Field name for status (default: 'status')
   * @returns Filtered data array
   */
  const filterByStatus = <T extends Record<string, any>>(
    data: T[], 
    selectedStatus: string | number, 
    statusField: string = 'status'
  ): T[] => {
    if (selectedStatus === "All" || selectedStatus === "") {
      return data
    }
    return data.filter(item => parseInt(item[statusField]) === parseInt(selectedStatus.toString()))
  }

  /**
   * Handle row edit action
   * @param row - Row data to edit
   * @param routeName - Route name to navigate to
   * @param fillAction - Optional action to fill data before navigation
   */
  const editRow = async (
    row: any, 
    routeName: string, 
    fillAction?: (row: any) => Promise<void>
  ): Promise<void> => {
    closeDropdown()
    if (fillAction) {
      await fillAction(row)
    }
    await router.push({ name: routeName })
  }

  /**
   * Handle view action for related data
   * @param item - Item to view
   * @param routeName - Route name to navigate to
   * @param fillAction - Optional action to fill data before navigation
   * @param params - Optional route parameters
   */
  const viewRelatedData = async (
    item: any,
    routeName: string,
    fillAction?: (item: any) => Promise<void>,
    params?: Record<string, any>
  ): Promise<void> => {
    if (fillAction) {
      await fillAction(item)
    }
    const routeParams = params || { client_id: item.client_id }
    await router.push({ name: routeName, params: routeParams })
  }

  /**
   * Reset pagination and filters
   */
  const resetFilters = (): void => {
    currentPage.value = 1
    offset.value = 0
    searchQuery.value = ''
    searchClient.value = ''
    moreParams.client_id = ''
    moreParams.offset = 0
    moreParams.search = ''
    moreParams.status = ''
  }

  /**
   * Update pagination parameters
   * @param page - Page number
   * @param limit - Items per page
   */
  const updatePagination = (page: number, limit: number = 10): void => {
    currentPage.value = page
    pageSize.value = limit
    offset.value = page
    moreParams.offset = page
    moreParams.limit = limit
  }

  /**
   * Handle search input
   * @param query - Search query
   * @param callback - Optional callback to execute search
   */
  const handleSearch = (query: string, callback?: () => void): void => {
    searchQuery.value = query
    moreParams.search = query
    currentPage.value = 1
    offset.value = 0
    moreParams.offset = 0
    if (callback) {
      callback()
    }
  }

  /**
   * Format date to readable format
   * @param dateString - Date string to format
   * @returns Formatted date string
   */
  const formatDate = (dateString: string): string => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  /**
   * Format time to readable format
   * @param timeString - Time string to format
   * @returns Formatted time string
   */
  const formatTime = (timeString: string): string => {
    if (!timeString) return ''
    const time = new Date(`1970-01-01T${timeString}`)
    return time.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  return {
    // State
    showDropdown,
    loading,
    currentPage,
    pageSize,
    totalRecords,
    offset,
    searchQuery,
    searchDropdown,
    searchDropdownPlaceholder,
    searchClient,
    moreParams,

    // Methods
    formatCurrency,
    capitalizeFirstLetter,
    toggleDropdown,
    closeDropdown,
    goToPage,
    setClientId,
    toggleSearchDropdown,
    filterByStatus,
    editRow,
    viewRelatedData,
    resetFilters,
    updatePagination,
    handleSearch,
    formatDate,
    formatTime
  }
}
