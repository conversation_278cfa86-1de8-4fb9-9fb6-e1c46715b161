import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { 
  PERMISSIONS, 
  ROLES, 
  PERMISSION_MODULES,
  getPermissionsByModule,
  getPermissionById,
  getPermissionByName,
  getRoleById,
  hasPermission as checkPermission,
  hasAnyPermission as checkAnyPermission,
  hasAllPermissions as checkAllPermissions,
  hasModuleAccess as checkModuleAccess,
  type PermissionId,
  type RoleId,
  type ModuleName
} from '@/config/permissions'

/**
 * Permission management composable
 * Provides reactive permission checking and role-based access control
 */
export function usePermissions() {
  const authStore = useAuthStore()

  // Reactive computed properties
  const userPermissions = computed(() => authStore.permissions || [])
  const userRole = computed(() => authStore.role)
  const isSuperUser = computed(() => authStore.isSuperUser)

  /**
   * Check if user has a specific permission by ID
   */
  const hasPermission = (permissionId: PermissionId): boolean => {
    if (isSuperUser.value) return true
    return checkPermission(userPermissions.value, permissionId)
  }

  /**
   * Check if user has a specific permission by name
   */
  const hasPermissionByName = (permissionName: string): boolean => {
    if (isSuperUser.value) return true
    const permission = getPermissionByName(permissionName)
    return permission ? hasPermission(permission.id as PermissionId) : false
  }

  /**
   * Check if user has any of the specified permissions
   */
  const hasAnyPermission = (permissionIds: PermissionId[]): boolean => {
    if (isSuperUser.value) return true
    return checkAnyPermission(userPermissions.value, permissionIds)
  }

  /**
   * Check if user has all of the specified permissions
   */
  const hasAllPermissions = (permissionIds: PermissionId[]): boolean => {
    if (isSuperUser.value) return true
    return checkAllPermissions(userPermissions.value, permissionIds)
  }

  /**
   * Check if user has access to a specific module
   */
  const hasModuleAccess = (module: ModuleName): boolean => {
    if (isSuperUser.value) return true
    return checkModuleAccess(userPermissions.value, module)
  }

  /**
   * Check if user has a specific role
   */
  const hasRole = (roleId: RoleId): boolean => {
    return userRole.value === roleId
  }

  /**
   * Check if user has any of the specified roles
   */
  const hasAnyRole = (roleIds: RoleId[]): boolean => {
    return userRole.value ? roleIds.includes(userRole.value as RoleId) : false
  }

  /**
   * Get user's role information
   */
  const getUserRole = () => {
    return userRole.value ? getRoleById(userRole.value) : null
  }

  /**
   * Get permissions for a specific module
   */
  const getModulePermissions = (module: ModuleName) => {
    return getPermissionsByModule(module)
  }

  /**
   * Check if user can perform specific actions
   */
  const canView = (resource: string): boolean => {
    const viewPermissions: Record<string, PermissionId> = {
      dashboard: PERMISSIONS.DASHBOARD_VIEW.id as PermissionId,
      clients: PERMISSIONS.CLIENTS_VIEW.id as PermissionId,
      loans: PERMISSIONS.LOANS_VIEW.id as PermissionId,
      'loan-requests': PERMISSIONS.LOANS_REQUESTS_VIEW.id as PermissionId,
      'loan-limits': PERMISSIONS.LOANS_LIMITS_VIEW.id as PermissionId,
      'loan-accounts': PERMISSIONS.LOANS_ACCOUNTS_VIEW.id as PermissionId,
      'loan-repayments': PERMISSIONS.LOANS_REPAYMENTS_VIEW.id as PermissionId,
      'loan-products': PERMISSIONS.LOANS_PRODUCTS_VIEW.id as PermissionId,
      transactions: PERMISSIONS.TRANSACTIONS_VIEW.id as PermissionId,
      reports: PERMISSIONS.REPORTS_VIEW.id as PermissionId,
      'system-users': PERMISSIONS.SYSTEM_USERS_VIEW.id as PermissionId,
      'system-roles': PERMISSIONS.SYSTEM_ROLES_VIEW.id as PermissionId,
      settings: PERMISSIONS.SETTINGS_VIEW.id as PermissionId
    }
    
    const permissionId = viewPermissions[resource]
    return permissionId ? hasPermission(permissionId) : false
  }

  const canCreate = (resource: string): boolean => {
    const createPermissions: Record<string, PermissionId> = {
      clients: PERMISSIONS.CLIENTS_CREATE.id as PermissionId,
      'system-users': PERMISSIONS.SYSTEM_USERS_CREATE.id as PermissionId,
      'system-roles': PERMISSIONS.SYSTEM_ROLES_CREATE.id as PermissionId
    }
    
    const permissionId = createPermissions[resource]
    return permissionId ? hasPermission(permissionId) : false
  }

  const canEdit = (resource: string): boolean => {
    const editPermissions: Record<string, PermissionId> = {
      clients: PERMISSIONS.CLIENTS_EDIT.id as PermissionId,
      'loan-limits': PERMISSIONS.LOANS_LIMITS_MANAGE.id as PermissionId,
      'loan-accounts': PERMISSIONS.LOANS_ACCOUNTS_MANAGE.id as PermissionId,
      'loan-products': PERMISSIONS.LOANS_PRODUCTS_MANAGE.id as PermissionId,
      'system-users': PERMISSIONS.SYSTEM_USERS_EDIT.id as PermissionId,
      'system-roles': PERMISSIONS.SYSTEM_ROLES_EDIT.id as PermissionId,
      settings: PERMISSIONS.SETTINGS_EDIT.id as PermissionId
    }
    
    const permissionId = editPermissions[resource]
    return permissionId ? hasPermission(permissionId) : false
  }

  const canDelete = (resource: string): boolean => {
    const deletePermissions: Record<string, PermissionId> = {
      clients: PERMISSIONS.CLIENTS_DELETE.id as PermissionId,
      'system-users': PERMISSIONS.SYSTEM_USERS_DELETE.id as PermissionId,
      'system-roles': PERMISSIONS.SYSTEM_ROLES_DELETE.id as PermissionId
    }
    
    const permissionId = deletePermissions[resource]
    return permissionId ? hasPermission(permissionId) : false
  }

  const canApprove = (resource: string): boolean => {
    const approvePermissions: Record<string, PermissionId> = {
      'loan-requests': PERMISSIONS.LOANS_REQUESTS_APPROVE.id as PermissionId,
      'loan-repayments': PERMISSIONS.LOANS_REPAYMENTS_VERIFY.id as PermissionId
    }
    
    const permissionId = approvePermissions[resource]
    return permissionId ? hasPermission(permissionId) : false
  }

  const canExport = (resource: string): boolean => {
    const exportPermissions: Record<string, PermissionId> = {
      clients: PERMISSIONS.CLIENTS_EXPORT.id as PermissionId,
      transactions: PERMISSIONS.TRANSACTIONS_EXPORT.id as PermissionId,
      reports: PERMISSIONS.REPORTS_EXPORT.id as PermissionId
    }
    
    const permissionId = exportPermissions[resource]
    return permissionId ? hasPermission(permissionId) : false
  }

  /**
   * Check route access permissions
   */
  const canAccessRoute = (routePath: string): boolean => {
    if (isSuperUser.value) return true
    
    // Define route permission mappings
    const routePermissions: Record<string, PermissionId[]> = {
      '/dashboard': [PERMISSIONS.DASHBOARD_VIEW.id as PermissionId],
      '/clients': [PERMISSIONS.CLIENTS_VIEW.id as PermissionId],
      '/loans': [PERMISSIONS.LOANS_VIEW.id as PermissionId],
      '/loans/requests': [PERMISSIONS.LOANS_REQUESTS_VIEW.id as PermissionId],
      '/loans/limits': [PERMISSIONS.LOANS_LIMITS_VIEW.id as PermissionId],
      '/loans/accounts': [PERMISSIONS.LOANS_ACCOUNTS_VIEW.id as PermissionId],
      '/loans/repayments': [PERMISSIONS.LOANS_REPAYMENTS_VIEW.id as PermissionId],
      '/loans/products': [PERMISSIONS.LOANS_PRODUCTS_VIEW.id as PermissionId],
      '/transactions': [PERMISSIONS.TRANSACTIONS_VIEW.id as PermissionId],
      '/reports': [PERMISSIONS.REPORTS_VIEW.id as PermissionId],
      '/system/users': [PERMISSIONS.SYSTEM_USERS_VIEW.id as PermissionId],
      '/system/roles': [PERMISSIONS.SYSTEM_ROLES_VIEW.id as PermissionId],
      '/settings': [PERMISSIONS.SETTINGS_VIEW.id as PermissionId]
    }
    
    const requiredPermissions = routePermissions[routePath]
    return requiredPermissions ? hasAnyPermission(requiredPermissions) : true
  }

  /**
   * Get user's permission summary
   */
  const getPermissionSummary = () => {
    const role = getUserRole()
    const moduleAccess = Object.values(PERMISSION_MODULES).reduce((acc, module) => {
      acc[module] = hasModuleAccess(module)
      return acc
    }, {} as Record<string, boolean>)

    return {
      role: role?.name || 'Unknown',
      isSuperUser: isSuperUser.value,
      totalPermissions: userPermissions.value.length,
      moduleAccess
    }
  }

  return {
    // Permission constants
    PERMISSIONS,
    ROLES,
    PERMISSION_MODULES,
    
    // State
    userPermissions,
    userRole,
    isSuperUser,
    
    // Permission checking
    hasPermission,
    hasPermissionByName,
    hasAnyPermission,
    hasAllPermissions,
    hasModuleAccess,
    hasRole,
    hasAnyRole,
    
    // Action-based permissions
    canView,
    canCreate,
    canEdit,
    canDelete,
    canApprove,
    canExport,
    canAccessRoute,
    
    // Utility functions
    getUserRole,
    getModulePermissions,
    getPermissionSummary
  }
}
